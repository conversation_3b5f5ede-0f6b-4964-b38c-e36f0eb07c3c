import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Turbopack configuration (stable in Next.js 15)
  turbopack: {
    rules: {
      // Optimize font loading for Turbopack
      '*.woff2': {
        loaders: ['file-loader'],
        as: '*.woff2',
      },
    },
  },

  // Ensure proper static file serving
  assetPrefix: process.env.NODE_ENV === 'production' ? undefined : '',

  // Improve development experience
  devIndicators: {
    position: 'bottom-right',
  },
};

export default nextConfig;
