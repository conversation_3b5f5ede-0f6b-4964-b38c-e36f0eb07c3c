{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/%28auth%29/layout.tsx"], "sourcesContent": ["// Auth layout for authentication pages\n\nexport default function AuthLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;AAExB,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,iRAAC;QAAI,WAAU;kBACZ;;;;;;AAGP", "debugId": null}}]}