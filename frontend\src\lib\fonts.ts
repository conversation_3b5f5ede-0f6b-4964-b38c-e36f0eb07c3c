// Font configuration with Turbopack compatibility
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";

// Configure Geist Sans with optimizations
export const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
  fallback: [
    "system-ui",
    "-apple-system", 
    "BlinkMacSystemFont",
    "Segoe UI",
    "Roboto",
    "sans-serif"
  ],
  adjustFontFallback: true,
  preload: true,
});

// Configure Geist Mono with optimizations  
export const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
  fallback: [
    "ui-monospace",
    "SFMono-Regular",
    "SF Mono", 
    "Consolas",
    "Liberation Mono",
    "Menlo",
    "monospace"
  ],
  adjustFontFallback: true,
  preload: true,
});

// Font class names for easy usage
export const fontClassNames = `${geistSans.variable} ${geistMono.variable}`;

// CSS variables for direct usage
export const fontVariables = {
  sans: geistSans.style.fontFamily,
  mono: geistMono.style.fontFamily,
} as const;
