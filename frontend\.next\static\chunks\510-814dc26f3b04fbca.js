"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[510],{1275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(2115),o=n(2712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},4315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(2115);n(5155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},4678:(e,t,n)=>{n.d(t,{UC:()=>re,YJ:()=>rn,In:()=>n8,q7:()=>ro,VF:()=>rl,p4:()=>ri,JU:()=>rr,ZL:()=>n4,bL:()=>n7,wn:()=>ru,PP:()=>ra,wv:()=>rs,l9:()=>n3,WT:()=>n9,LM:()=>rt});var r,o,i,l=n(2115),a=n(7650),u=n(9367),s=n(5185),c=n(7328),d=n(6101),f=n(6081),p=n(4315),h=n(3655),v=n(9033),m=n(5155),g="dismissableLayer.update",y=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=l.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:c,onInteractOutside:f,onDismiss:p,...w}=e,E=l.useContext(y),[S,C]=l.useState(null),R=null!=(r=null==S?void 0:S.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,A]=l.useState({}),T=(0,d.s)(t,e=>C(e)),k=Array.from(E.layers),[L]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),P=k.indexOf(L),N=S?k.indexOf(S):-1,M=E.layersWithOutsidePointerEventsDisabled.size>0,O=N>=P,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,v.c)(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){b("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));O&&!n&&(null==u||u(e),null==f||f(e),e.defaultPrevented||null==p||p())},R),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,v.c)(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&b("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==c||c(e),null==f||f(e),e.defaultPrevented||null==p||p())},R);return!function(e,t=globalThis?.document){let n=(0,v.c)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===E.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},R),l.useEffect(()=>{if(S)return i&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(o=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),x(),()=>{i&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=o)}},[S,R,i,E]),l.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),x())},[S,E]),l.useEffect(()=>{let e=()=>A({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,m.jsx)(h.sG.div,{...w,ref:T,style:{pointerEvents:M?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.mK)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,s.mK)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,s.mK)(e.onPointerDownCapture,j.onPointerDownCapture)})});function x(){let e=new CustomEvent(g);document.dispatchEvent(e)}function b(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,h.hO)(i,l):i.dispatchEvent(l)}w.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(y),r=l.useRef(null),o=(0,d.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,m.jsx)(h.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var E=0;function S(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var C="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",A={bubbles:!1,cancelable:!0},T=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[u,s]=l.useState(null),c=(0,v.c)(o),f=(0,v.c)(i),p=l.useRef(null),g=(0,d.s)(t,e=>s(e)),y=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(y.paused||!u)return;let t=e.target;u.contains(t)?p.current=t:P(p.current,{select:!0})},t=function(e){if(y.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||P(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&P(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,y.paused]),l.useEffect(()=>{if(u){N.add(y);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(C,A);u.addEventListener(C,c),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(P(r,{select:t}),document.activeElement!==n)return}(k(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&P(u))}return()=>{u.removeEventListener(C,c),setTimeout(()=>{let t=new CustomEvent(R,A);u.addEventListener(R,f),u.dispatchEvent(t),t.defaultPrevented||P(null!=e?e:document.body,{select:!0}),u.removeEventListener(R,f),N.remove(y)},0)}}},[u,c,f,y]);let w=l.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=k(e);return[L(t,e),L(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&P(i,{select:!0})):(e.preventDefault(),n&&P(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,m.jsx)(h.sG.div,{tabIndex:-1,...a,ref:g,onKeyDown:w})});function k(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function L(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function P(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}T.displayName="FocusScope";var N=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=M(e,t)).unshift(t)},remove(t){var n;null==(n=(e=M(e,t))[0])||n.resume()}}}();function M(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var O=n(1285);let j=["top","right","bottom","left"],D=Math.min,I=Math.max,W=Math.round,F=Math.floor,H=e=>({x:e,y:e}),B={left:"right",right:"left",bottom:"top",top:"bottom"},K={start:"end",end:"start"};function z(e,t){return"function"==typeof e?e(t):e}function _(e){return e.split("-")[0]}function V(e){return e.split("-")[1]}function G(e){return"x"===e?"y":"x"}function X(e){return"y"===e?"height":"width"}let Y=new Set(["top","bottom"]);function q(e){return Y.has(_(e))?"y":"x"}function U(e){return e.replace(/start|end/g,e=>K[e])}let Z=["left","right"],$=["right","left"],J=["top","bottom"],Q=["bottom","top"];function ee(e){return e.replace(/left|right|bottom|top/g,e=>B[e])}function et(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function en(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function er(e,t,n){let r,{reference:o,floating:i}=e,l=q(t),a=G(q(t)),u=X(a),s=_(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(s){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(V(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let eo=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=er(s,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:m,y:g,data:y,reset:w}=await v({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=m?m:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=er(s,f,u)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function ei(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=z(t,e),h=et(p),v=a[f?"floating"===d?"reference":"floating":d],m=en(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=en(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-x.top+h.top)/w.y,bottom:(x.bottom-m.bottom+h.bottom)/w.y,left:(m.left-x.left+h.left)/w.x,right:(x.right-m.right+h.right)/w.x}}function el(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ea(e){return j.some(t=>e[t]>=0)}let eu=new Set(["left","top"]);async function es(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=_(n),a=V(n),u="y"===q(n),s=eu.has(l)?-1:1,c=i&&u?-1:1,d=z(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*c,y:f*s}:{x:f*s,y:p*c}}function ec(){return"undefined"!=typeof window}function ed(e){return eh(e)?(e.nodeName||"").toLowerCase():"#document"}function ef(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ep(e){var t;return null==(t=(eh(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eh(e){return!!ec()&&(e instanceof Node||e instanceof ef(e).Node)}function ev(e){return!!ec()&&(e instanceof Element||e instanceof ef(e).Element)}function em(e){return!!ec()&&(e instanceof HTMLElement||e instanceof ef(e).HTMLElement)}function eg(e){return!!ec()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ef(e).ShadowRoot)}let ey=new Set(["inline","contents"]);function ew(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eP(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ey.has(o)}let ex=new Set(["table","td","th"]),eb=[":popover-open",":modal"];function eE(e){return eb.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eS=["transform","translate","scale","rotate","perspective"],eC=["transform","translate","scale","rotate","perspective","filter"],eR=["paint","layout","strict","content"];function eA(e){let t=eT(),n=ev(e)?eP(e):e;return eS.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eC.some(e=>(n.willChange||"").includes(e))||eR.some(e=>(n.contain||"").includes(e))}function eT(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ek=new Set(["html","body","#document"]);function eL(e){return ek.has(ed(e))}function eP(e){return ef(e).getComputedStyle(e)}function eN(e){return ev(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eM(e){if("html"===ed(e))return e;let t=e.assignedSlot||e.parentNode||eg(e)&&e.host||ep(e);return eg(t)?t.host:t}function eO(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eM(t);return eL(n)?t.ownerDocument?t.ownerDocument.body:t.body:em(n)&&ew(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=ef(o);if(i){let e=ej(l);return t.concat(l,l.visualViewport||[],ew(o)?o:[],e&&n?eO(e):[])}return t.concat(o,eO(o,[],n))}function ej(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eD(e){let t=eP(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=em(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=W(n)!==i||W(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eI(e){return ev(e)?e:e.contextElement}function eW(e){let t=eI(e);if(!em(t))return H(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eD(t),l=(i?W(n.width):n.width)/r,a=(i?W(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eF=H(0);function eH(e){let t=ef(e);return eT()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eF}function eB(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eI(e),a=H(1);t&&(r?ev(r)&&(a=eW(r)):a=eW(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ef(l))&&o)?eH(l):H(0),s=(i.left+u.x)/a.x,c=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=ef(l),t=r&&ev(r)?ef(r):r,n=e,o=ej(n);for(;o&&r&&t!==n;){let e=eW(o),t=o.getBoundingClientRect(),r=eP(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,c*=e.y,d*=e.x,f*=e.y,s+=i,c+=l,o=ej(n=ef(o))}}return en({width:d,height:f,x:s,y:c})}function eK(e,t){let n=eN(e).scrollLeft;return t?t.left+n:eB(ep(e)).left+n}function ez(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eK(e,r)),y:r.top+t.scrollTop}}let e_=new Set(["absolute","fixed"]);function eV(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ef(e),r=ep(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eT();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=ep(e),n=eN(e),r=e.ownerDocument.body,o=I(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=I(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eK(e),a=-n.scrollTop;return"rtl"===eP(r).direction&&(l+=I(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(ep(e));else if(ev(t))r=function(e,t){let n=eB(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=em(e)?eW(e):H(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=eH(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return en(r)}function eG(e){return"static"===eP(e).position}function eX(e,t){if(!em(e)||"fixed"===eP(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ep(e)===n&&(n=n.ownerDocument.body),n}function eY(e,t){var n;let r=ef(e);if(eE(e))return r;if(!em(e)){let t=eM(e);for(;t&&!eL(t);){if(ev(t)&&!eG(t))return t;t=eM(t)}return r}let o=eX(e,t);for(;o&&(n=o,ex.has(ed(n)))&&eG(o);)o=eX(o,t);return o&&eL(o)&&eG(o)&&!eA(o)?r:o||function(e){let t=eM(e);for(;em(t)&&!eL(t);){if(eA(t))return t;if(eE(t))break;t=eM(t)}return null}(e)||r}let eq=async function(e){let t=this.getOffsetParent||eY,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=em(t),o=ep(t),i="fixed"===n,l=eB(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=H(0);if(r||!r&&!i)if(("body"!==ed(t)||ew(o))&&(a=eN(t)),r){let e=eB(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eK(o));i&&!r&&o&&(u.x=eK(o));let s=!o||r||i?H(0):ez(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eU={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=ep(r),a=!!t&&eE(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=H(1),c=H(0),d=em(r);if((d||!d&&!i)&&(("body"!==ed(r)||ew(l))&&(u=eN(r)),em(r))){let e=eB(r);s=eW(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!l||d||i?H(0):ez(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+f.x,y:n.y*s.y-u.scrollTop*s.y+c.y+f.y}},getDocumentElement:ep,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eE(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eO(e,[],!1).filter(e=>ev(e)&&"body"!==ed(e)),o=null,i="fixed"===eP(e).position,l=i?eM(e):e;for(;ev(l)&&!eL(l);){let t=eP(l),n=eA(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&e_.has(o.position)||ew(l)&&!n&&function e(t,n){let r=eM(t);return!(r===n||!ev(r)||eL(r))&&("fixed"===eP(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eM(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eV(t,n,o);return e.top=I(r.top,e.top),e.right=D(r.right,e.right),e.bottom=D(r.bottom,e.bottom),e.left=I(r.left,e.left),e},eV(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eY,getElementRects:eq,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eD(e);return{width:t,height:n}},getScale:eW,isElement:ev,isRTL:function(e){return"rtl"===eP(e).direction}};function eZ(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e$=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:s,padding:c=0}=z(e,t)||{};if(null==s)return{};let d=et(c),f={x:n,y:r},p=G(q(o)),h=X(p),v=await l.getDimensions(s),m="y"===p,g=m?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-f[p]-i.floating[h],w=f[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s)),b=x?x[g]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[g]||i.floating[h]);let E=b/2-v[h]/2-1,S=D(d[m?"top":"left"],E),C=D(d[m?"bottom":"right"],E),R=b-v[h]-C,A=b/2-v[h]/2+(y/2-w/2),T=I(S,D(A,R)),k=!u.arrow&&null!=V(o)&&A!==T&&i.reference[h]/2-(A<S?S:C)-v[h]/2<0,L=k?A<S?A-S:A-R:0;return{[p]:f[p]+L,data:{[p]:T,centerOffset:A-T-L,...k&&{alignmentOffset:L}},reset:k}}});var eJ="undefined"!=typeof document?l.useLayoutEffect:function(){};function eQ(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eQ(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eQ(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e0(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e1(e,t){let n=e0(e);return Math.round(t*n)/n}function e2(e){let t=l.useRef(e);return eJ(()=>{t.current=e}),t}var e5=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,m.jsx)(h.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,m.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e5.displayName="Arrow";var e6=n(2712),e7=n(1275),e3="Popper",[e9,e8]=(0,f.A)(e3),[e4,te]=e9(e3),tt=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,m.jsx)(e4,{scope:t,anchor:r,onAnchorChange:o,children:n})};tt.displayName=e3;var tn="PopperAnchor",tr=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=te(tn,n),a=l.useRef(null),u=(0,d.s)(t,a),s=l.useRef(null);return l.useEffect(()=>{let e=s.current;s.current=(null==r?void 0:r.current)||a.current,e!==s.current&&i.onAnchorChange(s.current)}),r?null:(0,m.jsx)(h.sG.div,{...o,ref:u})});tr.displayName=tn;var to="PopperContent",[ti,tl]=e9(to),ta=l.forwardRef((e,t)=>{var n,r,o,i,u,s,c,f;let{__scopePopper:p,side:g="bottom",sideOffset:y=0,align:w="center",alignOffset:x=0,arrowPadding:b=0,avoidCollisions:E=!0,collisionBoundary:S=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:T="optimized",onPlaced:k,...L}=e,P=te(to,p),[N,M]=l.useState(null),O=(0,d.s)(t,e=>M(e)),[j,W]=l.useState(null),H=(0,e7.X)(j),B=null!=(c=null==H?void 0:H.width)?c:0,K=null!=(f=null==H?void 0:H.height)?f:0,Y="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},et=Array.isArray(S)?S:[S],en=et.length>0,er={padding:Y,boundary:et.filter(td),altBoundary:en},{refs:ec,floatingStyles:ed,placement:ef,isPositioned:eh,middlewareData:ev}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:u}={},transform:s=!0,whileElementsMounted:c,open:d}=e,[f,p]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,v]=l.useState(r);eQ(h,r)||v(r);let[m,g]=l.useState(null),[y,w]=l.useState(null),x=l.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=l.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||m,S=u||y,C=l.useRef(null),R=l.useRef(null),A=l.useRef(f),T=null!=c,k=e2(c),L=e2(o),P=e2(d),N=l.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};L.current&&(e.platform=L.current),((e,t,n)=>{let r=new Map,o={platform:eU,...n},i={...o.platform,_c:r};return eo(e,t,{...o,platform:i})})(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};M.current&&!eQ(A.current,t)&&(A.current=t,a.flushSync(()=>{p(t)}))})},[h,t,n,L,P]);eJ(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let M=l.useRef(!1);eJ(()=>(M.current=!0,()=>{M.current=!1}),[]),eJ(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(k.current)return k.current(E,S,N);N()}},[E,S,N,k,T]);let O=l.useMemo(()=>({reference:C,floating:R,setReference:x,setFloating:b}),[x,b]),j=l.useMemo(()=>({reference:E,floating:S}),[E,S]),D=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=e1(j.floating,f.x),r=e1(j.floating,f.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...e0(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,j.floating,f.x,f.y]);return l.useMemo(()=>({...f,update:N,refs:O,elements:j,floatingStyles:D}),[f,N,O,j,D])}({strategy:"fixed",placement:g+("center"!==w?"-"+w:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,c=eI(e),d=i||l?[...c?eO(c):[],...eO(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=c&&u?function(e,t){let n,r=null,o=ep(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let s=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=s;if(a||t(),!f||!p)return;let h=F(d),v=F(o.clientWidth-(c+f)),m={rootMargin:-h+"px "+-v+"px "+-F(o.clientHeight-(d+p))+"px "+-F(c)+"px",threshold:I(0,D(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eZ(s,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,m)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!s&&h.observe(c),h.observe(t));let v=s?eB(e):null;return s&&function t(){let r=eB(e);v&&!eZ(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,s&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===T})},elements:{reference:P.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await es(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}))({mainAxis:y+K,alignmentAxis:x}),E&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=z(e,t),s={x:n,y:r},c=await ei(t,u),d=q(_(o)),f=G(d),p=s[f],h=s[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=I(n,D(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=I(n,D(h,r))}let v=a.fn({...t,[f]:p,[d]:h});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=z(e,t),c={x:n,y:r},d=q(o),f=G(d),p=c[f],h=c[d],v=z(a,t),m="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(s){var g,y;let e="y"===f?"width":"height",t=eu.has(_(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}))():void 0,...er}),E&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=z(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=_(a),b=q(c),E=_(c)===c,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=v||(E||!y?[ee(c)]:function(e){let t=ee(e);return[U(e),t,U(t)]}(c)),R="none"!==g;!v&&R&&C.push(...function(e,t,n,r){let o=V(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?$:Z;return t?Z:$;case"left":case"right":return t?J:Q;default:return[]}}(_(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(U)))),i}(c,y,g,S));let A=[c,...C],T=await ei(t,w),k=[],L=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&k.push(T[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=V(e),o=G(q(e)),i=X(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ee(l)),[l,ee(l)]}(a,s,S);k.push(T[e[0]],T[e[1]])}if(L=[...L,{placement:a,overflows:k}],!k.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=A[e];if(t&&("alignment"!==h||b===q(t)||L.every(e=>q(e.placement)!==b||e.overflows[0]>0)))return{data:{index:e,overflows:L},reset:{placement:t}};let n=null==(i=L.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=L.filter(e=>{if(R){let t=q(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...er}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:u,elements:s}=t,{apply:c=()=>{},...d}=z(e,t),f=await ei(t,d),p=_(l),h=V(l),v="y"===q(l),{width:m,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-f.top-f.bottom,w=m-f.left-f.right,x=D(g-f[o],y),b=D(m-f[i],w),E=!t.middlewareData.shift,S=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!h){let e=I(f.left,0),t=I(f.right,0),n=I(f.top,0),r=I(f.bottom,0);v?C=m-2*(0!==e||0!==t?e+t:I(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:I(f.top,f.bottom))}await c({...t,availableWidth:C,availableHeight:S});let R=await u.getDimensions(s.floating);return m!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...er,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e$({element:n.current,padding:r}).fn(t):{}:n?e$({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:j,padding:b}),tf({arrowWidth:B,arrowHeight:K}),A&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=z(e,t);switch(r){case"referenceHidden":{let e=el(await ei(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ea(e)}}}case"escaped":{let e=el(await ei(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ea(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...er})]}),[em,eg]=tp(ef),ey=(0,v.c)(k);(0,e6.N)(()=>{eh&&(null==ey||ey())},[eh,ey]);let ew=null==(n=ev.arrow)?void 0:n.x,ex=null==(r=ev.arrow)?void 0:r.y,eb=(null==(o=ev.arrow)?void 0:o.centerOffset)!==0,[eE,eS]=l.useState();return(0,e6.N)(()=>{N&&eS(window.getComputedStyle(N).zIndex)},[N]),(0,m.jsx)("div",{ref:ec.setFloating,"data-radix-popper-content-wrapper":"",style:{...ed,transform:eh?ed.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eE,"--radix-popper-transform-origin":[null==(i=ev.transformOrigin)?void 0:i.x,null==(u=ev.transformOrigin)?void 0:u.y].join(" "),...(null==(s=ev.hide)?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,m.jsx)(ti,{scope:p,placedSide:em,onArrowChange:W,arrowX:ew,arrowY:ex,shouldHideArrow:eb,children:(0,m.jsx)(h.sG.div,{"data-side":em,"data-align":eg,...L,ref:O,style:{...L.style,animation:eh?void 0:"none"}})})})});ta.displayName=to;var tu="PopperArrow",ts={top:"bottom",right:"left",bottom:"top",left:"right"},tc=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tl(tu,n),i=ts[o.placedSide];return(0,m.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,m.jsx)(e5,{...r,ref:t,style:{...r.style,display:"block"}})})});function td(e){return null!==e}tc.displayName=tu;var tf=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=tp(a),v={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(i=null==(r=s.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(l=null==(o=s.arrow)?void 0:o.y)?l:0)+f/2,y="",w="";return"bottom"===p?(y=c?v:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=c?v:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=c?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function tp(e){let[t,n="center"]=e.split("-");return[t,n]}var th=l.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[u,s]=l.useState(!1);(0,e6.N)(()=>s(!0),[]);let c=o||u&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?a.createPortal((0,m.jsx)(h.sG.div,{...i,ref:t}),c):null});th.displayName="Portal";var tv=n(9708),tm=n(5845),tg=n(5503),ty=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});l.forwardRef((e,t)=>(0,m.jsx)(h.sG.span,{...e,ref:t,style:{...ty,...e.style}})).displayName="VisuallyHidden";var tw=new WeakMap,tx=new WeakMap,tb={},tE=0,tS=function(e){return e&&(e.host||tS(e.parentNode))},tC=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tS(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tb[n]||(tb[n]=new WeakMap);var i=tb[n],l=[],a=new Set,u=new Set(o),s=function(e){!e||a.has(e)||(a.add(e),s(e.parentNode))};o.forEach(s);var c=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tw.get(e)||0)+1,s=(i.get(e)||0)+1;tw.set(e,u),i.set(e,s),l.push(e),1===u&&o&&tx.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),tE++,function(){l.forEach(function(e){var t=tw.get(e)-1,o=i.get(e)-1;tw.set(e,t),i.set(e,o),t||(tx.has(e)||e.removeAttribute(r),tx.delete(e)),o||e.removeAttribute(n)}),--tE||(tw=new WeakMap,tw=new WeakMap,tx=new WeakMap,tb={})}},tR=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tC(r,o,n,"aria-hidden")):function(){return null}},tA=function(){return(tA=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tT(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tk=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tL="width-before-scroll-bar";function tP(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tN="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,tM=new WeakMap;function tO(e){return e}var tj=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=tO),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return o.options=tA({async:!0,ssr:!1},e),o}(),tD=function(){},tI=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),u=l.useState({onScrollCapture:tD,onWheelCapture:tD,onTouchMoveCapture:tD}),s=u[0],c=u[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,v=e.enabled,m=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,x=e.inert,b=e.allowPinchZoom,E=e.as,S=e.gapMode,C=tT(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[a,t],r=function(e){return n.forEach(function(t){return tP(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tN(function(){var e=tM.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tP(e,null)}),r.forEach(function(e){t.has(e)||tP(e,o)})}tM.set(i,n)},[n]),i),A=tA(tA({},C),s);return l.createElement(l.Fragment,null,v&&l.createElement(g,{sideCar:tj,removeScrollBar:h,shards:m,noRelative:y,noIsolation:w,inert:x,setCallbacks:c,allowPinchZoom:!!b,lockRef:a,gapMode:S}),d?l.cloneElement(l.Children.only(f),tA(tA({},A),{ref:R})):l.createElement(void 0===E?"div":E,tA({},A,{className:p,ref:R}),f))});tI.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tI.classNames={fullWidth:tL,zeroRight:tk};var tW=function(e){var t=e.sideCar,n=tT(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,tA({},n))};tW.isSideCarExport=!0;var tF=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tH=function(){var e=tF();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tB=function(){var e=tH();return function(t){return e(t.styles,t.dynamic),null}},tK={left:0,top:0,right:0,gap:0},tz=function(e){return parseInt(e||"",10)||0},t_=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tz(n),tz(r),tz(o)]},tV=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tK;var t=t_(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tG=tB(),tX="data-scroll-locked",tY=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(tX,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tk," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tL," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tk," .").concat(tk," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tL," .").concat(tL," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(tX,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},tq=function(){var e=parseInt(document.body.getAttribute(tX)||"0",10);return isFinite(e)?e:0},tU=function(){l.useEffect(function(){return document.body.setAttribute(tX,(tq()+1).toString()),function(){var e=tq()-1;e<=0?document.body.removeAttribute(tX):document.body.setAttribute(tX,e.toString())}},[])},tZ=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;tU();var i=l.useMemo(function(){return tV(o)},[o]);return l.createElement(tG,{styles:tY(i,!t,o,n?"":"!important")})},t$=!1;if("undefined"!=typeof window)try{var tJ=Object.defineProperty({},"passive",{get:function(){return t$=!0,!0}});window.addEventListener("test",tJ,tJ),window.removeEventListener("test",tJ,tJ)}catch(e){t$=!1}var tQ=!!t$&&{passive:!1},t0=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},t1=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),t2(e,r)){var o=t5(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},t2=function(e,t){return"v"===e?t0(t,"overflowY"):t0(t,"overflowX")},t5=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},t6=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,s=t.contains(u),c=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=t5(e,u),v=h[0],m=h[1]-h[2]-l*v;(v||m)&&t2(e,u)&&(f+=m,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},t7=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},t3=function(e){return[e.deltaX,e.deltaY]},t9=function(e){return e&&"current"in e?e.current:e},t8=0,t4=[];let ne=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(t8++)[0],i=l.useState(tB)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(t9),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=t7(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],s="deltaY"in e?e.deltaY:l[1]-i[1],c=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=t1(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t1(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return t6(p,t,e,"h"===p?u:s,!0)},[]),s=l.useCallback(function(e){if(t4.length&&t4[t4.length-1]===i){var n="deltaY"in e?t3(e):t7(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(t9).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=t7(e),r.current=void 0},[]),f=l.useCallback(function(t){c(t.type,t3(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){c(t.type,t7(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return t4.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,tQ),document.addEventListener("touchmove",s,tQ),document.addEventListener("touchstart",d,tQ),function(){t4=t4.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,tQ),document.removeEventListener("touchmove",s,tQ),document.removeEventListener("touchstart",d,tQ)}},[]);var h=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(tZ,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tj.useMedium(r),tW);var nt=l.forwardRef(function(e,t){return l.createElement(tI,tA({},e,{ref:t,sideCar:ne}))});nt.classNames=tI.classNames;var nn=[" ","Enter","ArrowUp","ArrowDown"],nr=[" ","Enter"],no="Select",[ni,nl,na]=(0,c.N)(no),[nu,ns]=(0,f.A)(no,[na,e8]),nc=e8(),[nd,nf]=nu(no),[np,nh]=nu(no),nv=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:a,defaultValue:u,onValueChange:s,dir:c,name:d,autoComplete:f,disabled:h,required:v,form:g}=e,y=nc(t),[w,x]=l.useState(null),[b,E]=l.useState(null),[S,C]=l.useState(!1),R=(0,p.jH)(c),[A,T]=(0,tm.i)({prop:r,defaultProp:null!=o&&o,onChange:i,caller:no}),[k,L]=(0,tm.i)({prop:a,defaultProp:u,onChange:s,caller:no}),P=l.useRef(null),N=!w||g||!!w.closest("form"),[M,j]=l.useState(new Set),D=Array.from(M).map(e=>e.props.value).join(";");return(0,m.jsx)(tt,{...y,children:(0,m.jsxs)(nd,{required:v,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,O.B)(),value:k,onValueChange:L,open:A,onOpenChange:T,dir:R,triggerPointerDownPosRef:P,disabled:h,children:[(0,m.jsx)(ni.Provider,{scope:t,children:(0,m.jsx)(np,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{j(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{j(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,m.jsxs)(n1,{"aria-hidden":!0,required:v,tabIndex:-1,name:d,autoComplete:f,value:k,onChange:e=>L(e.target.value),disabled:h,form:g,children:[void 0===k?(0,m.jsx)("option",{value:""}):null,Array.from(M)]},D):null]})})};nv.displayName=no;var nm="SelectTrigger",ng=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nc(n),a=nf(nm,n),u=a.disabled||r,c=(0,d.s)(t,a.onTriggerChange),f=nl(n),p=l.useRef("touch"),[v,g,y]=n5(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=n6(t,e,n);void 0!==r&&a.onValueChange(r.value)}),w=e=>{u||(a.onOpenChange(!0),y()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,m.jsx)(tr,{asChild:!0,...i,children:(0,m.jsx)(h.sG.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":n2(a.value)?"":void 0,...o,ref:c,onClick:(0,s.mK)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,s.mK)(o.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,s.mK)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&nn.includes(e.key)&&(w(),e.preventDefault())})})})});ng.displayName=nm;var ny="SelectValue",nw=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nf(ny,n),{onValueNodeHasChildrenChange:s}=u,c=void 0!==i,f=(0,d.s)(t,u.onValueNodeChange);return(0,e6.N)(()=>{s(c)},[s,c]),(0,m.jsx)(h.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:n2(u.value)?(0,m.jsx)(m.Fragment,{children:l}):i})});nw.displayName=ny;var nx=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,m.jsx)(h.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nx.displayName="SelectIcon";var nb=e=>(0,m.jsx)(th,{asChild:!0,...e});nb.displayName="SelectPortal";var nE="SelectContent",nS=l.forwardRef((e,t)=>{let n=nf(nE,e.__scopeSelect),[r,o]=l.useState();return((0,e6.N)(()=>{o(new DocumentFragment)},[]),n.open)?(0,m.jsx)(nT,{...e,ref:t}):r?a.createPortal((0,m.jsx)(nC,{scope:e.__scopeSelect,children:(0,m.jsx)(ni.Slot,{scope:e.__scopeSelect,children:(0,m.jsx)("div",{children:e.children})})}),r):null});nS.displayName=nE;var[nC,nR]=nu(nE),nA=(0,tv.TL)("SelectContent.RemoveScroll"),nT=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:a,side:u,sideOffset:c,align:f,alignOffset:p,arrowPadding:h,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:x,avoidCollisions:b,...C}=e,R=nf(nE,n),[A,k]=l.useState(null),[L,P]=l.useState(null),N=(0,d.s)(t,e=>k(e)),[M,O]=l.useState(null),[j,D]=l.useState(null),I=nl(n),[W,F]=l.useState(!1),H=l.useRef(!1);l.useEffect(()=>{if(A)return tR(A)},[A]),l.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:S()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:S()),E++,()=>{1===E&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),E--}},[]);let B=l.useCallback(e=>{let[t,...n]=I().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&L&&(L.scrollTop=0),n===r&&L&&(L.scrollTop=L.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[I,L]),K=l.useCallback(()=>B([M,A]),[B,M,A]);l.useEffect(()=>{W&&K()},[W,K]);let{onOpenChange:z,triggerPointerDownPosRef:_}=R;l.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=_.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(i=null==(r=_.current)?void 0:r.y)?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||z(!1),document.removeEventListener("pointermove",t),_.current=null};return null!==_.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,z,_]),l.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[V,G]=n5(e=>{let t=I().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=n6(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),X=l.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==R.value&&R.value===t||r)&&(O(e),r&&(H.current=!0))},[R.value]),Y=l.useCallback(()=>null==A?void 0:A.focus(),[A]),q=l.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==R.value&&R.value===t||r)&&D(e)},[R.value]),U="popper"===r?nL:nk,Z=U===nL?{side:u,sideOffset:c,align:f,alignOffset:p,arrowPadding:h,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:x,avoidCollisions:b}:{};return(0,m.jsx)(nC,{scope:n,content:A,viewport:L,onViewportChange:P,itemRefCallback:X,selectedItem:M,onItemLeave:Y,itemTextRefCallback:q,focusSelectedItem:K,selectedItemText:j,position:r,isPositioned:W,searchRef:V,children:(0,m.jsx)(nt,{as:nA,allowPinchZoom:!0,children:(0,m.jsx)(T,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,s.mK)(o,e=>{var t;null==(t=R.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,m.jsx)(w,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,m.jsx)(U,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...Z,onPlaced:()=>F(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,s.mK)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});nT.displayName="SelectContentImpl";var nk=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nf(nE,n),a=nR(nE,n),[s,c]=l.useState(null),[f,p]=l.useState(null),v=(0,d.s)(t,e=>p(e)),g=nl(n),y=l.useRef(!1),w=l.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:S}=a,C=l.useCallback(()=>{if(i.trigger&&i.valueNode&&s&&f&&x&&b&&E){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,a=e.width+l,c=Math.max(a,t.width),d=window.innerWidth-10,f=(0,u.q)(i,[10,Math.max(10,d-c)]);s.style.minWidth=a+"px",s.style.left=f+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,a=e.width+l,c=Math.max(a,t.width),d=window.innerWidth-10,f=(0,u.q)(i,[10,Math.max(10,d-c)]);s.style.minWidth=a+"px",s.style.right=f+"px"}let l=g(),a=window.innerHeight-20,c=x.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),v=parseInt(d.borderBottomWidth,10),m=p+h+c+parseInt(d.paddingBottom,10)+v,w=Math.min(5*b.offsetHeight,m),S=window.getComputedStyle(x),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,T=b.offsetHeight/2,k=p+h+(b.offsetTop+T);if(k<=A){let e=l.length>0&&b===l[l.length-1].ref.current;s.style.bottom="0px";let t=Math.max(a-A,T+(e?R:0)+(f.clientHeight-x.offsetTop-x.offsetHeight)+v);s.style.height=k+t+"px"}else{let e=l.length>0&&b===l[0].ref.current;s.style.top="0px";let t=Math.max(A,p+x.offsetTop+(e?C:0)+T);s.style.height=t+(m-k)+"px",x.scrollTop=k-A+x.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=w+"px",s.style.maxHeight=a+"px",null==r||r(),requestAnimationFrame(()=>y.current=!0)}},[g,i.trigger,i.valueNode,s,f,x,b,E,i.dir,r]);(0,e6.N)(()=>C(),[C]);let[R,A]=l.useState();(0,e6.N)(()=>{f&&A(window.getComputedStyle(f).zIndex)},[f]);let T=l.useCallback(e=>{e&&!0===w.current&&(C(),null==S||S(),w.current=!1)},[C,S]);return(0,m.jsx)(nP,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:y,onScrollButtonChange:T,children:(0,m.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,m.jsx)(h.sG.div,{...o,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nk.displayName="SelectItemAlignedPosition";var nL=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nc(n);return(0,m.jsx)(ta,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nL.displayName="SelectPopperPosition";var[nP,nN]=nu(nE,{}),nM="SelectViewport",nO=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nR(nM,n),a=nN(nM,n),u=(0,d.s)(t,i.onViewportChange),c=l.useRef(0);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,m.jsx)(ni.Slot,{scope:n,children:(0,m.jsx)(h.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,s.mK)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});nO.displayName=nM;var nj="SelectGroup",[nD,nI]=nu(nj),nW=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,O.B)();return(0,m.jsx)(nD,{scope:n,id:o,children:(0,m.jsx)(h.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nW.displayName=nj;var nF="SelectLabel",nH=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nI(nF,n);return(0,m.jsx)(h.sG.div,{id:o.id,...r,ref:t})});nH.displayName=nF;var nB="SelectItem",[nK,nz]=nu(nB),n_=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...a}=e,u=nf(nB,n),c=nR(nB,n),f=u.value===r,[p,v]=l.useState(null!=i?i:""),[g,y]=l.useState(!1),w=(0,d.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,r,o)}),x=(0,O.B)(),b=l.useRef("touch"),E=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,m.jsx)(nK,{scope:n,value:r,disabled:o,textId:x,isSelected:f,onItemTextChange:l.useCallback(e=>{v(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,m.jsx)(ni.ItemSlot,{scope:n,value:r,disabled:o,textValue:p,children:(0,m.jsx)(h.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":g?"":void 0,"aria-selected":f&&g,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:w,onFocus:(0,s.mK)(a.onFocus,()=>y(!0)),onBlur:(0,s.mK)(a.onBlur,()=>y(!1)),onClick:(0,s.mK)(a.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,s.mK)(a.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,s.mK)(a.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,s.mK)(a.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,s.mK)(a.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,s.mK)(a.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(nr.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});n_.displayName=nB;var nV="SelectItemText",nG=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,u=nf(nV,n),s=nR(nV,n),c=nz(nV,n),f=nh(nV,n),[p,v]=l.useState(null),g=(0,d.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=s.itemTextRefCallback)?void 0:t.call(s,e,c.value,c.disabled)}),y=null==p?void 0:p.textContent,w=l.useMemo(()=>(0,m.jsx)("option",{value:c.value,disabled:c.disabled,children:y},c.value),[c.disabled,c.value,y]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=f;return(0,e6.N)(()=>(x(w),()=>b(w)),[x,b,w]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(h.sG.span,{id:c.textId,...i,ref:g}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?a.createPortal(i.children,u.valueNode):null]})});nG.displayName=nV;var nX="SelectItemIndicator",nY=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nz(nX,n).isSelected?(0,m.jsx)(h.sG.span,{"aria-hidden":!0,...r,ref:t}):null});nY.displayName=nX;var nq="SelectScrollUpButton",nU=l.forwardRef((e,t)=>{let n=nR(nq,e.__scopeSelect),r=nN(nq,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,d.s)(t,r.onScrollButtonChange);return(0,e6.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,m.jsx)(nJ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nU.displayName=nq;var nZ="SelectScrollDownButton",n$=l.forwardRef((e,t)=>{let n=nR(nZ,e.__scopeSelect),r=nN(nZ,e.__scopeSelect),[o,i]=l.useState(!1),a=(0,d.s)(t,r.onScrollButtonChange);return(0,e6.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,m.jsx)(nJ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});n$.displayName=nZ;var nJ=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nR("SelectScrollButton",n),a=l.useRef(null),u=nl(n),c=l.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return l.useEffect(()=>()=>c(),[c]),(0,e6.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,m.jsx)(h.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,s.mK)(o.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(r,50))}),onPointerMove:(0,s.mK)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===a.current&&(a.current=window.setInterval(r,50))}),onPointerLeave:(0,s.mK)(o.onPointerLeave,()=>{c()})})}),nQ=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,m.jsx)(h.sG.div,{"aria-hidden":!0,...r,ref:t})});nQ.displayName="SelectSeparator";var n0="SelectArrow";l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nc(n),i=nf(n0,n),l=nR(n0,n);return i.open&&"popper"===l.position?(0,m.jsx)(tc,{...o,...r,ref:t}):null}).displayName=n0;var n1=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,...o}=e,i=l.useRef(null),a=(0,d.s)(t,i),u=(0,tg.Z)(r);return l.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[u,r]),(0,m.jsx)(h.sG.select,{...o,style:{...ty,...o.style},ref:a,defaultValue:r})});function n2(e){return""===e||void 0===e}function n5(e){let t=(0,v.c)(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function n6(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}n1.displayName="SelectBubbleInput";var n7=nv,n3=ng,n9=nw,n8=nx,n4=nb,re=nS,rt=nO,rn=nW,rr=nH,ro=n_,ri=nG,rl=nY,ra=nU,ru=n$,rs=nQ},5185:(e,t,n)=>{function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{mK:()=>r}),"undefined"!=typeof window&&window.document&&window.document.createElement},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(2115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(2115),i=n(2712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[s,e,a,u])]}Symbol("RADIX:SYNC_STATE")},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function i(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>f});var l,a=n(2115),u=n(6081),s=n(6101),c=n(9708),d=n(5155);function f(e){let t=e+"CollectionProvider",[n,r]=(0,u.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),i=a.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let f=e+"CollectionSlot",p=(0,c.TL)(f),h=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(f,n),l=(0,s.s)(t,o.collectionRef);return(0,d.jsx)(p,{ref:l,children:r})});h.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",g=(0,c.TL)(v),y=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=a.useRef(null),u=(0,s.s)(t,l),c=i(v,n);return a.useEffect(()=>(c.itemMap.set(l,{ref:l,...o}),()=>void c.itemMap.delete(l))),(0,d.jsx)(g,{...{[m]:""},ref:u,children:r})});return y.displayName=v,[{Provider:l,Slot:h,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function h(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=v(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function v(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap,class e extends Map{set(e,t){return p.get(this)&&(this.has(e)?o(this,l)[o(this,l).indexOf(e)]=e:o(this,l).push(e)),super.set(e,t),this}insert(e,t,n){let r,i=this.has(t),a=o(this,l).length,u=v(e),s=u>=0?u:a+u,c=s<0||s>=a?-1:s;if(c===this.size||i&&c===this.size-1||-1===c)return this.set(t,n),this;let d=this.size+ +!i;u<0&&s++;let f=[...o(this,l)],p=!1;for(let e=s;e<d;e++)if(s===e){let o=f[e];f[e]===t&&(o=f[e+1]),i&&this.delete(t),r=this.get(o),this.set(t,n)}else{p||f[e-1]!==t||(p=!0);let n=f[p?e:e-1],o=r;r=this.get(n),this.delete(n),this.set(n,o)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=o(this,l).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let r=o(this,l).indexOf(e);return -1===r?this:this.insert(r,t,n)}after(e){let t=o(this,l).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let r=o(this,l).indexOf(e);return -1===r?this:this.insert(r+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return i(this,l,[]),super.clear()}delete(e){let t=super.delete(e);return t&&o(this,l).splice(o(this,l).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=h(o(this,l),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=h(o(this,l),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return o(this,l).indexOf(e)}keyAt(e){return h(o(this,l),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=0,l=null!=o?o:this.at(0);for(let e of this)l=0===i&&1===t.length?e:Reflect.apply(r,this,[l,e,i,this]),i++;return l}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);i=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[i,n,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let o=[...this.entries()];return o.splice(...n),new e(o)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,l,{writable:!0,value:void 0}),i(this,l,[...super.keys()]),p.set(this,!0)}}},7863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9367:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}}}]);