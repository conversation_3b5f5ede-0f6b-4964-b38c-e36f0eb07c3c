{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/icon--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nconst contentType = \"image/svg+xml\"\nconst cacheControl = \"no-cache, no-store\"\nconst buffer = Buffer.from(\"PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHJ4PSI4IiBmaWxsPSIjM0I4MkY2Ii8+CiAgPHBhdGggZD0iTTggMTJoMTZ2Mkg4di0yem0wIDRoMTZ2Mkg4di0yem0wIDRoMTJ2Mkg4di0yeiIgZmlsbD0id2hpdGUiLz4KICA8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSIzIiBmaWxsPSIjMTBCOTgxIi8+Cjwvc3ZnPgo=\", 'base64')\n\nif (false || false) {\n    const fileSizeInMB = buffer.byteLength / 1024 / 1024\n    if (fileSizeInMB > 8) {\n        throw new Error('File size for Open Graph image \"[project]/Desktop/AI/ai/frontend/src/app/icon.svg\" exceeds 8MB. ' +\n        `(Current: ${fileSizeInMB.toFixed(2)}MB)\\n` +\n        'Read more: https://nextjs.org/docs/app/api-reference/file-conventions/metadata/opengraph-image#image-files-jpg-png-gif'\n        )\n    }\n}\n\nexport function GET() {\n    return new NextResponse(buffer, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\nexport const dynamic = 'force-static'\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,SAAS,OAAO,IAAI,CAAC,4XAA4X;AAEvZ;;AAUO,SAAS;IACZ,OAAO,IAAI,iKAAA,CAAA,eAAY,CAAC,QAAQ;QAC5B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ;AAEO,MAAM,UAAU", "debugId": null}}]}