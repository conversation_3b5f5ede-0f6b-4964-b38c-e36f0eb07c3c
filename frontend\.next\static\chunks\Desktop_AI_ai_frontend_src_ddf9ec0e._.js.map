{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/auth/RouteGuard.tsx"], "sourcesContent": ["'use client';\n\n// Route guard component for role-based access control\n\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useEffect } from 'react';\nimport { UserRole } from '@/types';\nimport { Loader2 } from 'lucide-react';\n\ninterface RouteGuardProps {\n  children: React.ReactNode;\n  allowedRoles?: UserRole[];\n  redirectTo?: string;\n}\n\nexport default function RouteGuard({ \n  children, \n  allowedRoles = [], \n  redirectTo = '/sign-in' \n}: RouteGuardProps) {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (status === 'loading') return; // Still loading\n\n    if (!session) {\n      router.push(redirectTo);\n      return;\n    }\n\n    if (allowedRoles.length > 0 && !allowedRoles.includes(session.user.role)) {\n      // Redirect to appropriate dashboard based on user role\n      switch (session.user.role) {\n        case 'student':\n          router.push('/student');\n          break;\n        case 'teacher':\n          router.push('/teacher');\n          break;\n        case 'hod':\n          router.push('/hod');\n          break;\n        case 'admin':\n          router.push('/admin');\n          break;\n        default:\n          router.push('/sign-in');\n      }\n      return;\n    }\n  }, [session, status, router, allowedRoles, redirectTo]);\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!session) {\n    return null; // Will redirect\n  }\n\n  if (allowedRoles.length > 0 && !allowedRoles.includes(session.user.role)) {\n    return null; // Will redirect\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA,sDAAsD;AAEtD;AACA;AACA;AAEA;;;AARA;;;;;AAgBe,SAAS,WAAW,KAIjB;QAJiB,EACjC,QAAQ,EACR,eAAe,EAAE,EACjB,aAAa,UAAU,EACP,GAJiB;;IAKjC,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,gMAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,WAAW,WAAW,QAAQ,gBAAgB;YAElD,IAAI,CAAC,SAAS;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG;gBACxE,uDAAuD;gBACvD,OAAQ,QAAQ,IAAI,CAAC,IAAI;oBACvB,KAAK;wBACH,OAAO,IAAI,CAAC;wBACZ;oBACF,KAAK;wBACH,OAAO,IAAI,CAAC;wBACZ;oBACF,KAAK;wBACH,OAAO,IAAI,CAAC;wBACZ;oBACF,KAAK;wBACH,OAAO,IAAI,CAAC;wBACZ;oBACF;wBACE,OAAO,IAAI,CAAC;gBAChB;gBACA;YACF;QACF;+BAAG;QAAC;QAAS;QAAQ;QAAQ;QAAc;KAAW;IAEtD,IAAI,WAAW,WAAW;QACxB,qBACE,gOAAC;YAAI,WAAU;sBACb,cAAA,gOAAC;gBAAI,WAAU;;kCACb,gOAAC,uPAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,gOAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,gBAAgB;IAC/B;IAEA,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG;QACxE,OAAO,MAAM,gBAAgB;IAC/B;IAEA,qBAAO;kBAAG;;AACZ;GA1DwB;;QAKY,oLAAA,CAAA,aAAU;QAC7B,wKAAA,CAAA,YAAS;;;KANF", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/lib/utils.ts"], "sourcesContent": ["// Utility functions for the AI Tutor Platform\n\nimport { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n// Utility function to merge Tailwind CSS classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format date to readable string\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date);\n}\n\n// Format time to readable string\nexport function formatTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\n// Format relative time (e.g., \"2 hours ago\")\nexport function formatRelativeTime(date: Date): string {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n  \n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n  \n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n  \n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n  \n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n  \n  return formatDate(date);\n}\n\n// Truncate text to specified length\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n// Generate random ID\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n// Validate email format\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// Debounce function\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle function\nexport function throttle<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n// Deep clone object\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj;\n  if (obj instanceof Date) return new Date(obj.getTime()) as T;\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T;\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T;\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key]);\n      }\n    }\n    return clonedObj;\n  }\n  return obj;\n}\n\n// Check if object is empty\nexport function isEmpty(obj: unknown): boolean {\n  if (obj == null) return true;\n  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;\n  if (typeof obj === 'object') return Object.keys(obj).length === 0;\n  return false;\n}\n\n// Capitalize first letter\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n// Convert camelCase to Title Case\nexport function camelToTitle(str: string): string {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n}\n\n// Sleep function for async operations\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// Local storage helpers with error handling\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : defaultValue || null;\n    } catch (error) {\n      console.error(`Error reading from localStorage:`, error);\n      return defaultValue || null;\n    }\n  },\n  \n  set: (key: string, value: unknown): boolean => {\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n      return true;\n    } catch (error) {\n      console.error(`Error writing to localStorage:`, error);\n      return false;\n    }\n  },\n  \n  remove: (key: string): boolean => {\n    try {\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error(`Error removing from localStorage:`, error);\n      return false;\n    }\n  },\n  \n  clear: (): boolean => {\n    try {\n      localStorage.clear();\n      return true;\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error);\n      return false;\n    }\n  }\n};\n\n// URL helpers\nexport const url = {\n  addParams: (baseUrl: string, params: Record<string, string>): string => {\n    const url = new URL(baseUrl, window.location.origin);\n    Object.entries(params).forEach(([key, value]) => {\n      url.searchParams.set(key, value);\n    });\n    return url.toString();\n  },\n  \n  removeParams: (baseUrl: string, paramsToRemove: string[]): string => {\n    const url = new URL(baseUrl, window.location.origin);\n    paramsToRemove.forEach(param => {\n      url.searchParams.delete(param);\n    });\n    return url.toString();\n  }\n};\n\n// Color helpers\nexport const color = {\n  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result ? {\n      r: parseInt(result[1], 16),\n      g: parseInt(result[2], 16),\n      b: parseInt(result[3], 16)\n    } : null;\n  },\n  \n  rgbToHex: (r: number, g: number, b: number): string => {\n    return \"#\" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);\n  }\n};\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;;;;;;;;;;;;;;;;AAE9C;AACA;;;AAGO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,iMAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,2KAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,mBAAmB,IAAU;IAC3C,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,AAAC,GAAyB,OAAvB,eAAc,WAAsC,OAA7B,gBAAgB,IAAI,MAAM,IAAG;IAChE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,AAAC,GAAqB,OAAnB,aAAY,SAAkC,OAA3B,cAAc,IAAI,MAAM,IAAG;IAC1D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,AAAC,GAAmB,OAAjB,YAAW,QAAgC,OAA1B,aAAa,IAAI,MAAM,IAAG;IACvD;IAEA,OAAO,WAAW;AACpB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IAEJ,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,eAAe,OAAO,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IAC3D,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,QAAQ,GAAY;IAClC,IAAI,OAAO,MAAM,OAAO;IACxB,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,QAAQ,UAAU,OAAO,IAAI,MAAM,KAAK;IACzE,IAAI,OAAO,QAAQ,UAAU,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;IAChE,OAAO;AACT;AAGO,SAAS,WAAW,GAAW;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,gBAAgB;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAE,oCAAmC;YAClD,OAAO,gBAAgB;QACzB;IACF;IAEA,KAAK,CAAC,KAAa;QACjB,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAE,kCAAiC;YAChD,OAAO;QACT;IACF;IAEA,QAAQ,CAAC;QACP,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAE,qCAAoC;YACnD,OAAO;QACT;IACF;IAEA,OAAO;QACL,IAAI;YACF,aAAa,KAAK;YAClB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAE,gCAA+B;YAC9C,OAAO;QACT;IACF;AACF;AAGO,MAAM,MAAM;IACjB,WAAW,CAAC,SAAiB;QAC3B,MAAM,MAAM,IAAI,IAAI,SAAS,OAAO,QAAQ,CAAC,MAAM;QACnD,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC;gBAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK;QAC5B;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,cAAc,CAAC,SAAiB;QAC9B,MAAM,MAAM,IAAI,IAAI,SAAS,OAAO,QAAQ,CAAC,MAAM;QACnD,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,YAAY,CAAC,MAAM,CAAC;QAC1B;QACA,OAAO,IAAI,QAAQ;IACrB;AACF;AAGO,MAAM,QAAQ;IACnB,UAAU,CAAC;QACT,MAAM,SAAS,4CAA4C,IAAI,CAAC;QAChE,OAAO,SAAS;YACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACzB,IAAI;IACN;IAEA,UAAU,CAAC,GAAW,GAAW;QAC/B,OAAO,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;IACzE;AACF", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,sMAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,gMAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,sMAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,gOAAC;QACC,WAAW,CAAA,GAAA,yJAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/%28dash%29/layout.tsx"], "sourcesContent": ["'use client';\n\n// Dashboard layout with navigation and role-based access\n\nimport { useSession, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport RouteGuard from '@/components/auth/RouteGuard';\nimport { Button } from '@/components/ui/button';\nimport { \n  GraduationCap, \n  LogOut, \n  User, \n  BookOpen, \n  MessageSquare,\n  Settings,\n  BarChart3,\n  Users,\n  Bell\n} from 'lucide-react';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const { data: session } = useSession();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/sign-in' });\n  };\n\n  const getNavigationItems = () => {\n    const role = session?.user?.role;\n    \n    const baseItems = [\n      { icon: BookOpen, label: 'Subjects', href: `/${role}` },\n    ];\n\n    switch (role) {\n      case 'student':\n        return [\n          ...baseItems,\n          { icon: MessageSquare, label: '<PERSON> Chat', href: '/student/chat' },\n        ];\n      case 'teacher':\n        return [\n          ...baseItems,\n          { icon: MessageSquare, label: 'AI Chat', href: '/teacher/chat' },\n          { icon: Settings, label: 'Avatar Setup', href: '/teacher/avatar' },\n        ];\n      case 'hod':\n        return [\n          ...baseItems,\n          { icon: Users, label: 'Teachers', href: '/hod/teachers' },\n          { icon: BarChart3, label: 'Analytics', href: '/hod/analytics' },\n          { icon: Bell, label: 'Announcements', href: '/hod/announcements' },\n        ];\n      case 'admin':\n        return [\n          ...baseItems,\n          { icon: Users, label: 'Users', href: '/admin/users' },\n          { icon: BarChart3, label: 'Analytics', href: '/admin/analytics' },\n          { icon: Bell, label: 'Announcements', href: '/admin/announcements' },\n          { icon: Settings, label: 'Settings', href: '/admin/settings' },\n        ];\n      default:\n        return baseItems;\n    }\n  };\n\n  return (\n    <RouteGuard allowedRoles={['student', 'teacher', 'hod', 'admin']}>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white shadow-sm border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center h-16\">\n              <div className=\"flex items-center\">\n                <div className=\"flex items-center\">\n                  <GraduationCap className=\"h-8 w-8 text-blue-600\" />\n                  <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                    AI Tutor Platform\n                  </span>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <User className=\"h-5 w-5 text-gray-500\" />\n                  <span className=\"text-sm text-gray-700\">\n                    {session?.user?.name}\n                  </span>\n                  <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full capitalize\">\n                    {session?.user?.role}\n                  </span>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleSignOut}\n                  className=\"flex items-center space-x-2\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Sign Out</span>\n                </Button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        <div className=\"flex\">\n          {/* Sidebar */}\n          <nav className=\"w-64 bg-white shadow-sm min-h-screen\">\n            <div className=\"p-4\">\n              <ul className=\"space-y-2\">\n                {getNavigationItems().map((item) => (\n                  <li key={item.href}>\n                    <button\n                      onClick={() => router.push(item.href)}\n                      className=\"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\"\n                    >\n                      <item.icon className=\"h-5 w-5\" />\n                      <span>{item.label}</span>\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </nav>\n\n          {/* Main Content */}\n          <main className=\"flex-1 p-6\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </RouteGuard>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,yDAAyD;AAEzD;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;AAoBe,SAAS,gBAAgB,KAIvC;QAJuC,EACtC,QAAQ,EAGT,GAJuC;QAuEnB,eAGA;;IArEnB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,oLAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAW;IAC1C;IAEA,MAAM,qBAAqB;YACZ;QAAb,MAAM,OAAO,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI;QAEhC,MAAM,YAAY;YAChB;gBAAE,MAAM,oPAAA,CAAA,WAAQ;gBAAE,OAAO;gBAAY,MAAM,AAAC,IAAQ,OAAL;YAAO;SACvD;QAED,OAAQ;YACN,KAAK;gBACH,OAAO;uBACF;oBACH;wBAAE,MAAM,8PAAA,CAAA,gBAAa;wBAAE,OAAO;wBAAW,MAAM;oBAAgB;iBAChE;YACH,KAAK;gBACH,OAAO;uBACF;oBACH;wBAAE,MAAM,8PAAA,CAAA,gBAAa;wBAAE,OAAO;wBAAW,MAAM;oBAAgB;oBAC/D;wBAAE,MAAM,gPAAA,CAAA,WAAQ;wBAAE,OAAO;wBAAgB,MAAM;oBAAkB;iBAClE;YACH,KAAK;gBACH,OAAO;uBACF;oBACH;wBAAE,MAAM,0OAAA,CAAA,QAAK;wBAAE,OAAO;wBAAY,MAAM;oBAAgB;oBACxD;wBAAE,MAAM,wPAAA,CAAA,YAAS;wBAAE,OAAO;wBAAa,MAAM;oBAAiB;oBAC9D;wBAAE,MAAM,wOAAA,CAAA,OAAI;wBAAE,OAAO;wBAAiB,MAAM;oBAAqB;iBAClE;YACH,KAAK;gBACH,OAAO;uBACF;oBACH;wBAAE,MAAM,0OAAA,CAAA,QAAK;wBAAE,OAAO;wBAAS,MAAM;oBAAe;oBACpD;wBAAE,MAAM,wPAAA,CAAA,YAAS;wBAAE,OAAO;wBAAa,MAAM;oBAAmB;oBAChE;wBAAE,MAAM,wOAAA,CAAA,OAAI;wBAAE,OAAO;wBAAiB,MAAM;oBAAuB;oBACnE;wBAAE,MAAM,gPAAA,CAAA,WAAQ;wBAAE,OAAO;wBAAY,MAAM;oBAAkB;iBAC9D;YACH;gBACE,OAAO;QACX;IACF;IAEA,qBACE,gOAAC,8KAAA,CAAA,UAAU;QAAC,cAAc;YAAC;YAAW;YAAW;YAAO;SAAQ;kBAC9D,cAAA,gOAAC;YAAI,WAAU;;8BAEb,gOAAC;oBAAO,WAAU;8BAChB,cAAA,gOAAC;wBAAI,WAAU;kCACb,cAAA,gOAAC;4BAAI,WAAU;;8CACb,gOAAC;oCAAI,WAAU;8CACb,cAAA,gOAAC;wCAAI,WAAU;;0DACb,gOAAC,8PAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,gOAAC;gDAAK,WAAU;0DAAuC;;;;;;;;;;;;;;;;;8CAM3D,gOAAC;oCAAI,WAAU;;sDACb,gOAAC;4CAAI,WAAU;;8DACb,gOAAC,wOAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,gOAAC;oDAAK,WAAU;8DACb,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI;;;;;;8DAEtB,gOAAC;oDAAK,WAAU;8DACb,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,IAAI;;;;;;;;;;;;sDAGxB,gOAAC,wKAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,gOAAC,gPAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,gOAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,gOAAC;oBAAI,WAAU;;sCAEb,gOAAC;4BAAI,WAAU;sCACb,cAAA,gOAAC;gCAAI,WAAU;0CACb,cAAA,gOAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,qBACzB,gOAAC;sDACC,cAAA,gOAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;gDACpC,WAAU;;kEAEV,gOAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,gOAAC;kEAAM,KAAK,KAAK;;;;;;;;;;;;2CANZ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;sCAe1B,gOAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAvHwB;;QAKI,oLAAA,CAAA,aAAU;QACrB,wKAAA,CAAA,YAAS;;;KANF", "debugId": null}}]}