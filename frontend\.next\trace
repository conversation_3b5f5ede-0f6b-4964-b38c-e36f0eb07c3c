[{"name": "generate-buildid", "duration": 292, "timestamp": 1765237906227, "id": 4, "parentId": 1, "tags": {}, "startTime": 1755441095089, "traceId": "2759de8680f26594"}, {"name": "load-custom-routes", "duration": 469, "timestamp": 1765237906644, "id": 5, "parentId": 1, "tags": {}, "startTime": 1755441095089, "traceId": "2759de8680f26594"}, {"name": "create-dist-dir", "duration": 897, "timestamp": 1765238257142, "id": 6, "parentId": 1, "tags": {}, "startTime": 1755441095440, "traceId": "2759de8680f26594"}, {"name": "create-pages-mapping", "duration": 865, "timestamp": 1765238681424, "id": 7, "parentId": 1, "tags": {}, "startTime": 1755441095864, "traceId": "2759de8680f26594"}, {"name": "collect-app-paths", "duration": 13362, "timestamp": 1765238682454, "id": 8, "parentId": 1, "tags": {}, "startTime": 1755441095865, "traceId": "2759de8680f26594"}, {"name": "create-app-mapping", "duration": 27971, "timestamp": 1765238695915, "id": 9, "parentId": 1, "tags": {}, "startTime": 1755441095879, "traceId": "2759de8680f26594"}, {"name": "public-dir-conflict-check", "duration": 7308, "timestamp": 1765238724932, "id": 10, "parentId": 1, "tags": {}, "startTime": 1755441095908, "traceId": "2759de8680f26594"}, {"name": "generate-routes-manifest", "duration": 10684, "timestamp": 1765238735194, "id": 11, "parentId": 1, "tags": {}, "startTime": 1755441095918, "traceId": "2759de8680f26594"}, {"name": "next-build", "duration": 35899604, "timestamp": 1765236963541, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.4.6", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1755441094146, "traceId": "2759de8680f26594"}]