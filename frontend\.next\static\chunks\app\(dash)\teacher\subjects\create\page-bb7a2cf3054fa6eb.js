(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[588],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var r=t(5155),a=t(2115),n=t(9708),i=t(2085),l=t(9434);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,s)=>{let{className:t,variant:a,size:i,asChild:c=!1,...o}=e,u=c?n.DX:"button";return(0,r.jsx)(u,{className:(0,l.cn)(d({variant:a,size:i,className:t})),ref:s,...o})});c.displayName="Button"},968:(e,s,t)=>{"use strict";t.d(s,{b:()=>l});var r=t(2115),a=t(3655),n=t(5155),i=r.forwardRef((e,s)=>(0,n.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var l=i},1425:(e,s,t)=>{Promise.resolve().then(t.bind(t,8118))},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var r=t(5155),a=t(2115),n=t(9434);let i=a.forwardRef((e,s)=>{let{className:t,type:a,...i}=e;return(0,r.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"},3655:(e,s,t)=>{"use strict";t.d(s,{hO:()=>d,sG:()=>l});var r=t(2115),a=t(7650),n=t(9708),i=t(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,n.TL)(`Primitive.${s}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?t:s,{...n,ref:r})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function d(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}},4229:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5040:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5057:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var r=t(5155),a=t(2115),n=t(968),i=t(2085),l=t(9434);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.b,{ref:s,className:(0,l.cn)(d(),t),...a})});c.displayName=n.b.displayName},5169:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>l});var r=t(5155),a=t(2115),n=t(9434);let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});l.displayName="CardHeader";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},8118:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(5155),a=t(2115),n=t(5695),i=t(2108),l=t(6695),d=t(285),c=t(2523),o=t(5057),u=t(8539),m=t(5169),f=t(5040),h=t(4229);function x(){var e,s,t,x;let p=(0,n.useRouter)(),{data:b}=(0,i.useSession)(),[v,g]=(0,a.useState)(!1),[j,y]=(0,a.useState)({name:"",description:""}),N=async e=>{e.preventDefault(),g(!0);try{console.log("Creating subject:",j),await new Promise(e=>setTimeout(e,1e3)),p.push("/teacher")}catch(e){console.error("Failed to create subject:",e)}finally{g(!1)}},w=(e,s)=>{y(t=>({...t,[e]:s}))};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(d.$,{variant:"ghost",size:"sm",onClick:()=>p.push("/teacher"),children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(f.A,{className:"h-5 w-5 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Create New Subject"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Add a new subject for your students"})]})]})]})}),(0,r.jsxs)(l.Zp,{className:"max-w-2xl",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"Subject Information"}),(0,r.jsx)(l.BT,{children:"Enter the details for your new subject. Students will be able to access AI tutoring for this subject."})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"name",children:"Subject Name *"}),(0,r.jsx)(c.p,{id:"name",type:"text",placeholder:"e.g., Japanese Language, Mathematics, Physics",value:j.name,onChange:e=>w("name",e.target.value),required:!0,disabled:v})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"description",children:"Description"}),(0,r.jsx)(u.T,{id:"description",placeholder:"Describe what students will learn in this subject...",value:j.description,onChange:e=>w("description",e.target.value),disabled:v,rows:4})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"What happens next?"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• Students in your organization will be able to access this subject"}),(0,r.jsx)("li",{children:"• You can upload study materials and customize the AI tutor"}),(0,r.jsx)("li",{children:"• Students can start chatting with the AI tutor immediately"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(d.$,{type:"submit",disabled:v||!j.name.trim(),className:"flex-1",children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Create Subject"]})}),(0,r.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>p.push("/teacher"),disabled:v,children:"Cancel"})]})]})})]}),(0,r.jsxs)(l.Zp,{className:"max-w-2xl",children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Teacher Information"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-medium",children:(null==b||null==(s=b.user)||null==(e=s.name)?void 0:e.charAt(0))||"T"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:null==b||null==(t=b.user)?void 0:t.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:null==b||null==(x=b.user)?void 0:x.email})]})]})})]})]})}},8539:(e,s,t)=>{"use strict";t.d(s,{T:()=>i});var r=t(5155),a=t(2115),n=t(9434);let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...a})});i.displayName="Textarea"},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var r=t(2596),a=t(9688);function n(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}}},e=>{e.O(0,[817,108,441,964,358],()=>e(e.s=1425)),_N_E=e.O()}]);