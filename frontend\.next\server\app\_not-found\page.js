const CHUNK_PUBLIC_PATH = "server/app/_not-found/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_a6dbe58e._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__72e60385._.js");
runtime.loadChunk("server/chunks/ssr/27da8_dd8956fe._.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_client_components_cd516cf2._.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_client_components_builtin_forbidden_c184db15.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_4ed8b418._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__aa0614bf._.js");
runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/.next-internal/server/app/_not-found/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { MODULE_0 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/layout.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/esm/build/templates/app-page.js?page=/_not-found/page { MODULE_0 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/layout.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
