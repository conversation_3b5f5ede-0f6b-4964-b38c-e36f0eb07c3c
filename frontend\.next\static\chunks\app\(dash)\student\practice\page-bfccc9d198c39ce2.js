(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[700],{252:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>F});var t=s(5155),r=s(2115),i=s(5695),n=s(2108),l=s(6695),c=s(285),d=s(6126),o=s(6081),u=s(3655),m="Progress",[x,h]=(0,o.A)(m),[p,f]=x(m),g=r.forwardRef((e,a)=>{var s,r,i,n;let{__scopeProgress:l,value:c=null,max:d,getValueLabel:o=j,...m}=e;(d||0===d)&&!w(d)&&console.error((s="".concat(d),r="Progress","Invalid prop `max` of value `".concat(s,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let x=w(d)?d:100;null===c||k(c,x)||console.error((i="".concat(c),n="Progress","Invalid prop `value` of value `".concat(i,"` supplied to `").concat(n,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let h=k(c,x)?c:null,f=N(h)?o(h,x):void 0;return(0,t.jsx)(p,{scope:l,value:h,max:x,children:(0,t.jsx)(u.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":N(h)?h:void 0,"aria-valuetext":f,role:"progressbar","data-state":b(h,x),"data-value":null!=h?h:void 0,"data-max":x,...m,ref:a})})});g.displayName=m;var v="ProgressIndicator",y=r.forwardRef((e,a)=>{var s;let{__scopeProgress:r,...i}=e,n=f(v,r);return(0,t.jsx)(u.sG.div,{"data-state":b(n.value,n.max),"data-value":null!=(s=n.value)?s:void 0,"data-max":n.max,...i,ref:a})});function j(e,a){return"".concat(Math.round(e/a*100),"%")}function b(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function N(e){return"number"==typeof e}function w(e){return N(e)&&!isNaN(e)&&e>0}function k(e,a){return N(e)&&!isNaN(e)&&e<=a&&e>=0}y.displayName=v;var A=s(9434);let C=r.forwardRef((e,a)=>{let{className:s,value:r,...i}=e;return(0,t.jsx)(g,{ref:a,className:(0,A.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...i,children:(0,t.jsx)(y,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});C.displayName=g.displayName;var M=s(9946);let q=(0,M.A)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]),P=(0,M.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var z=s(5040),T=s(5690);let I=(0,M.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var R=s(5169);let S=(0,M.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var E=s(4186);let L=(0,M.A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var _=s(693),B=s(5731);function F(){let e=(0,i.useRouter)(),a=(0,i.useSearchParams)().get("subject"),{data:s}=(0,n.useSession)();console.log("User session:",s);let o=(0,_.fR)(),[u,m]=(0,r.useState)(!0),x=(o.length>0?o:B.jy.subjects).find(e=>e.id===a),h=[{id:"1",title:"Hiragana Recognition",description:"Practice identifying hiragana characters",difficulty:"beginner",estimatedTime:15,questionsCount:20,completedCount:12,subjectId:"1",subjectName:"Japanese Language",type:"flashcards"},{id:"2",title:"Basic Grammar Quiz",description:"Test your understanding of basic Japanese grammar patterns",difficulty:"intermediate",estimatedTime:25,questionsCount:15,completedCount:0,subjectId:"1",subjectName:"Japanese Language",type:"quiz"},{id:"3",title:"Listening Comprehension",description:"Practice understanding spoken Japanese",difficulty:"intermediate",estimatedTime:30,questionsCount:10,completedCount:5,subjectId:"1",subjectName:"Japanese Language",type:"listening"},{id:"4",title:"Algebra Problem Solving",description:"Practice solving algebraic equations",difficulty:"beginner",estimatedTime:20,questionsCount:12,completedCount:12,subjectId:"2",subjectName:"Mathematics",type:"quiz"}];(0,r.useEffect)(()=>{let e=setTimeout(()=>{m(!1)},1e3);return()=>clearTimeout(e)},[]);let p=a?h.filter(e=>e.subjectId===a):h;return u?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading practice sessions..."})]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>e.push("/student"),children:[(0,t.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,t.jsx)(I,{className:"h-5 w-5 text-orange-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Practice Sessions"}),(0,t.jsx)("p",{className:"text-gray-600",children:x?"Practice ".concat(x.name," skills"):"Improve your skills with interactive practice"})]})]})]})}),x&&(0,t.jsx)(l.Zp,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(z.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900",children:x.name}),(0,t.jsxs)("p",{className:"text-sm text-blue-700",children:[p.length," practice session",1!==p.length?"s":""," available"]})]})]}),(0,t.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>e.push("/student/practice"),children:"View All Subjects"})]})})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map(e=>{let a=e.completedCount/e.questionsCount*100,s=e.completedCount===e.questionsCount;return(0,t.jsxs)(l.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(e=>{switch(e){case"quiz":return(0,t.jsx)(q,{className:"h-5 w-5 text-blue-600"});case"flashcards":return(0,t.jsx)(P,{className:"h-5 w-5 text-purple-600"});case"writing":return(0,t.jsx)(z.A,{className:"h-5 w-5 text-green-600"});case"listening":return(0,t.jsx)(T.A,{className:"h-5 w-5 text-orange-600"});default:return(0,t.jsx)(I,{className:"h-5 w-5 text-gray-600"})}})(e.type),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)(l.ZB,{className:"text-lg",children:e.title}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(d.E,{variant:"secondary",className:"text-xs",children:(e=>{switch(e){case"quiz":return"Quiz";case"flashcards":return"Flashcards";case"writing":return"Writing";case"listening":return"Listening";default:return"Practice"}})(e.type)}),(0,t.jsx)(d.E,{className:"text-xs ".concat((e=>{switch(e){case"beginner":return"bg-green-100 text-green-800";case"intermediate":return"bg-yellow-100 text-yellow-800";case"advanced":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.difficulty)),children:e.difficulty})]})]})]}),s&&(0,t.jsx)(S,{className:"h-5 w-5 text-green-600"})]}),(0,t.jsx)(l.BT,{className:"line-clamp-2",children:e.description})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Progress"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.completedCount,"/",e.questionsCount]})]}),(0,t.jsx)(C,{value:a,className:"h-2"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[e.estimatedTime," min"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.subjectName})]})]}),(0,t.jsx)(c.$,{onClick:()=>{console.log("Starting practice session:",e.id),alert("Practice session would start here!")},className:"w-full",variant:s?"outline":"default",children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(L,{className:"h-4 w-4 mr-2"}),"Review"]}):e.completedCount>0?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Continue"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Start Practice"]})})]})})]},e.id)})}),0===p.length&&(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,t.jsx)(I,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No practice sessions available"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:x?"No practice sessions are available for ".concat(x.name," yet."):"No practice sessions are available yet."}),(0,t.jsx)(c.$,{variant:"outline",onClick:()=>e.push("/student"),children:"Back to Dashboard"})]})})]})}},4186:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5040:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5526:(e,a,s)=>{Promise.resolve().then(s.bind(s,252))},5690:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]])},6126:(e,a,s)=>{"use strict";s.d(a,{E:()=>l});var t=s(5155);s(2115);var r=s(2085),i=s(9434);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:a,variant:s,...r}=e;return(0,t.jsx)("div",{className:(0,i.cn)(n({variant:s}),a),...r})}}},e=>{e.O(0,[817,108,294,747,441,964,358],()=>e(e.s=5526)),_N_E=e.O()}]);