import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { connectDB } from './config/db';
import { env } from './config/env';
import routes from './routes';

const app = express();

/**
 * Security Middleware
 */
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

/**
 * CORS Configuration
 */
app.use(cors({
  origin: env.CORS_ORIGIN,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

/**
 * Body Parsing Middleware
 */
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * Request Logging Middleware (Development)
 */
if (env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
  });
}

/**
 * API Routes
 */
app.use('/api/v1', routes);

/**
 * Root Endpoint
 */
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'AI Tutor Platform API',
    version: '1.0.0',
    documentation: '/api/v1/health',
  });
});

/**
 * 404 Handler
 */
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.originalUrl,
  });
});

/**
 * Global Error Handler
 */
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', error);
  
  res.status(error.status || 500).json({
    success: false,
    error: env.NODE_ENV === 'production' ? 'Internal server error' : error.message,
    ...(env.NODE_ENV === 'development' && { stack: error.stack }),
  });
});

/**
 * Start Server
 */
const startServer = async () => {
  try {
    // Always connect to MongoDB
    try {
      await connectDB();
      console.log('✅ MongoDB connected successfully');
    } catch (dbError) {
      console.error('❌ MongoDB connection failed:', dbError);
      if (env.NODE_ENV === 'production') {
        throw dbError;
      }
      console.log('⚠️  Continuing without MongoDB in development mode');
    }

    // Start HTTP server
    const PORT = parseInt(env.PORT) || 3000;
    
    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📚 Environment: ${env.NODE_ENV}`);
      console.log(`🌐 API Base URL: http://localhost:${PORT}/api/v1`);
      console.log(`💾 Database: Connected to MongoDB`);
      
      if (env.NODE_ENV === 'development') {
        console.log('\n📋 Available Endpoints:');
        console.log('  GET  /api/v1/health - Health check');
        console.log('  POST /api/v1/auth/register - User registration');
        console.log('  POST /api/v1/auth/login - User login');
        console.log('  GET  /api/v1/auth/me - Get current user');
        console.log('  POST /api/v1/materials - Create material');
        console.log('  GET  /api/v1/materials - Get materials');
        console.log('  POST /api/v1/chat - Process question');
        console.log('  POST /api/v1/tts - Generate TTS');
        console.log('  POST /api/v1/avatar - Create avatar');
        console.log('  GET  /api/v1/announcements - Get announcements');
        console.log('  GET  /api/v1/analytics/dashboard - Dashboard summary');
        console.log('\n🔧 Frontend Integration Notes:');
        console.log('  - All protected routes require Authorization: Bearer <token>');
        console.log('  - File uploads use signed S3 URLs from /materials endpoint');
        console.log('  - Chat responses can be converted to audio via /tts endpoint');
        console.log('  - Role-based access control enforced on all endpoints');
      }
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

// Start the server
startServer();
