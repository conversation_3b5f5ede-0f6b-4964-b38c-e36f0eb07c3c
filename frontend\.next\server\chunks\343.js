"use strict";exports.id=343,exports.ids=[343],exports.modules={62185:(a,b,c)=>{c.d(b,{Pt:()=>f,jy:()=>g});let d="http://localhost:3001";async function e(a,b={}){try{let c=await fetch(`${d}${a}`,{headers:{"Content-Type":"application/json",...b.headers},...b});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(a){return console.error("API request failed:",a),{success:!1,error:a instanceof Error?a.message:"Unknown error occurred"}}}let f={sendMessage:async function*(a,b){let c=await fetch(`${d}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:a,subjectId:b})});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);let e=c.body?.getReader();if(!e)throw Error("No response body");let f=new TextDecoder,g="";try{for(;;){let{done:a,value:b}=await e.read();if(a)break;let c=(g+=f.decode(b,{stream:!0})).split("\n");for(let a of(g=c.pop()||"",c))if(a.startsWith("data: ")){let b=a.slice(6);if("[DONE]"===b)return;try{let a=JSON.parse(b);yield a}catch(a){console.error("Failed to parse SSE data:",a)}}}}finally{e.releaseLock()}},getChatHistory:a=>e(`/chat/history${a?`?subjectId=${a}`:""}`)},g={user:{id:"1",name:"John Doe",email:"<EMAIL>",role:"student",orgId:"org1",createdAt:new Date,updatedAt:new Date},subjects:[{id:"1",name:"Japanese Language",description:"Learn Japanese with AI assistance",teacherId:"teacher1",teacherName:"Tanaka Sensei",orgId:"org1",createdAt:new Date,updatedAt:new Date},{id:"2",name:"Mathematics",description:"Advanced mathematics concepts",teacherId:"teacher2",teacherName:"Smith Sensei",orgId:"org1",createdAt:new Date,updatedAt:new Date}],messages:[{id:"1",content:"Hello! How can I help you today?",role:"assistant",timestamp:new Date,subjectId:"1"},{id:"2",content:"Can you explain the difference between は and が?",role:"user",timestamp:new Date,subjectId:"1"}]}},90631:(a,b,c)=>{c.d(b,{R9:()=>n,fR:()=>h,st:()=>i});var d=c(98483),e=c(59350),f=c(62185);let g=(0,d.v)()((0,e.lt)((0,e.Zr)(a=>({user:void 0,subjects:[],materials:[],announcements:[],analytics:void 0,setUser:b=>{a({user:b},!1,"setUser")},setSubjects:b=>{a({subjects:b},!1,"setSubjects")},setMaterials:b=>{a({materials:b},!1,"setMaterials")},setAnnouncements:b=>{a({announcements:b},!1,"setAnnouncements")},setAnalytics:b=>{a({analytics:b},!1,"setAnalytics")}}),{name:"user-store",partialize:a=>({user:a.user,subjects:a.subjects,announcements:a.announcements})}),{name:"user-store"})),h=()=>g(a=>a.subjects),i=()=>g(a=>a.analytics),j=async()=>{try{return console.log("Loading subjects"),g.getState().setSubjects(f.jy.subjects),{success:!0}}catch(a){return console.error("Failed to load subjects:",a),{success:!1,error:"Failed to load subjects"}}},k=async()=>{try{return console.log("Loading announcements"),g.getState().setAnnouncements([]),{success:!0}}catch(a){return console.error("Failed to load announcements:",a),{success:!1,error:"Failed to load announcements"}}},l=async a=>{if(!a||"hod"!==a&&"admin"!==a)return{success:!1,error:"Unauthorized"};try{return console.log("Loading analytics"),g.getState().setAnalytics({totalUsers:150,totalStudents:120,totalTeachers:25,totalSubjects:15,totalMaterials:85,totalChats:1250,activeUsersToday:45,activeUsersThisWeek:98,popularSubjects:[{subjectId:"1",subjectName:"Japanese Language",chatCount:450},{subjectId:"2",subjectName:"Mathematics",chatCount:320}],userGrowth:[{date:"2024-01-01",count:100},{date:"2024-02-01",count:120},{date:"2024-03-01",count:150}]}),{success:!0}}catch(a){return console.error("Failed to load analytics:",a),{success:!1,error:"Failed to load analytics"}}},m=async a=>{let b=(await Promise.allSettled([j(),k(),..."hod"===a||"admin"===a?[l(a)]:[]])).filter(a=>"rejected"===a.status);return b.length>0&&console.error("Some data failed to load:",b),{success:0===b.length}},n={loadSubjects:j,loadMaterials:async a=>{try{return console.log("Loading materials for subject:",a),g.getState().setMaterials([]),{success:!0}}catch(a){return console.error("Failed to load materials:",a),{success:!1,error:"Failed to load materials"}}},loadAnnouncements:k,loadAnalytics:l,loadAllData:m}},93508:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},96834:(a,b,c)=>{c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}}};