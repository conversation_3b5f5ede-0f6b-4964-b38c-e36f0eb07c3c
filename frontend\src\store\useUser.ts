// Zustand store for User and App state management

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { useCallback } from 'react';
import { User, Subject, StudyMaterial, Announcement, Analytics, UserStore } from '@/types';
import { mockData } from '@/lib/api';

export const useUser = create<UserStore>()(
  devtools(
    persist(
      (set) => ({
        // State
        user: undefined,
        subjects: [],
        materials: [],
        announcements: [],
        analytics: undefined,

        // Actions
        setUser: (user: User | undefined) => {
          set({ user }, false, 'setUser');
        },

        setSubjects: (subjects: Subject[]) => {
          set({ subjects }, false, 'setSubjects');
        },

        setMaterials: (materials: StudyMaterial[]) => {
          set({ materials }, false, 'setMaterials');
        },

        setAnnouncements: (announcements: Announcement[]) => {
          set({ announcements }, false, 'setAnnouncements');
        },

        setAnalytics: (analytics: Analytics) => {
          set({ analytics }, false, 'setAnalytics');
        },
      }),
      {
        name: 'user-store',
        partialize: (state) => ({
          user: state.user,
          subjects: state.subjects,
          announcements: state.announcements,
        }),
      }
    ),
    {
      name: 'user-store',
    }
  )
);

// Selectors for optimized re-renders
export const useCurrentUser = () => useUser((state) => state.user);
export const useSubjects = () => useUser((state) => state.subjects);
export const useMaterials = () => useUser((state) => state.materials);
export const useAnnouncements = () => useUser((state) => state.announcements);
export const useAnalytics = () => useUser((state) => state.analytics);

// Actions selectors
export const useUserActions = () => useUser((state) => ({
  setUser: state.setUser,
  setSubjects: state.setSubjects,
  setMaterials: state.setMaterials,
  setAnnouncements: state.setAnnouncements,
  setAnalytics: state.setAnalytics,
}));

// Authentication helpers
export const useAuth = () => {
  const { user, setUser } = useUser();

  const login = async (email: string, password: string) => {
    try {
      // TODO: Implement actual login when NextAuth is configured
      console.log('Logging in:', email, 'with password length:', password.length);

      // For now, use mock data
      setUser(mockData.user);
      return { success: true };
    } catch (error) {
      console.error('Login failed:', error);
      return { success: false, error: 'Login failed' };
    }
  };

  const logout = async () => {
    try {
      // TODO: Implement actual logout when NextAuth is configured
      setUser(undefined);
      // Clear other stores if needed
      return { success: true };
    } catch (error) {
      console.error('Logout failed:', error);
      return { success: false, error: 'Logout failed' };
    }
  };

  const loadUserProfile = async () => {
    try {
      // TODO: Implement actual API call when backend is ready
      // const response = await authApi.getMe();
      console.log('Loading user profile');
      
      // For now, use mock data
      setUser(mockData.user);
      return { success: true };
    } catch (error) {
      console.error('Failed to load user profile:', error);
      return { success: false, error: 'Failed to load profile' };
    }
  };

  return {
    user,
    login,
    logout,
    loadUserProfile,
    isAuthenticated: !!user,
    isStudent: user?.role === 'student',
    isTeacher: user?.role === 'teacher',
    isHOD: user?.role === 'hod',
    isAdmin: user?.role === 'admin',
  };
};

// Data loading helpers
export const useDataLoaders = () => {
  // Get stable references to store actions
  const setSubjects = useUser((state) => state.setSubjects);
  const setMaterials = useUser((state) => state.setMaterials);
  const setAnnouncements = useUser((state) => state.setAnnouncements);
  const setAnalytics = useUser((state) => state.setAnalytics);
  const { user } = useAuth();

  const loadSubjects = useCallback(async () => {
    try {
      // TODO: Implement actual API call when backend is ready
      // const response = await subjectApi.getSubjects();
      console.log('Loading subjects');

      // For now, use mock data
      setSubjects(mockData.subjects);
      return { success: true };
    } catch (error) {
      console.error('Failed to load subjects:', error);
      return { success: false, error: 'Failed to load subjects' };
    }
  }, [setSubjects]); // setSubjects is stable from Zustand store

  const loadMaterials = useCallback(async (subjectId?: string) => {
    try {
      // TODO: Implement actual API call when backend is ready
      // const response = await materialApi.getMaterials(subjectId);
      console.log('Loading materials for subject:', subjectId);

      // For now, use empty array
      setMaterials([]);
      return { success: true };
    } catch (error) {
      console.error('Failed to load materials:', error);
      return { success: false, error: 'Failed to load materials' };
    }
  }, [setMaterials]); // setMaterials is stable from Zustand store

  const loadAnnouncements = useCallback(async () => {
    try {
      // TODO: Implement actual API call when backend is ready
      // const response = await announcementApi.getAnnouncements();
      console.log('Loading announcements');

      // For now, use empty array
      setAnnouncements([]);
      return { success: true };
    } catch (error) {
      console.error('Failed to load announcements:', error);
      return { success: false, error: 'Failed to load announcements' };
    }
  }, [setAnnouncements]); // setAnnouncements is stable from Zustand store

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const loadAnalytics = useCallback(async () => {
    if (!user || (user.role !== 'hod' && user.role !== 'admin')) {
      return { success: false, error: 'Unauthorized' };
    }

    try {
      // TODO: Implement actual API call when backend is ready
      // const response = await analyticsApi.getAnalytics();
      console.log('Loading analytics');

      // For now, use mock analytics data
      const mockAnalytics: Analytics = {
        totalUsers: 150,
        totalStudents: 120,
        totalTeachers: 25,
        totalSubjects: 15,
        totalMaterials: 85,
        totalChats: 1250,
        activeUsersToday: 45,
        activeUsersThisWeek: 98,
        popularSubjects: [
          { subjectId: '1', subjectName: 'Japanese Language', chatCount: 450 },
          { subjectId: '2', subjectName: 'Mathematics', chatCount: 320 },
        ],
        userGrowth: [
          { date: '2024-01-01', count: 100 },
          { date: '2024-02-01', count: 120 },
          { date: '2024-03-01', count: 150 },
        ],
      };

      setAnalytics(mockAnalytics);
      return { success: true };
    } catch (error) {
      console.error('Failed to load analytics:', error);
      return { success: false, error: 'Failed to load analytics' };
    }
  }, [user?.role]); // setAnalytics and user are stable, only user.role changes matter

  const loadAllData = useCallback(async () => {
    const results = await Promise.allSettled([
      loadSubjects(),
      loadAnnouncements(),
      ...(user?.role === 'hod' || user?.role === 'admin' ? [loadAnalytics()] : []),
    ]);

    const failures = results.filter(result => result.status === 'rejected');
    if (failures.length > 0) {
      console.error('Some data failed to load:', failures);
    }

    return { success: failures.length === 0 };
  }, [loadSubjects, loadAnnouncements, loadAnalytics, user?.role]);

  return {
    loadSubjects,
    loadMaterials,
    loadAnnouncements,
    loadAnalytics,
    loadAllData,
  };
};

// Subject helpers
export const useSubjectHelpers = () => {
  const subjects = useSubjects();
  const { user } = useAuth();

  const getSubjectById = (id: string) => {
    return subjects.find(subject => subject.id === id);
  };

  const getSubjectsByTeacher = (teacherId: string) => {
    return subjects.filter(subject => subject.teacherId === teacherId);
  };

  const getUserSubjects = () => {
    if (!user) return [];
    
    if (user.role === 'student') {
      // Students see all subjects in their org
      return subjects.filter(subject => subject.orgId === user.orgId);
    } else if (user.role === 'teacher') {
      // Teachers see only their subjects
      return subjects.filter(subject => subject.teacherId === user.id);
    } else {
      // HOD and Admin see all subjects in their org
      return subjects.filter(subject => subject.orgId === user.orgId);
    }
  };

  return {
    getSubjectById,
    getSubjectsByTeacher,
    getUserSubjects,
  };
};

// Role-based permissions
export const usePermissions = () => {
  const { user } = useAuth();

  const canCreateSubject = () => {
    return user?.role === 'teacher' || user?.role === 'admin';
  };

  const canEditSubject = (subjectId: string) => {
    if (!user) return false;
    if (user.role === 'admin') return true;
    
    const subject = useUser.getState().subjects.find(s => s.id === subjectId);
    return user.role === 'teacher' && subject?.teacherId === user.id;
  };

  const canUploadMaterial = (subjectId: string) => {
    return canEditSubject(subjectId);
  };

  const canCreateAnnouncement = () => {
    return user?.role === 'hod' || user?.role === 'admin';
  };

  const canViewAnalytics = () => {
    return user?.role === 'hod' || user?.role === 'admin';
  };

  const canManageUsers = () => {
    return user?.role === 'admin';
  };

  return {
    canCreateSubject,
    canEditSubject,
    canUploadMaterial,
    canCreateAnnouncement,
    canViewAnalytics,
    canManageUsers,
  };
};