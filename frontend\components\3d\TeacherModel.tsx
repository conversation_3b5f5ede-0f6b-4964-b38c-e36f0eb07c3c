'use client';

import { useRef, useEffect, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { useGLTF, useAnimations } from '@react-three/drei';
import { Group, SkinnedMesh, Bone } from 'three';
import { 
  useCurrentAvatar, 
  useModelState, 
  useAnimationState, 
  useSpeechState,
  useAITeacher
} from '@/store/useAITeacher';

interface TeacherModelProps {
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
}

export function TeacherModel({ 
  position = [0, 0, 0], 
  rotation = [0, 0, 0], 
  scale = 1 
}: TeacherModelProps) {
  const groupRef = useRef<Group>(null);
  const [modelUrl, setModelUrl] = useState<string>('/models/teacher-avatar.glb');
  
  // Zustand state
  const currentAvatar = useCurrentAvatar();
  const { isLoaded, isLoading, error } = useModelState();
  const { isAnimating, currentAnimation } = useAnimationState();
  const { isSpeaking, visemes, currentVisemeIndex } = useSpeechState();
  
  // Zustand actions
  const setModelLoading = useAITeacher((state) => state.setModelLoading);
  const setModelLoaded = useAITeacher((state) => state.setModelLoaded);
  const setModelError = useAITeacher((state) => state.setModelError);
  const setAnimating = useAITeacher((state) => state.setAnimating);

  // Update model URL when avatar changes
  useEffect(() => {
    if (currentAvatar?.modelUrl) {
      setModelUrl(currentAvatar.modelUrl);
    }
  }, [currentAvatar?.modelUrl]);

  // Load GLTF model
  const { scene, animations, error: gltfError } = useGLTF(modelUrl, true);
  const { actions, mixer } = useAnimations(animations, groupRef);

  // Handle model loading states
  useEffect(() => {
    if (gltfError) {
      console.error('Failed to load teacher model:', gltfError);
      setModelError('Failed to load 3D model');
      return;
    }

    if (scene) {
      setModelLoaded(true);
      console.log('Teacher model loaded successfully');
      
      // Setup model materials and shadows
      scene.traverse((child) => {
        if (child instanceof SkinnedMesh) {
          child.castShadow = true;
          child.receiveShadow = true;
          
          // Apply avatar appearance customizations
          if (currentAvatar?.appearance) {
            applyAvatarAppearance(child, currentAvatar.appearance);
          }
        }
      });
    }
  }, [scene, gltfError, currentAvatar?.appearance, setModelLoaded, setModelError]);

  // Handle animations
  useEffect(() => {
    if (!actions || !currentAnimation) return;

    const action = actions[currentAnimation];
    if (action) {
      // Stop all other animations
      Object.values(actions).forEach(a => a?.stop());
      
      // Play the requested animation
      action.reset().fadeIn(0.5).play();
      setAnimating(true);

      // Handle animation completion
      const onFinished = () => {
        setAnimating(false);
      };

      mixer?.addEventListener('finished', onFinished);

      return () => {
        mixer?.removeEventListener('finished', onFinished);
        action.fadeOut(0.5);
      };
    }
  }, [currentAnimation, actions, mixer, setAnimating]);

  // Handle lip-sync with visemes
  useEffect(() => {
    if (!scene || !isSpeaking || !visemes.length) return;

    const morphTargets = findMorphTargets(scene);
    if (!morphTargets) return;

    // Apply current viseme
    const currentViseme = visemes[currentVisemeIndex];
    if (currentViseme) {
      applyViseme(morphTargets, currentViseme);
    }
  }, [scene, isSpeaking, visemes, currentVisemeIndex]);

  // Animation loop
  useFrame((state, delta) => {
    if (mixer) {
      mixer.update(delta);
    }

    // Subtle idle animation when not speaking or animating
    if (groupRef.current && !isAnimating && !isSpeaking) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.05;
      groupRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.02;
    }
  });

  // Show error state
  if (error) {
    return (
      <mesh position={position}>
        <boxGeometry args={[0.5, 1.8, 0.3]} />
        <meshStandardMaterial color="#ff6b6b" />
      </mesh>
    );
  }

  // Show loading state
  if (isLoading || !isLoaded) {
    return (
      <mesh position={position}>
        <boxGeometry args={[0.5, 1.8, 0.3]} />
        <meshStandardMaterial color="#74c0fc" wireframe />
      </mesh>
    );
  }

  return (
    <group ref={groupRef} position={position} rotation={rotation} scale={scale}>
      <primitive object={scene} />
    </group>
  );
}

// Helper function to apply avatar appearance customizations
function applyAvatarAppearance(mesh: SkinnedMesh, appearance: any) {
  // This would be expanded based on your specific model structure
  // For now, we'll just apply basic color changes
  
  if (mesh.material && 'color' in mesh.material) {
    // Apply skin tone
    if (mesh.name.includes('skin') || mesh.name.includes('face')) {
      (mesh.material as any).color.setHex(appearance.skinTone?.replace('#', '0x') || 0xFDBCB4);
    }
    
    // Apply hair color
    if (mesh.name.includes('hair')) {
      (mesh.material as any).color.setHex(appearance.hairColor?.replace('#', '0x') || 0x8B4513);
    }
  }
}

// Helper function to find morph targets for lip-sync
function findMorphTargets(scene: Group) {
  let morphTargets: any = null;
  
  scene.traverse((child) => {
    if (child instanceof SkinnedMesh && child.morphTargetInfluences) {
      morphTargets = child;
    }
  });
  
  return morphTargets;
}

// Helper function to apply viseme to morph targets
function applyViseme(morphTargets: SkinnedMesh, viseme: any) {
  if (!morphTargets.morphTargetInfluences) return;

  // Reset all morph targets
  for (let i = 0; i < morphTargets.morphTargetInfluences.length; i++) {
    morphTargets.morphTargetInfluences[i] = 0;
  }

  // Apply viseme-specific morph target
  // This mapping would depend on your specific model's morph targets
  const visemeMapping: Record<string, number> = {
    'A': 0,  // Open mouth
    'E': 1,  // Smile
    'I': 2,  // Small opening
    'O': 3,  // Round mouth
    'U': 4,  // Pucker
    'sil': 5 // Silence/closed
  };

  const morphIndex = visemeMapping[viseme.value];
  if (morphIndex !== undefined && morphTargets.morphTargetInfluences[morphIndex] !== undefined) {
    morphTargets.morphTargetInfluences[morphIndex] = viseme.weight || 1;
  }
}

// Available animations for the teacher model
export const teacherAnimations = [
  { name: 'idle', label: 'Idle', description: 'Default standing pose' },
  { name: 'greeting', label: 'Greeting', description: 'Wave hello' },
  { name: 'explaining', label: 'Explaining', description: 'Teaching gesture' },
  { name: 'pointing', label: 'Pointing', description: 'Point to blackboard' },
  { name: 'thinking', label: 'Thinking', description: 'Contemplative pose' },
  { name: 'nodding', label: 'Nodding', description: 'Agreement gesture' },
  { name: 'writing', label: 'Writing', description: 'Writing on blackboard' },
] as const;

// Preload the default model
useGLTF.preload('/models/teacher-avatar.glb');
