exports.id=192,exports.ids=[192],exports.modules={2830:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},22677:(a,b,c)=>{Promise.resolve().then(c.bind(c,88894))},26977:(a,b,c)=>{Promise.resolve().then(c.bind(c,73035))},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(4780);let i=(0,g.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},30909:(a,b,c)=>{"use strict";c.d(b,{n9:()=>i,qe:()=>h});var d=c(91639),e=c.n(d),f=c(89895),g=c.n(f);let h=`${e().variable} ${g().variable}`,i={sans:e().style.fontFamily,mono:g().style.fontFamily}},32405:(a,b,c)=>{Promise.resolve().then(c.bind(c,54676))},38646:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s});var d=c(60687),e=c(82136),f=c(16189);c(43210);var g=c(41862);function h({children:a,allowedRoles:b=[],redirectTo:c="/sign-in"}){let{data:h,status:i}=(0,e.useSession)();return((0,f.useRouter)(),"loading"===i)?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(g.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):h&&(!(b.length>0)||b.includes(h.user.role))?(0,d.jsx)(d.Fragment,{children:a}):null}var i=c(29523),j=c(82080),k=c(58887),l=c(84027),m=c(41312),n=c(53411),o=c(97051),p=c(27351),q=c(58869),r=c(40083);function s({children:a}){let{data:b}=(0,e.useSession)(),c=(0,f.useRouter)(),g=async()=>{await (0,e.signOut)({callbackUrl:"/sign-in"})};return(0,d.jsx)(h,{allowedRoles:["student","teacher","hod","admin"],children:(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(p.A,{className:"h-8 w-8 text-blue-600"}),(0,d.jsx)("span",{className:"ml-2 text-xl font-bold text-gray-900",children:"AI Tutor Platform"})]})}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(q.A,{className:"h-5 w-5 text-gray-500"}),(0,d.jsx)("span",{className:"text-sm text-gray-700",children:b?.user?.name}),(0,d.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full capitalize",children:b?.user?.role})]}),(0,d.jsxs)(i.$,{variant:"outline",size:"sm",onClick:g,className:"flex items-center space-x-2",children:[(0,d.jsx)(r.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Sign Out"})]})]})]})})}),(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("nav",{className:"w-64 bg-white shadow-sm min-h-screen",children:(0,d.jsx)("div",{className:"p-4",children:(0,d.jsx)("ul",{className:"space-y-2",children:(()=>{let a=b?.user?.role,c=[{icon:j.A,label:"Subjects",href:`/${a}`}];switch(a){case"student":return[...c,{icon:k.A,label:"AI Chat",href:"/student/chat"}];case"teacher":return[...c,{icon:k.A,label:"AI Chat",href:"/teacher/chat"},{icon:l.A,label:"Avatar Setup",href:"/teacher/avatar"}];case"hod":return[...c,{icon:m.A,label:"Teachers",href:"/hod/teachers"},{icon:n.A,label:"Analytics",href:"/hod/analytics"},{icon:o.A,label:"Announcements",href:"/hod/announcements"}];case"admin":return[...c,{icon:m.A,label:"Users",href:"/admin/users"},{icon:n.A,label:"Analytics",href:"/admin/analytics"},{icon:o.A,label:"Announcements",href:"/admin/announcements"},{icon:l.A,label:"Settings",href:"/admin/settings"}];default:return c}})().map(a=>(0,d.jsx)("li",{children:(0,d.jsxs)("button",{onClick:()=>c.push(a.href),className:"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,d.jsx)(a.icon,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:a.label})]})},a.href))})})}),(0,d.jsx)("main",{className:"flex-1 p-6",children:a})]})]})})}},40129:(a,b,c)=>{Promise.resolve().then(c.bind(c,38646))},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},54676:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI\\\\ai\\\\frontend\\\\src\\\\components\\\\auth\\\\AuthProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\AuthProvider.tsx","default")},59645:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/svg+xml",sizes:"any",url:(0,d.fillMetadataSegment)(".",await a.params,"icon.svg")+"?422a85f867f20be2"}]},61135:()=>{},65878:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},73035:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI\\\\ai\\\\frontend\\\\src\\\\app\\\\(dash)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx","default")},88894:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(60687),e=c(82136);function f({children:a}){return(0,d.jsx)(e.SessionProvider,{children:a})}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(37413);c(61135);var e=c(54676),f=c(30909);let g={title:"AI Tutor Platform",description:"Personalized AI-powered learning experience",icons:{icon:"/icon.svg",shortcut:"/icon.svg",apple:"/icon.svg"}};function h({children:a}){return(0,d.jsx)("html",{lang:"en","data-scroll-behavior":"smooth",children:(0,d.jsx)("body",{className:`${f.qe} antialiased`,children:(0,d.jsx)(e.default,{children:a})})})}}};