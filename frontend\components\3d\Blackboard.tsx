'use client';

import { useRef } from 'react';
import { Mesh } from 'three';
import { useFrame } from '@react-three/fiber';

interface BlackboardProps {
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
}

export function Blackboard({ 
  position = [0, 2, -2], 
  rotation = [0, 0, 0], 
  scale = 1 
}: BlackboardProps) {
  const boardRef = useRef<Mesh>(null);
  const frameRef = useRef<Mesh>(null);

  useFrame((state) => {
    // Subtle animation for the blackboard
    if (boardRef.current) {
      boardRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5) * 0.01;
    }
  });

  return (
    <group position={position} rotation={rotation} scale={scale}>
      {/* Blackboard Frame */}
      <mesh ref={frameRef} castShadow>
        <boxGeometry args={[3.2, 2.2, 0.1]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* Blackboard Surface */}
      <mesh ref={boardRef} position={[0, 0, 0.06]} castShadow>
        <planeGeometry args={[3, 2]} />
        <meshStandardMaterial 
          color="#1a4d3a" 
          roughness={0.8}
          metalness={0.1}
        />
      </mesh>

      {/* Chalk Tray */}
      <mesh position={[0, -1, 0.1]} castShadow>
        <boxGeometry args={[3, 0.1, 0.15]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* Chalk Pieces */}
      <mesh position={[-1, -0.95, 0.15]} castShadow>
        <cylinderGeometry args={[0.02, 0.02, 0.2]} />
        <meshStandardMaterial color="#ffffff" />
      </mesh>
      
      <mesh position={[-0.7, -0.95, 0.15]} castShadow>
        <cylinderGeometry args={[0.02, 0.02, 0.15]} />
        <meshStandardMaterial color="#ffff88" />
      </mesh>

      {/* Eraser */}
      <mesh position={[1, -0.95, 0.15]} castShadow>
        <boxGeometry args={[0.15, 0.05, 0.08]} />
        <meshStandardMaterial color="#333333" />
      </mesh>
    </group>
  );
}
