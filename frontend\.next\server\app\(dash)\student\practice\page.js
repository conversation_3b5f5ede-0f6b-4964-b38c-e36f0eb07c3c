(()=>{var a={};a.id=700,a.ids=[700],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11273:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(43210),e=c(60687);function f(a,b=[]){let c=[],g=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return g.scopeName=a,[function(b,f){let g=d.createContext(f),h=c.length;c=[...c,f];let i=b=>{let{scope:c,children:f,...i}=b,j=c?.[a]?.[h]||g,k=d.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=d.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(g,...b)]}},14163:(a,b,c)=>{"use strict";c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},51330:(a,b,c)=>{Promise.resolve().then(c.bind(c,73962))},62185:(a,b,c)=>{"use strict";c.d(b,{Pt:()=>f,jy:()=>g});let d="http://localhost:3001";async function e(a,b={}){try{let c=await fetch(`${d}${a}`,{headers:{"Content-Type":"application/json",...b.headers},...b});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(a){return console.error("API request failed:",a),{success:!1,error:a instanceof Error?a.message:"Unknown error occurred"}}}let f={sendMessage:async function*(a,b){let c=await fetch(`${d}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:a,subjectId:b})});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);let e=c.body?.getReader();if(!e)throw Error("No response body");let f=new TextDecoder,g="";try{for(;;){let{done:a,value:b}=await e.read();if(a)break;let c=(g+=f.decode(b,{stream:!0})).split("\n");for(let a of(g=c.pop()||"",c))if(a.startsWith("data: ")){let b=a.slice(6);if("[DONE]"===b)return;try{let a=JSON.parse(b);yield a}catch(a){console.error("Failed to parse SSE data:",a)}}}}finally{e.releaseLock()}},getChatHistory:a=>e(`/chat/history${a?`?subjectId=${a}`:""}`)},g={user:{id:"1",name:"John Doe",email:"<EMAIL>",role:"student",orgId:"org1",createdAt:new Date,updatedAt:new Date},subjects:[{id:"1",name:"Japanese Language",description:"Learn Japanese with AI assistance",teacherId:"teacher1",teacherName:"Tanaka Sensei",orgId:"org1",createdAt:new Date,updatedAt:new Date},{id:"2",name:"Mathematics",description:"Advanced mathematics concepts",teacherId:"teacher2",teacherName:"Smith Sensei",orgId:"org1",createdAt:new Date,updatedAt:new Date}],messages:[{id:"1",content:"Hello! How can I help you today?",role:"assistant",timestamp:new Date,subjectId:"1"},{id:"2",content:"Can you explain the difference between は and が?",role:"user",timestamp:new Date,subjectId:"1"}]}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64293:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>N});var d=c(60687),e=c(43210),f=c(16189),g=c(82136),h=c(44493),i=c(29523),j=c(96834),k=c(11273),l=c(14163),m="Progress",[n,o]=(0,k.A)(m),[p,q]=n(m),r=e.forwardRef((a,b)=>{var c,e;let{__scopeProgress:f,value:g=null,max:h,getValueLabel:i=u,...j}=a;(h||0===h)&&!x(h)&&console.error((c=`${h}`,`Invalid prop \`max\` of value \`${c}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let k=x(h)?h:100;null===g||y(g,k)||console.error((e=`${g}`,`Invalid prop \`value\` of value \`${e}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=y(g,k)?g:null,n=w(m)?i(m,k):void 0;return(0,d.jsx)(p,{scope:f,value:m,max:k,children:(0,d.jsx)(l.sG.div,{"aria-valuemax":k,"aria-valuemin":0,"aria-valuenow":w(m)?m:void 0,"aria-valuetext":n,role:"progressbar","data-state":v(m,k),"data-value":m??void 0,"data-max":k,...j,ref:b})})});r.displayName=m;var s="ProgressIndicator",t=e.forwardRef((a,b)=>{let{__scopeProgress:c,...e}=a,f=q(s,c);return(0,d.jsx)(l.sG.div,{"data-state":v(f.value,f.max),"data-value":f.value??void 0,"data-max":f.max,...e,ref:b})});function u(a,b){return`${Math.round(a/b*100)}%`}function v(a,b){return null==a?"indeterminate":a===b?"complete":"loading"}function w(a){return"number"==typeof a}function x(a){return w(a)&&!isNaN(a)&&a>0}function y(a,b){return w(a)&&!isNaN(a)&&a<=b&&a>=0}t.displayName=s;var z=c(4780);let A=e.forwardRef(({className:a,value:b,...c},e)=>(0,d.jsx)(r,{ref:e,className:(0,z.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...c,children:(0,d.jsx)(t,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(b||0)}%)`}})}));A.displayName=r.displayName;var B=c(62688);let C=(0,B.A)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]),D=(0,B.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var E=c(82080),F=c(97840);let G=(0,B.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var H=c(28559);let I=(0,B.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var J=c(48730);let K=(0,B.A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var L=c(90631),M=c(62185);function N(){let a=(0,f.useRouter)(),b=(0,f.useSearchParams)().get("subject"),{data:c}=(0,g.useSession)();console.log("User session:",c);let k=(0,L.fR)(),[l,m]=(0,e.useState)(!0),n=(k.length>0?k:M.jy.subjects).find(a=>a.id===b),o=[{id:"1",title:"Hiragana Recognition",description:"Practice identifying hiragana characters",difficulty:"beginner",estimatedTime:15,questionsCount:20,completedCount:12,subjectId:"1",subjectName:"Japanese Language",type:"flashcards"},{id:"2",title:"Basic Grammar Quiz",description:"Test your understanding of basic Japanese grammar patterns",difficulty:"intermediate",estimatedTime:25,questionsCount:15,completedCount:0,subjectId:"1",subjectName:"Japanese Language",type:"quiz"},{id:"3",title:"Listening Comprehension",description:"Practice understanding spoken Japanese",difficulty:"intermediate",estimatedTime:30,questionsCount:10,completedCount:5,subjectId:"1",subjectName:"Japanese Language",type:"listening"},{id:"4",title:"Algebra Problem Solving",description:"Practice solving algebraic equations",difficulty:"beginner",estimatedTime:20,questionsCount:12,completedCount:12,subjectId:"2",subjectName:"Mathematics",type:"quiz"}],p=b?o.filter(a=>a.subjectId===b):o;return l?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Loading practice sessions..."})]})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>a.push("/student"),children:[(0,d.jsx)(H.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,d.jsx)(G,{className:"h-5 w-5 text-orange-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold",children:"Practice Sessions"}),(0,d.jsx)("p",{className:"text-gray-600",children:n?`Practice ${n.name} skills`:"Improve your skills with interactive practice"})]})]})]})}),n&&(0,d.jsx)(h.Zp,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(E.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-blue-900",children:n.name}),(0,d.jsxs)("p",{className:"text-sm text-blue-700",children:[p.length," practice session",1!==p.length?"s":""," available"]})]})]}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>a.push("/student/practice"),children:"View All Subjects"})]})})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map(a=>{let b=a.completedCount/a.questionsCount*100,c=a.completedCount===a.questionsCount;return(0,d.jsxs)(h.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(a=>{switch(a){case"quiz":return(0,d.jsx)(C,{className:"h-5 w-5 text-blue-600"});case"flashcards":return(0,d.jsx)(D,{className:"h-5 w-5 text-purple-600"});case"writing":return(0,d.jsx)(E.A,{className:"h-5 w-5 text-green-600"});case"listening":return(0,d.jsx)(F.A,{className:"h-5 w-5 text-orange-600"});default:return(0,d.jsx)(G,{className:"h-5 w-5 text-gray-600"})}})(a.type),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)(h.ZB,{className:"text-lg",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,d.jsx)(j.E,{variant:"secondary",className:"text-xs",children:(a=>{switch(a){case"quiz":return"Quiz";case"flashcards":return"Flashcards";case"writing":return"Writing";case"listening":return"Listening";default:return"Practice"}})(a.type)}),(0,d.jsx)(j.E,{className:`text-xs ${(a=>{switch(a){case"beginner":return"bg-green-100 text-green-800";case"intermediate":return"bg-yellow-100 text-yellow-800";case"advanced":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(a.difficulty)}`,children:a.difficulty})]})]})]}),c&&(0,d.jsx)(I,{className:"h-5 w-5 text-green-600"})]}),(0,d.jsx)(h.BT,{className:"line-clamp-2",children:a.description})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Progress"}),(0,d.jsxs)("span",{className:"font-medium",children:[a.completedCount,"/",a.questionsCount]})]}),(0,d.jsx)(A,{value:b,className:"h-2"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(J.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:[a.estimatedTime," min"]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(E.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.subjectName})]})]}),(0,d.jsx)(i.$,{onClick:()=>{console.log("Starting practice session:",a.id),alert("Practice session would start here!")},className:"w-full",variant:c?"outline":"default",children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(K,{className:"h-4 w-4 mr-2"}),"Review"]}):a.completedCount>0?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Continue"]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Start Practice"]})})]})})]},a.id)})}),0===p.length&&(0,d.jsx)(h.Zp,{children:(0,d.jsxs)(h.Wu,{className:"text-center py-12",children:[(0,d.jsx)(G,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No practice sessions available"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:n?`No practice sessions are available for ${n.name} yet.`:"No practice sessions are available yet."}),(0,d.jsx)(i.$,{variant:"outline",onClick:()=>a.push("/student"),children:"Back to Dashboard"})]})})]})}},73962:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI\\\\ai\\\\frontend\\\\src\\\\app\\\\(dash)\\\\student\\\\practice\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\practice\\page.tsx","default")},82794:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dash)",{children:["student",{children:["practice",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,73962)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\practice\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,73035)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a),async a=>(await Promise.resolve().then(c.bind(c,59645))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a),async a=>(await Promise.resolve().then(c.bind(c,59645))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\practice\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dash)/student/practice/page",pathname:"/student/practice",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dash)/student/practice/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},85834:(a,b,c)=>{Promise.resolve().then(c.bind(c,64293))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90631:(a,b,c)=>{"use strict";c.d(b,{R9:()=>n,fR:()=>h,st:()=>i});var d=c(98483),e=c(59350),f=c(62185);let g=(0,d.v)()((0,e.lt)((0,e.Zr)(a=>({user:void 0,subjects:[],materials:[],announcements:[],analytics:void 0,setUser:b=>{a({user:b},!1,"setUser")},setSubjects:b=>{a({subjects:b},!1,"setSubjects")},setMaterials:b=>{a({materials:b},!1,"setMaterials")},setAnnouncements:b=>{a({announcements:b},!1,"setAnnouncements")},setAnalytics:b=>{a({analytics:b},!1,"setAnalytics")}}),{name:"user-store",partialize:a=>({user:a.user,subjects:a.subjects,announcements:a.announcements})}),{name:"user-store"})),h=()=>g(a=>a.subjects),i=()=>g(a=>a.analytics),j=async()=>{try{return console.log("Loading subjects"),g.getState().setSubjects(f.jy.subjects),{success:!0}}catch(a){return console.error("Failed to load subjects:",a),{success:!1,error:"Failed to load subjects"}}},k=async()=>{try{return console.log("Loading announcements"),g.getState().setAnnouncements([]),{success:!0}}catch(a){return console.error("Failed to load announcements:",a),{success:!1,error:"Failed to load announcements"}}},l=async a=>{if(!a||"hod"!==a&&"admin"!==a)return{success:!1,error:"Unauthorized"};try{return console.log("Loading analytics"),g.getState().setAnalytics({totalUsers:150,totalStudents:120,totalTeachers:25,totalSubjects:15,totalMaterials:85,totalChats:1250,activeUsersToday:45,activeUsersThisWeek:98,popularSubjects:[{subjectId:"1",subjectName:"Japanese Language",chatCount:450},{subjectId:"2",subjectName:"Mathematics",chatCount:320}],userGrowth:[{date:"2024-01-01",count:100},{date:"2024-02-01",count:120},{date:"2024-03-01",count:150}]}),{success:!0}}catch(a){return console.error("Failed to load analytics:",a),{success:!1,error:"Failed to load analytics"}}},m=async a=>{let b=(await Promise.allSettled([j(),k(),..."hod"===a||"admin"===a?[l(a)]:[]])).filter(a=>"rejected"===a.status);return b.length>0&&console.error("Some data failed to load:",b),{success:0===b.length}},n={loadSubjects:j,loadMaterials:async a=>{try{return console.log("Loading materials for subject:",a),g.getState().setMaterials([]),{success:!0}}catch(a){return console.error("Failed to load materials:",a),{success:!1,error:"Failed to load materials"}}},loadAnnouncements:k,loadAnalytics:l,loadAllData:m}},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},97840:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]])}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,653,393,484,192],()=>b(b.s=82794));module.exports=c})();