(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[40],{793:(e,s,a)=>{Promise.resolve().then(a.bind(a,5762))},4796:(e,s,a)=>{"use strict";a.d(s,{Gs:()=>d,H:()=>o,LC:()=>m,oh:()=>n,ot:()=>i,v2:()=>x});var t=a(5521),r=a(6786),l=a(5731);let c=(0,t.v)()((0,r.lt)((0,r.Zr)(e=>({messages:[],isLoading:!1,isStreaming:!1,currentSubject:void 0,addMessage:s=>{let a={...s,id:crypto.randomUUID(),timestamp:new Date};e(e=>({messages:[...e.messages,a]}),!1,"addMessage")},updateLastMessage:s=>{e(e=>{let a=[...e.messages],t=a.length-1;return t>=0&&(a[t]={...a[t],...s}),{messages:a}},!1,"updateLastMessage")},setLoading:s=>{e({isLoading:s},!1,"setLoading")},setStreaming:s=>{e({isStreaming:s},!1,"setStreaming")},setCurrentSubject:s=>{e({currentSubject:s},!1,"setCurrentSubject")},clearMessages:()=>{e({messages:[]},!1,"clearMessages")}}),{name:"chat-store",partialize:e=>({messages:e.messages,currentSubject:e.currentSubject})}),{name:"chat-store"})),i=()=>c(e=>e.messages),n=()=>c(e=>e.isLoading),d=()=>c(e=>e.isStreaming),o=()=>c(e=>e.currentSubject),x=()=>c(e=>e.setCurrentSubject),m=()=>{let e=c(e=>e.addMessage),s=c(e=>e.updateLastMessage),a=c(e=>e.setLoading),t=c(e=>e.setStreaming),r=o(),i=async c=>{if(c.trim()){e({content:c.trim(),role:"user",subjectId:null==r?void 0:r.id}),e({content:"",role:"assistant",subjectId:null==r?void 0:r.id}),a(!0),t(!0);try{let e="";for await(let a of l.Pt.sendMessage(c,null==r?void 0:r.id))if("text"===a.type)e+=a.content,s({content:e});else if("audio"===a.type&&a.audioUrl)s({hasAudio:!0,audioUrl:a.audioUrl,visemes:a.visemes});else if("complete"===a.type){s({content:e+a.content});break}}catch(e){console.error("Failed to send message:",e),s({content:"Sorry, I encountered an error. Please try again."})}finally{a(!1),t(!1)}}},n=async()=>{let e=c.getState().messages,s=[...e].reverse().find(e=>"user"===e.role);if(!s)return;let a=e.filter((s,a)=>a!==e.length-1||"assistant"!==e[e.length-1].role);c.setState({messages:a}),await i(s.content)};return{sendMessage:i,regenerateLastResponse:n,loadChatHistory:async e=>{a(!0);try{console.log("Loading chat history for subject:",e)}catch(e){console.error("Failed to load chat history:",e)}finally{a(!1)}}}}},5762:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(5155),r=a(2115),l=a(2108),c=a(5695),i=a(6695),n=a(285),d=a(6126),o=a(5040),x=a(1497),m=a(4186),h=a(1007),u=a(7434),j=a(5690),g=a(7395),p=a(693),N=a(4796),v=a(5731);function b(){var e;let{data:s}=(0,l.useSession)(),a=(0,c.useRouter)(),b=(0,p.fR)(),f=(0,N.H)(),y=(0,N.v2)(),[w,S]=(0,r.useState)(!0),[A,C]=(0,r.useState)(!1);(0,r.useEffect)(()=>{A||(async()=>{await p.R9.loadSubjects(),S(!1),C(!0)})()},[A]);let k=e=>{y(e),a.push("/student/chat")},L=e=>{a.push("/student/materials?subject=".concat(e))};return w?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading your subjects..."})]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white",children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold mb-2",children:["Welcome back, ",null==s||null==(e=s.user)?void 0:e.name,"!"]}),(0,t.jsx)("p",{className:"text-blue-100",children:"Ready to continue your learning journey? Choose a subject to start chatting with your AI tutor."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Enrolled Subjects"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:b.length})]})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,t.jsx)(x.A,{className:"h-5 w-5 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Chat Sessions"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:"12"})]})]})})}),(0,t.jsx)(i.Zp,{children:(0,t.jsx)(i.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Study Time"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:"24h"})]})]})})})]}),f&&(0,t.jsxs)(i.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("span",{children:"Continue Learning"})]}),(0,t.jsxs)(i.BT,{children:["You were last studying ",f.name]})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:f.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["with ",f.teacherName]})]})]}),(0,t.jsxs)(n.$,{onClick:()=>k(f),children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Continue Chat"]})]})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Your Subjects"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:b.length>0?b.map(e=>(0,t.jsxs)(i.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-lg",children:e.name}),(0,t.jsx)(d.E,{variant:"secondary",children:"Active"})]}),(0,t.jsx)(i.BT,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.teacherName})]})})]}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:e.description||"Enhance your knowledge with AI-powered tutoring."}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(n.$,{onClick:()=>k(e),className:"w-full",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Start AI Chat"]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>L(e.id),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"Materials"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>a.push("/student/practice?subject=".concat(e.id)),children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-1"}),"Practice"]})]})]})]})]},e.id)):v.jy.subjects.map(e=>(0,t.jsxs)(i.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-lg",children:e.name}),(0,t.jsx)(d.E,{variant:"secondary",children:"Active"})]}),(0,t.jsx)(i.BT,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.teacherName})]})})]}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:e.description||"Enhance your knowledge with AI-powered tutoring."}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(n.$,{onClick:()=>k(e),className:"w-full",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Start AI Chat"]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>L(e.id),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"Materials"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>a.push("/student/practice?subject=".concat(e.id)),children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-1"}),"Practice"]})]})]})]})]},e.id))})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Recent Activity"}),(0,t.jsx)(i.BT,{children:"Your latest learning sessions"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:"Japanese Language Chat"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Discussed grammar patterns - 2 hours ago"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:"Pronunciation Practice"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Completed voice exercises - 1 day ago"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-purple-600"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:"Study Material Review"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Read Chapter 5 notes - 2 days ago"})]})]})]})})]})]})}},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>i});var t=a(5155);a(2115);var r=a(2085),l=a(9434);let c=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:(0,l.cn)(c({variant:a}),s),...r})}}},e=>{e.O(0,[817,108,300,747,441,964,358],()=>e(e.s=793)),_N_E=e.O()}]);