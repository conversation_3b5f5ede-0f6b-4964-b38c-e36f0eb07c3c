"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[747],{285:(e,t,a)=>{a.d(t,{$:()=>i});var r=a(5155),s=a(2115),n=a(9708),o=a(2085),c=a(9434);let d=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=s.forwardRef((e,t)=>{let{className:a,variant:s,size:o,asChild:i=!1,...l}=e,u=i?n.DX:"button";return(0,r.jsx)(u,{className:(0,c.cn)(d({variant:s,size:o,className:a})),ref:t,...l})});i.displayName="Button"},693:(e,t,a)=>{a.d(t,{R9:()=>m,fR:()=>c,st:()=>d});var r=a(5521),s=a(6786),n=a(5731);let o=(0,r.v)()((0,s.lt)((0,s.Zr)(e=>({user:void 0,subjects:[],materials:[],announcements:[],analytics:void 0,setUser:t=>{e({user:t},!1,"setUser")},setSubjects:t=>{e({subjects:t},!1,"setSubjects")},setMaterials:t=>{e({materials:t},!1,"setMaterials")},setAnnouncements:t=>{e({announcements:t},!1,"setAnnouncements")},setAnalytics:t=>{e({analytics:t},!1,"setAnalytics")}}),{name:"user-store",partialize:e=>({user:e.user,subjects:e.subjects,announcements:e.announcements})}),{name:"user-store"})),c=()=>o(e=>e.subjects),d=()=>o(e=>e.analytics),i=async()=>{try{return console.log("Loading subjects"),o.getState().setSubjects(n.jy.subjects),{success:!0}}catch(e){return console.error("Failed to load subjects:",e),{success:!1,error:"Failed to load subjects"}}},l=async()=>{try{return console.log("Loading announcements"),o.getState().setAnnouncements([]),{success:!0}}catch(e){return console.error("Failed to load announcements:",e),{success:!1,error:"Failed to load announcements"}}},u=async e=>{if(!e||"hod"!==e&&"admin"!==e)return{success:!1,error:"Unauthorized"};try{return console.log("Loading analytics"),o.getState().setAnalytics({totalUsers:150,totalStudents:120,totalTeachers:25,totalSubjects:15,totalMaterials:85,totalChats:1250,activeUsersToday:45,activeUsersThisWeek:98,popularSubjects:[{subjectId:"1",subjectName:"Japanese Language",chatCount:450},{subjectId:"2",subjectName:"Mathematics",chatCount:320}],userGrowth:[{date:"2024-01-01",count:100},{date:"2024-02-01",count:120},{date:"2024-03-01",count:150}]}),{success:!0}}catch(e){return console.error("Failed to load analytics:",e),{success:!1,error:"Failed to load analytics"}}},f=async e=>{let t=(await Promise.allSettled([i(),l(),..."hod"===e||"admin"===e?[u(e)]:[]])).filter(e=>"rejected"===e.status);return t.length>0&&console.error("Some data failed to load:",t),{success:0===t.length}},m={loadSubjects:i,loadMaterials:async e=>{try{return console.log("Loading materials for subject:",e),o.getState().setMaterials([]),{success:!0}}catch(e){return console.error("Failed to load materials:",e),{success:!1,error:"Failed to load materials"}}},loadAnnouncements:l,loadAnalytics:u,loadAllData:f}},5731:(e,t,a)=>{a.d(t,{Pt:()=>n,jy:()=>o});let r="http://localhost:3001";async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let a=await fetch("".concat(r).concat(e),{headers:{"Content-Type":"application/json",...t.headers},...t});if(!a.ok)throw Error("HTTP error! status: ".concat(a.status));return await a.json()}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error occurred"}}}let n={sendMessage:async function*(e,t){var a;let s=await fetch("".concat(r,"/chat"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e,subjectId:t})});if(!s.ok)throw Error("HTTP error! status: ".concat(s.status));let n=null==(a=s.body)?void 0:a.getReader();if(!n)throw Error("No response body");let o=new TextDecoder,c="";try{for(;;){let{done:e,value:t}=await n.read();if(e)break;let a=(c+=o.decode(t,{stream:!0})).split("\n");for(let e of(c=a.pop()||"",a))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)return;try{let e=JSON.parse(t);yield e}catch(e){console.error("Failed to parse SSE data:",e)}}}}finally{n.releaseLock()}},getChatHistory:e=>s("/chat/history".concat(e?"?subjectId=".concat(e):""))},o={user:{id:"1",name:"John Doe",email:"<EMAIL>",role:"student",orgId:"org1",createdAt:new Date,updatedAt:new Date},subjects:[{id:"1",name:"Japanese Language",description:"Learn Japanese with AI assistance",teacherId:"teacher1",teacherName:"Tanaka Sensei",orgId:"org1",createdAt:new Date,updatedAt:new Date},{id:"2",name:"Mathematics",description:"Advanced mathematics concepts",teacherId:"teacher2",teacherName:"Smith Sensei",orgId:"org1",createdAt:new Date,updatedAt:new Date}],messages:[{id:"1",content:"Hello! How can I help you today?",role:"assistant",timestamp:new Date,subjectId:"1"},{id:"2",content:"Can you explain the difference between は and が?",role:"user",timestamp:new Date,subjectId:"1"}]}},6695:(e,t,a)=>{a.d(t,{BT:()=>i,Wu:()=>l,ZB:()=>d,Zp:()=>o,aR:()=>c});var r=a(5155),s=a(2115),n=a(9434);let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});o.displayName="Card";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...s})});c.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});d.displayName="CardTitle";let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...s})});i.displayName="CardDescription";let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...s})});l.displayName="CardContent",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...s})}).displayName="CardFooter"},9434:(e,t,a)=>{a.d(t,{cn:()=>n});var r=a(2596),s=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}}]);