'use client';

// TeacherModel component with lip-sync animation and customizable appearance

import { useRef, useEffect, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { useGLTF, useAnimations } from '@react-three/drei';
import * as THREE from 'three';
import { 
  useAvatarConfig, 
  useIsSpeaking, 
  useCurrentVisemes,
  useAITeacherActions 
} from '@/store/useAITeacher';

interface TeacherModelProps {
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
}

// Viseme to blend shape mapping for lip-sync
const VISEME_MAPPING: Record<string, string> = {
  'sil': 'viseme_sil',     // Silence
  'PP': 'viseme_PP',       // P, B, M
  'FF': 'viseme_FF',       // F, V
  'TH': 'viseme_TH',       // TH
  'DD': 'viseme_DD',       // T, D
  'kk': 'viseme_kk',       // K, G
  'CH': 'viseme_CH',       // CH, J, SH
  'SS': 'viseme_SS',       // S, Z
  'nn': 'viseme_nn',       // N, L
  'RR': 'viseme_RR',       // R
  'aa': 'viseme_aa',       // AA (father)
  'E': 'viseme_E',         // E (bed)
  'I': 'viseme_I',         // I (bit)
  'O': 'viseme_O',         // O (boat)
  'U': 'viseme_U',         // U (book)
};

// Default teacher model (placeholder - replace with actual GLB model)
function DefaultTeacherGeometry() {
  return (
    <group>
      {/* Head */}
      <mesh position={[0, 1.6, 0]} castShadow>
        <sphereGeometry args={[0.15, 32, 32]} />
        <meshStandardMaterial color="#FDBCB4" />
      </mesh>
      
      {/* Body */}
      <mesh position={[0, 1, 0]} castShadow>
        <cylinderGeometry args={[0.2, 0.25, 0.8]} />
        <meshStandardMaterial color="#4A90E2" />
      </mesh>
      
      {/* Arms */}
      <mesh position={[-0.3, 1.2, 0]} rotation={[0, 0, 0.3]} castShadow>
        <cylinderGeometry args={[0.05, 0.05, 0.6]} />
        <meshStandardMaterial color="#FDBCB4" />
      </mesh>
      <mesh position={[0.3, 1.2, 0]} rotation={[0, 0, -0.3]} castShadow>
        <cylinderGeometry args={[0.05, 0.05, 0.6]} />
        <meshStandardMaterial color="#FDBCB4" />
      </mesh>
      
      {/* Legs */}
      <mesh position={[-0.1, 0.2, 0]} castShadow>
        <cylinderGeometry args={[0.08, 0.08, 0.8]} />
        <meshStandardMaterial color="#2C3E50" />
      </mesh>
      <mesh position={[0.1, 0.2, 0]} castShadow>
        <cylinderGeometry args={[0.08, 0.08, 0.8]} />
        <meshStandardMaterial color="#2C3E50" />
      </mesh>
      
      {/* Eyes */}
      <mesh position={[-0.05, 1.65, 0.12]} castShadow>
        <sphereGeometry args={[0.02, 16, 16]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
      <mesh position={[0.05, 1.65, 0.12]} castShadow>
        <sphereGeometry args={[0.02, 16, 16]} />
        <meshStandardMaterial color="#000000" />
      </mesh>
      
      {/* Mouth (for lip-sync) */}
      <mesh position={[0, 1.55, 0.12]} castShadow>
        <sphereGeometry args={[0.015, 16, 16]} />
        <meshStandardMaterial color="#8B0000" />
      </mesh>
    </group>
  );
}

// GLB Model component (when actual model is available)
function GLBTeacherModel({ url, ...props }: { url: string } & TeacherModelProps) {
  const group = useRef<THREE.Group>(null);
  const { scene, animations } = useGLTF(url);
  const { actions, mixer } = useAnimations(animations, group);
  
  const isSpeaking = useIsSpeaking();
  const currentVisemes = useCurrentVisemes();
  const avatarConfig = useAvatarConfig();
  
  // Clone the scene to avoid sharing between instances
  const clonedScene = useMemo(() => scene.clone(), [scene]);
  
  // Apply avatar customizations
  useEffect(() => {
    if (avatarConfig && clonedScene) {
      clonedScene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          // Apply appearance customizations
          if (child.name.includes('hair')) {
            child.material = new THREE.MeshStandardMaterial({
              color: avatarConfig.appearance.hairColor
            });
          } else if (child.name.includes('skin')) {
            child.material = new THREE.MeshStandardMaterial({
              color: avatarConfig.appearance.skinTone
            });
          } else if (child.name.includes('clothing')) {
            child.material = new THREE.MeshStandardMaterial({
              color: avatarConfig.appearance.clothing
            });
          }
        }
      });
    }
  }, [avatarConfig, clonedScene]);
  
  // Handle lip-sync animation
  useFrame((state, delta) => {
    if (mixer) mixer.update(delta);
    
    if (isSpeaking && currentVisemes.length > 0 && clonedScene) {
      const currentTime = state.clock.getElapsedTime() * 1000; // Convert to milliseconds
      
      // Find the current viseme based on time
      const currentViseme = currentVisemes.find((viseme, index) => {
        const nextViseme = currentVisemes[index + 1];
        return currentTime >= viseme.time && 
               (!nextViseme || currentTime < nextViseme.time);
      });
      
      if (currentViseme) {
        // Apply viseme to blend shapes
        clonedScene.traverse((child) => {
          if (child instanceof THREE.SkinnedMesh && child.morphTargetInfluences) {
            // Reset all viseme blend shapes
            Object.values(VISEME_MAPPING).forEach((blendShapeName) => {
              const index = child.morphTargetDictionary?.[blendShapeName];
              if (index !== undefined && child.morphTargetInfluences) {
                child.morphTargetInfluences[index] = 0;
              }
            });
            
            // Apply current viseme
            const blendShapeName = VISEME_MAPPING[currentViseme.viseme];
            if (blendShapeName) {
              const index = child.morphTargetDictionary?.[blendShapeName];
              if (index !== undefined && child.morphTargetInfluences) {
                child.morphTargetInfluences[index] = currentViseme.value;
              }
            }
          }
        });
      }
    }
  });
  
  // Idle animation
  useEffect(() => {
    if (actions.idle) {
      actions.idle.play();
    }
    
    return () => {
      if (actions.idle) {
        actions.idle.stop();
      }
    };
  }, [actions]);
  
  return (
    <group ref={group} {...props}>
      <primitive object={clonedScene} />
    </group>
  );
}

// Main TeacherModel component
export default function TeacherModel(props: TeacherModelProps) {
  const group = useRef<THREE.Group>(null);
  const avatarConfig = useAvatarConfig();
  const isSpeaking = useIsSpeaking();
  const { setModelLoaded } = useAITeacherActions();
  
  // Breathing animation for idle state
  useFrame((state) => {
    if (group.current && !isSpeaking) {
      const breathingScale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.02;
      group.current.scale.y = breathingScale;
    }
  });
  
  // Subtle swaying animation
  useFrame((state) => {
    if (group.current) {
      group.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.5) * 0.05;
    }
  });
  
  useEffect(() => {
    // Mark model as loaded after component mounts
    const timer = setTimeout(() => {
      setModelLoaded(true);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [setModelLoaded]);
  
  // Use GLB model if available, otherwise use default geometry
  const modelUrl = avatarConfig?.modelUrl;
  
  return (
    <group ref={group} {...props}>
      {modelUrl ? (
        <GLBTeacherModel url={modelUrl} />
      ) : (
        <DefaultTeacherGeometry />
      )}
    </group>
  );
}

// Preload the default model
// useGLTF.preload('/models/teacher-default.glb');
