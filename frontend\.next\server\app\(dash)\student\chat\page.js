(()=>{var a={};a.id=727,a.ids=[727],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(18224),g=c(78272),h=c(3589),i=c(13964),j=c(4780);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22304:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>L});var d=c(60687),e=c(43210),f=c(16189),g=c(44493),h=c(29523),i=c(96834),j=c(15079),k=c(28559),l=c(83753),m=c(82080),n=c(84027),o=c(58887),p=c(91391),q=c(62688);let r=(0,q.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),s=(0,q.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var t=c(58869),u=c(4780);function v({furigana:a}){return a&&0!==a.length?(0,d.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-blue-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Furigana Reading"})]}),(0,d.jsx)("div",{className:"space-y-2",children:a.map((a,b)=>(0,d.jsx)("div",{className:"inline-block mr-3 mb-2",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-xs text-blue-600 leading-none",children:a.reading}),(0,d.jsx)("div",{className:"text-lg font-medium text-gray-900",children:a.kanji}),a.meaning&&(0,d.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:a.meaning})]})},b))})]}):null}function w({grammar:a}){return a&&0!==a.length?(0,d.jsxs)("div",{className:"mt-3 p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 text-green-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Grammar Analysis"})]}),(0,d.jsx)("div",{className:"space-y-2",children:a.map((a,b)=>(0,d.jsxs)("div",{className:"border-l-2 border-green-300 pl-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:a.text}),(0,d.jsx)(i.E,{variant:"outline",className:"text-xs",children:a.partOfSpeech})]}),(0,d.jsx)("p",{className:"text-sm text-gray-700",children:a.explanation}),a.example&&(0,d.jsxs)("p",{className:"text-xs text-gray-600 mt-1 italic",children:["Example: ",a.example]})]},b))})]}):null}function x({message:a,onPlayAudio:b,onRegenerateResponse:c}){let e="user"===a.role,f="assistant"===a.role;return(0,d.jsxs)("div",{className:(0,u.cn)("flex gap-3 mb-4",e?"justify-end":"justify-start"),children:[!e&&(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:(0,d.jsx)(l.A,{className:"h-4 w-4 text-white"})})}),(0,d.jsxs)("div",{className:(0,u.cn)("max-w-[80%] space-y-1",e?"items-end":"items-start"),children:[(0,d.jsx)(g.Zp,{className:(0,u.cn)("p-3",e?"bg-blue-600 text-white":"bg-white border border-gray-200"),children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("p",{className:(0,u.cn)("text-sm leading-relaxed",e?"text-white":"text-gray-900"),children:a.content}),f&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 pt-2 border-t border-gray-100",children:[(0,d.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:()=>{navigator.clipboard.writeText(a.content)},className:"h-6 px-2 text-xs",children:[(0,d.jsx)(r,{className:"h-3 w-3 mr-1"}),"Copy"]}),a.hasAudio&&a.audioUrl&&(0,d.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:()=>{a.audioUrl&&b&&b(a.audioUrl)},className:"h-6 px-2 text-xs",children:[(0,d.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"Play"]}),c&&(0,d.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:c,className:"h-6 px-2 text-xs",children:[(0,d.jsx)(s,{className:"h-3 w-3 mr-1"}),"Regenerate"]})]})]})}),f&&a.furigana&&(0,d.jsx)(v,{furigana:a.furigana}),f&&a.grammarBreakdown&&(0,d.jsx)(w,{grammar:a.grammarBreakdown}),(0,d.jsx)("div",{className:(0,u.cn)("text-xs text-gray-500",e?"text-right":"text-left"),children:a.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),e&&(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center",children:(0,d.jsx)(t.A,{className:"h-4 w-4 text-white"})})})]})}function y(){return(0,d.jsxs)("div",{className:"flex gap-3 mb-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:(0,d.jsx)(l.A,{className:"h-4 w-4 text-white"})})}),(0,d.jsx)(g.Zp,{className:"p-3 bg-white border border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("div",{className:"flex space-x-1",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"AI is thinking..."})]})})]})}function z({messages:a,isLoading:b=!1,isStreaming:c=!1,onRegenerateResponse:f,onPlayAudio:g}){let h=(0,e.useRef)(null);return(0,d.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[0===a.length?(0,d.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(l.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Start a conversation"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Ask me anything about your subject. I'm here to help you learn!"})]})}):(0,d.jsxs)(d.Fragment,{children:[a.map(a=>(0,d.jsx)(x,{message:a,onPlayAudio:g,onRegenerateResponse:"assistant"===a.role?f:void 0},a.id)),(b||c)&&(0,d.jsx)(y,{})]}),(0,d.jsx)("div",{ref:h})]})}var A=c(34729);let B=(0,q.A)("mic-off",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M16.95 16.95A7 7 0 0 1 5 12v-2",key:"cqa7eg"}],["path",{d:"M18.89 13.23A7 7 0 0 0 19 12v-2",key:"16hl24"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}]]),C=(0,q.A)("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]]),D=(0,q.A)("paperclip",[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]]),E=(0,q.A)("smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);var F=c(41862);let G=(0,q.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);function H({onSendMessage:a,isLoading:b=!1,isStreaming:c=!1,placeholder:f="Type your message here...",disabled:g=!1,maxLength:i=1e3}){let[j,k]=(0,e.useState)(""),[l,m]=(0,e.useState)(!1),[n,o]=(0,e.useState)(0),p=(0,e.useRef)(null),q=(0,e.useRef)(null),r=(0,e.useRef)(null),s=()=>{!j.trim()||b||c||g||(a(j.trim()),k(""),p.current&&(p.current.style.height="auto"))},t=async()=>{try{let a=await navigator.mediaDevices.getUserMedia({audio:!0}),b=new MediaRecorder(a);q.current=b;let c=[];b.ondataavailable=a=>{c.push(a.data)},b.onstop=async()=>{let b=new Blob(c,{type:"audio/wav"});console.log("Audio recorded, blob ready for processing:",b.size,"bytes"),k("Voice message transcribed"),a.getTracks().forEach(a=>a.stop())},b.start(),m(!0),o(0),r.current=setInterval(()=>{o(a=>a+1)},1e3)}catch(a){console.error("Error starting recording:",a),alert("Could not access microphone. Please check permissions.")}},v=!j.trim()||b||c||g;return(0,d.jsx)("div",{className:"border-t bg-white p-4",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[l&&(0,d.jsxs)("div",{className:"mb-3 flex items-center justify-center space-x-2 text-red-600",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-red-600 rounded-full animate-pulse"}),(0,d.jsxs)("span",{className:"text-sm font-medium",children:["Recording... ",(a=>{let b=Math.floor(a/60);return`${b}:${(a%60).toString().padStart(2,"0")}`})(n)]})]}),(0,d.jsxs)("div",{className:"flex items-end space-x-3",children:[(0,d.jsx)(h.$,{variant:"outline",size:"icon",onClick:l?()=>{q.current&&l&&(q.current.stop(),m(!1),o(0),r.current&&(clearInterval(r.current),r.current=null))}:t,disabled:g||b||c,className:(0,u.cn)("flex-shrink-0",l&&"bg-red-50 border-red-200 text-red-600"),children:l?(0,d.jsx)(B,{className:"h-4 w-4"}):(0,d.jsx)(C,{className:"h-4 w-4"})}),(0,d.jsxs)("div",{className:"flex-1 relative",children:[(0,d.jsx)(A.T,{ref:p,value:j,onChange:a=>k(a.target.value),onKeyPress:a=>{"Enter"!==a.key||a.shiftKey||(a.preventDefault(),s())},placeholder:f,disabled:g||l,maxLength:i,className:(0,u.cn)("min-h-[44px] max-h-32 resize-none pr-12","focus:ring-2 focus:ring-blue-500 focus:border-transparent"),rows:1}),j.length>.8*i&&(0,d.jsxs)("div",{className:"absolute bottom-2 right-2 text-xs text-gray-500",children:[j.length,"/",i]})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(h.$,{variant:"outline",size:"icon",disabled:g||b||c,className:"flex-shrink-0",onClick:()=>{console.log("File attachment clicked")},children:(0,d.jsx)(D,{className:"h-4 w-4"})}),(0,d.jsx)(h.$,{variant:"outline",size:"icon",disabled:g||b||c,className:"flex-shrink-0",onClick:()=>{console.log("Emoji picker clicked")},children:(0,d.jsx)(E,{className:"h-4 w-4"})})]}),(0,d.jsx)(h.$,{onClick:s,disabled:v,className:"flex-shrink-0",size:"icon",children:b||c?(0,d.jsx)(F.A,{className:"h-4 w-4 animate-spin"}):(0,d.jsx)(G,{className:"h-4 w-4"})})]}),(0,d.jsx)("div",{className:"mt-3 flex flex-wrap gap-2",children:["Explain this concept","Give me an example","What does this mean?","How do I pronounce this?"].map(a=>(0,d.jsx)(h.$,{variant:"outline",size:"sm",onClick:()=>k(a),disabled:g||b||c||l,className:"text-xs",children:a},a))}),(0,d.jsxs)("div",{className:"mt-2 text-xs text-gray-500 text-center",children:["Press Enter to send, Shift+Enter for new line",!g&&" • Click mic to record voice message"]})]})})}var I=c(87142),J=c(90631),K=c(62185);function L(){let a=(0,f.useRouter)();(0,f.useSearchParams)().get("subject");let b=(0,I.ot)(),c=(0,I.oh)(),q=(0,I.Gs)(),r=(0,I.H)(),s=(0,J.fR)(),{setCurrentSubject:t}=(0,I.D9)(),{sendMessage:u,regenerateLastResponse:v}=(0,I.LC)(),[w,x]=(0,e.useState)(null),y=b=>{let c=s.find(a=>a.id===b)||K.jy.subjects.find(a=>a.id===b);c&&(t(c),a.push(`/student/chat?subject=${b}`))},A=async a=>{await u(a)},B=async()=>{await v()},C=s.length>0?s:K.jy.subjects;return(0,d.jsxs)("div",{className:"h-[calc(100vh-4rem)] flex flex-col",children:[(0,d.jsx)("div",{className:"border-b bg-white p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:()=>a.push("/student"),children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(l.A,{className:"h-5 w-5 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-lg font-semibold",children:"AI Tutor Chat"}),r&&(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[r.name," with ",r.teacherName]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)(j.l6,{value:r?.id||"",onValueChange:y,children:[(0,d.jsx)(j.bq,{className:"w-48",children:(0,d.jsx)(j.yv,{placeholder:"Select a subject"})}),(0,d.jsx)(j.gC,{children:C.map(a=>(0,d.jsx)(j.eb,{value:a.id,children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.name})]})},a.id))})]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Settings"]})]})]})}),r&&(0,d.jsx)("div",{className:"bg-blue-50 border-b border-blue-200 p-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.E,{variant:"secondary",children:r.name}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Ask questions, get explanations, and practice with your AI tutor"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:[b.length," messages"]})]})]})}),!r&&(0,d.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,d.jsxs)(g.Zp,{className:"w-96",children:[(0,d.jsxs)(g.aR,{className:"text-center",children:[(0,d.jsxs)(g.ZB,{className:"flex items-center justify-center space-x-2",children:[(0,d.jsx)(m.A,{className:"h-6 w-6"}),(0,d.jsx)("span",{children:"Select a Subject"})]}),(0,d.jsx)(g.BT,{children:"Choose a subject to start chatting with your AI tutor"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-3",children:C.map(a=>(0,d.jsxs)(h.$,{variant:"outline",className:"w-full justify-start",onClick:()=>y(a.id),children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-3"}),(0,d.jsxs)("div",{className:"text-left",children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:a.teacherName})]})]},a.id))})})]})}),r&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,d.jsx)(z,{messages:b,isLoading:c,isStreaming:q,onRegenerateResponse:B,onPlayAudio:a=>{w&&(w.src=a,w.play().catch(console.error))}})}),(0,d.jsx)(H,{onSendMessage:A,isLoading:c,isStreaming:q,placeholder:`Ask ${r.teacherName} anything about ${r.name}...`})]}),r&&0===b.length&&(0,d.jsxs)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center max-w-md",children:[(0,d.jsx)(l.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Welcome to AI Tutoring!"}),(0,d.jsxs)("p",{className:"text-gray-600 mb-6",children:["I'm your AI tutor for ",r.name,". I can help you with:"]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-gray-700",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 text-blue-600"}),(0,d.jsx)("span",{children:"Explanations"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-gray-700",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-green-600"}),(0,d.jsx)("span",{children:"Grammar help"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-gray-700",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 text-purple-600"}),(0,d.jsx)("span",{children:"Pronunciation"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-gray-700",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 text-orange-600"}),(0,d.jsx)("span",{children:"Practice"})]})]})]})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29872:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dash)",{children:["student",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,84185)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\chat\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,73035)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\chat\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dash)/student/chat/page",pathname:"/student/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dash)/student/chat/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},33873:a=>{"use strict";a.exports=require("path")},34729:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},62185:(a,b,c)=>{"use strict";c.d(b,{Pt:()=>f,jy:()=>g});let d=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3000/api/v1";async function e(a,b={}){try{let c=await fetch(`${d}${a}`,{headers:{"Content-Type":"application/json",...b.headers},...b});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(a){return console.error("API request failed:",a),{success:!1,error:a instanceof Error?a.message:"Unknown error occurred"}}}let f={sendMessage:async function*(a,b){let c=await fetch(`${d}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:a,subjectId:b})});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);let e=c.body?.getReader();if(!e)throw Error("No response body");let f=new TextDecoder,g="";try{for(;;){let{done:a,value:b}=await e.read();if(a)break;let c=(g+=f.decode(b,{stream:!0})).split("\n");for(let a of(g=c.pop()||"",c))if(a.startsWith("data: ")){let b=a.slice(6);if("[DONE]"===b)return;try{let a=JSON.parse(b);yield a}catch(a){console.error("Failed to parse SSE data:",a)}}}}finally{e.releaseLock()}},getChatHistory:a=>e(`/chat/history${a?`?subjectId=${a}`:""}`)},g={user:{id:"1",name:"John Doe",email:"<EMAIL>",role:"student",orgId:"org1",createdAt:new Date,updatedAt:new Date},subjects:[{id:"1",name:"Japanese Language",description:"Learn Japanese with AI assistance",teacherId:"teacher1",teacherName:"Tanaka Sensei",orgId:"org1",createdAt:new Date,updatedAt:new Date},{id:"2",name:"Mathematics",description:"Advanced mathematics concepts",teacherId:"teacher2",teacherName:"Smith Sensei",orgId:"org1",createdAt:new Date,updatedAt:new Date}],messages:[{id:"1",content:"Hello! How can I help you today?",role:"assistant",timestamp:new Date,subjectId:"1"},{id:"2",content:"Can you explain the difference between は and が?",role:"user",timestamp:new Date,subjectId:"1"}]}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},81839:(a,b,c)=>{Promise.resolve().then(c.bind(c,84185))},83753:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},84185:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI\\\\ai\\\\frontend\\\\src\\\\app\\\\(dash)\\\\student\\\\chat\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\chat\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87142:(a,b,c)=>{"use strict";c.d(b,{D9:()=>l,Gs:()=>j,H:()=>k,LC:()=>m,oh:()=>i,ot:()=>h});var d=c(98483),e=c(59350),f=c(62185);let g=(0,d.v)()((0,e.lt)((0,e.Zr)(a=>({messages:[],isLoading:!1,isStreaming:!1,currentSubject:void 0,addMessage:b=>{let c={...b,id:crypto.randomUUID(),timestamp:new Date};a(a=>({messages:[...a.messages,c]}),!1,"addMessage")},updateLastMessage:b=>{a(a=>{let c=[...a.messages],d=c.length-1;return d>=0&&(c[d]={...c[d],...b}),{messages:c}},!1,"updateLastMessage")},setLoading:b=>{a({isLoading:b},!1,"setLoading")},setStreaming:b=>{a({isStreaming:b},!1,"setStreaming")},setCurrentSubject:b=>{a({currentSubject:b},!1,"setCurrentSubject")},clearMessages:()=>{a({messages:[]},!1,"clearMessages")}}),{name:"chat-store",partialize:a=>({messages:a.messages,currentSubject:a.currentSubject})}),{name:"chat-store"})),h=()=>g(a=>a.messages),i=()=>g(a=>a.isLoading),j=()=>g(a=>a.isStreaming),k=()=>g(a=>a.currentSubject),l=()=>g(a=>({addMessage:a.addMessage,updateLastMessage:a.updateLastMessage,setLoading:a.setLoading,setStreaming:a.setStreaming,setCurrentSubject:a.setCurrentSubject,clearMessages:a.clearMessages})),m=()=>{let{addMessage:a,updateLastMessage:b,setLoading:c,setStreaming:d}=l(),e=k(),h=async g=>{if(g.trim()){a({content:g.trim(),role:"user",subjectId:e?.id}),a({content:"",role:"assistant",subjectId:e?.id}),c(!0),d(!0);try{let a="";for await(let c of f.Pt.sendMessage(g,e?.id))if("text"===c.type)a+=c.content,b({content:a});else if("audio"===c.type&&c.audioUrl)b({hasAudio:!0,audioUrl:c.audioUrl,visemes:c.visemes});else if("complete"===c.type){b({content:a+c.content});break}}catch(a){console.error("Failed to send message:",a),b({content:"Sorry, I encountered an error. Please try again."})}finally{c(!1),d(!1)}}},i=async()=>{let a=g.getState().messages,b=[...a].reverse().find(a=>"user"===a.role);if(!b)return;let c=a.filter((b,c)=>c!==a.length-1||"assistant"!==a[a.length-1].role);g.setState({messages:c}),await h(b.content)};return{sendMessage:h,regenerateLastResponse:i,loadChatHistory:async a=>{c(!0);try{console.log("Loading chat history for subject:",a)}catch(a){console.error("Failed to load chat history:",a)}finally{c(!1)}}}}},90087:(a,b,c)=>{Promise.resolve().then(c.bind(c,22304))},90631:(a,b,c)=>{"use strict";c.d(b,{Il:()=>k,fR:()=>i,st:()=>j});var d=c(98483),e=c(59350),f=c(43210),g=c(62185);let h=(0,d.v)()((0,e.lt)((0,e.Zr)(a=>({user:void 0,subjects:[],materials:[],announcements:[],analytics:void 0,setUser:b=>{a({user:b},!1,"setUser")},setSubjects:b=>{a({subjects:b},!1,"setSubjects")},setMaterials:b=>{a({materials:b},!1,"setMaterials")},setAnnouncements:b=>{a({announcements:b},!1,"setAnnouncements")},setAnalytics:b=>{a({analytics:b},!1,"setAnalytics")}}),{name:"user-store",partialize:a=>({user:a.user,subjects:a.subjects,announcements:a.announcements})}),{name:"user-store"})),i=()=>h(a=>a.subjects),j=()=>h(a=>a.analytics),k=()=>{let{user:a}=(()=>{let{user:a,setUser:b}=h();return{user:a,login:async(a,c)=>{try{return console.log("Logging in:",a,"with password length:",c.length),b(g.jy.user),{success:!0}}catch(a){return console.error("Login failed:",a),{success:!1,error:"Login failed"}}},logout:async()=>{try{return b(void 0),{success:!0}}catch(a){return console.error("Logout failed:",a),{success:!1,error:"Logout failed"}}},loadUserProfile:async()=>{try{return console.log("Loading user profile"),b(g.jy.user),{success:!0}}catch(a){return console.error("Failed to load user profile:",a),{success:!1,error:"Failed to load profile"}}},isAuthenticated:!!a,isStudent:a?.role==="student",isTeacher:a?.role==="teacher",isHOD:a?.role==="hod",isAdmin:a?.role==="admin"}})(),b=(0,f.useCallback)(async()=>{try{return console.log("Loading subjects"),h.getState().setSubjects(g.jy.subjects),{success:!0}}catch(a){return console.error("Failed to load subjects:",a),{success:!1,error:"Failed to load subjects"}}},[]),c=(0,f.useCallback)(async a=>{try{return console.log("Loading materials for subject:",a),h.getState().setMaterials([]),{success:!0}}catch(a){return console.error("Failed to load materials:",a),{success:!1,error:"Failed to load materials"}}},[]),d=(0,f.useCallback)(async()=>{try{return console.log("Loading announcements"),h.getState().setAnnouncements([]),{success:!0}}catch(a){return console.error("Failed to load announcements:",a),{success:!1,error:"Failed to load announcements"}}},[]),e=(0,f.useCallback)(async()=>{if(!a||"hod"!==a.role&&"admin"!==a.role)return{success:!1,error:"Unauthorized"};try{return console.log("Loading analytics"),h.getState().setAnalytics({totalUsers:150,totalStudents:120,totalTeachers:25,totalSubjects:15,totalMaterials:85,totalChats:1250,activeUsersToday:45,activeUsersThisWeek:98,popularSubjects:[{subjectId:"1",subjectName:"Japanese Language",chatCount:450},{subjectId:"2",subjectName:"Mathematics",chatCount:320}],userGrowth:[{date:"2024-01-01",count:100},{date:"2024-02-01",count:120},{date:"2024-03-01",count:150}]}),{success:!0}}catch(a){return console.error("Failed to load analytics:",a),{success:!1,error:"Failed to load analytics"}}},[a]),i=(0,f.useCallback)(async()=>{let c=(await Promise.allSettled([b(),d(),...a?.role==="hod"||a?.role==="admin"?[e()]:[]])).filter(a=>"rejected"===a.status);return c.length>0&&console.error("Some data failed to load:",c),{success:0===c.length}},[b,d,e,a?.role]);return{loadSubjects:b,loadMaterials:c,loadAnnouncements:d,loadAnalytics:e,loadAllData:i}}},91391:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,625,393,484,738,215],()=>b(b.s=29872));module.exports=c})();