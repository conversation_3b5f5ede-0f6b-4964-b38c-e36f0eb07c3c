{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/helpers/environment-assets.js"], "sourcesContent": ["const presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\n\nexport { presetsObj };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa;IACjB,WAAW;IACX,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,WAAW;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/useEnvironment.js"], "sourcesContent": ["import { useThree, useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, CubeTextureLoader } from 'three';\nimport { RGBELoader, EXRLoader } from 'three-stdlib';\nimport { HDRJPGLoader, GainMapLoader } from '@monogrid/gainmap-js';\nimport { presetsObj } from '../helpers/environment-assets.js';\nimport { useLayoutEffect } from 'react';\n\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nconst defaultFiles = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'];\nfunction useEnvironment({\n  files = defaultFiles,\n  path = '',\n  preset = undefined,\n  colorSpace = undefined,\n  extensions\n} = {}) {\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  // Everything else\n  const multiFile = isArray(files);\n  const {\n    extension,\n    isCubemap\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const gl = useThree(state => state.gl);\n  useLayoutEffect(() => {\n    // Only required for gainmap\n    if (extension !== 'webp' && extension !== 'jpg' && extension !== 'jpeg') return;\n    function clearGainmapTexture() {\n      useLoader.clear(loader, multiFile ? [files] : files);\n    }\n    gl.domElement.addEventListener('webglcontextlost', clearGainmapTexture, {\n      once: true\n    });\n  }, [files, gl.domElement]);\n  const loaderResult = useLoader(loader, multiFile ? [files] : files, loader => {\n    // Gainmap requires a renderer\n    if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n      // @ts-expect-error\n      loader.setRenderer(gl);\n    }\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n  let texture = multiFile ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  if (extension === 'jpg' || extension === 'jpeg' || extension === 'webp') {\n    var _renderTarget;\n    texture = (_renderTarget = texture.renderTarget) == null ? void 0 : _renderTarget.texture;\n  }\n  texture.mapping = isCubemap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  texture.colorSpace = colorSpace !== null && colorSpace !== void 0 ? colorSpace : isCubemap ? 'srgb' : 'srgb-linear';\n  return texture;\n}\nconst preloadDefaultOptions = {\n  files: defaultFiles,\n  path: '',\n  preset: undefined,\n  extensions: undefined\n};\nuseEnvironment.preload = preloadOptions => {\n  const options = {\n    ...preloadDefaultOptions,\n    ...preloadOptions\n  };\n  let {\n    files,\n    path = ''\n  } = options;\n  const {\n    preset,\n    extensions\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n  const {\n    extension\n  } = getExtension(files);\n  if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n    throw new Error('useEnvironment: Preloading gainmaps is not supported');\n  }\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.preload(loader, isArray(files) ? [files] : files, loader => {\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n};\nconst clearDefaultOptins = {\n  files: defaultFiles,\n  preset: undefined\n};\nuseEnvironment.clear = clearOptions => {\n  const options = {\n    ...clearDefaultOptins,\n    ...clearOptions\n  };\n  let {\n    files\n  } = options;\n  const {\n    preset\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n  }\n  const {\n    extension\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.clear(loader, isArray(files) ? [files] : files);\n};\nfunction validatePreset(preset) {\n  if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n}\nfunction getExtension(files) {\n  var _firstEntry$split$pop;\n  const isCubemap = isArray(files) && files.length === 6;\n  const isGainmap = isArray(files) && files.length === 3 && files.some(file => file.endsWith('json'));\n  const firstEntry = isArray(files) ? files[0] : files;\n\n  // Everything else\n  const extension = isCubemap ? 'cube' : isGainmap ? 'webp' : firstEntry.startsWith('data:application/exr') ? 'exr' : firstEntry.startsWith('data:application/hdr') ? 'hdr' : firstEntry.startsWith('data:image/jpeg') ? 'jpg' : (_firstEntry$split$pop = firstEntry.split('.').pop()) == null || (_firstEntry$split$pop = _firstEntry$split$pop.split('?')) == null || (_firstEntry$split$pop = _firstEntry$split$pop.shift()) == null ? void 0 : _firstEntry$split$pop.toLowerCase();\n  return {\n    extension,\n    isCubemap,\n    isGainmap\n  };\n}\nfunction getLoader(extension) {\n  const loader = extension === 'cube' ? CubeTextureLoader : extension === 'hdr' ? RGBELoader : extension === 'exr' ? EXRLoader : extension === 'jpg' || extension === 'jpeg' ? HDRJPGLoader : extension === 'webp' ? GainMapLoader : null;\n  return loader;\n}\n\nexport { useEnvironment };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,eAAe;AACrB,MAAM,UAAU,CAAA,MAAO,MAAM,OAAO,CAAC;AACrC,MAAM,eAAe;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU;AACvF,SAAS;QAAe,EACtB,QAAQ,YAAY,EACpB,OAAO,EAAE,EACT,SAAS,SAAS,EAClB,aAAa,SAAS,EACtB,UAAU,EACX,GANuB,iEAMpB,CAAC;IACH,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,+KAAA,CAAA,aAAU,CAAC,OAAO;QAC1B,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,YAAY,QAAQ;IAC1B,MAAM,EACJ,SAAS,EACT,SAAS,EACV,GAAG,aAAa;IACjB,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,MAAM,KAAK,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;uCAAE,CAAA,QAAS,MAAM,EAAE;;IACrC,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;0CAAE;YACd,4BAA4B;YAC5B,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;YACzE,SAAS;gBACP,mNAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,YAAY;oBAAC;iBAAM,GAAG;YAChD;YACA,GAAG,UAAU,CAAC,gBAAgB,CAAC,oBAAoB,qBAAqB;gBACtE,MAAM;YACR;QACF;yCAAG;QAAC;QAAO,GAAG,UAAU;KAAC;IACzB,MAAM,eAAe,CAAA,GAAA,mNAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,YAAY;QAAC;KAAM,GAAG;kDAAO,CAAA;YAClE,8BAA8B;YAC9B,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;gBACvE,mBAAmB;gBACnB,OAAO,WAAW,CAAC;YACrB;YACA,OAAO,OAAO,IAAI,QAAQ,OAAO,OAAO,CAAC;YACzC,mBAAmB;YACnB,IAAI,YAAY,WAAW;QAC7B;;IACA,IAAI,UAAU,YACd,aAAa;IACb,YAAY,CAAC,EAAE,GAAG;IAClB,IAAI,cAAc,SAAS,cAAc,UAAU,cAAc,QAAQ;QACvE,IAAI;QACJ,UAAU,CAAC,gBAAgB,QAAQ,YAAY,KAAK,OAAO,KAAK,IAAI,cAAc,OAAO;IAC3F;IACA,QAAQ,OAAO,GAAG,YAAY,kJAAA,CAAA,wBAAqB,GAAG,kJAAA,CAAA,mCAAgC;IACtF,QAAQ,UAAU,GAAG,eAAe,QAAQ,eAAe,KAAK,IAAI,aAAa,YAAY,SAAS;IACtG,OAAO;AACT;AACA,MAAM,wBAAwB;IAC5B,OAAO;IACP,MAAM;IACN,QAAQ;IACR,YAAY;AACd;AACA,eAAe,OAAO,GAAG,CAAA;IACvB,MAAM,UAAU;QACd,GAAG,qBAAqB;QACxB,GAAG,cAAc;IACnB;IACA,IAAI,EACF,KAAK,EACL,OAAO,EAAE,EACV,GAAG;IACJ,MAAM,EACJ,MAAM,EACN,UAAU,EACX,GAAG;IACJ,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,+KAAA,CAAA,aAAU,CAAC,OAAO;QAC1B,OAAO;IACT;IACA,MAAM,EACJ,SAAS,EACV,GAAG,aAAa;IACjB,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;QACvE,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,mNAAA,CAAA,YAAS,CAAC,OAAO,CAAC,QAAQ,QAAQ,SAAS;QAAC;KAAM,GAAG,OAAO,CAAA;QAC1D,OAAO,OAAO,IAAI,QAAQ,OAAO,OAAO,CAAC;QACzC,mBAAmB;QACnB,IAAI,YAAY,WAAW;IAC7B;AACF;AACA,MAAM,qBAAqB;IACzB,OAAO;IACP,QAAQ;AACV;AACA,eAAe,KAAK,GAAG,CAAA;IACrB,MAAM,UAAU;QACd,GAAG,kBAAkB;QACrB,GAAG,YAAY;IACjB;IACA,IAAI,EACF,KAAK,EACN,GAAG;IACJ,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,+KAAA,CAAA,aAAU,CAAC,OAAO;IAC5B;IACA,MAAM,EACJ,SAAS,EACV,GAAG,aAAa;IACjB,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,mNAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,QAAQ,SAAS;QAAC;KAAM,GAAG;AACrD;AACA,SAAS,eAAe,MAAM;IAC5B,IAAI,CAAC,CAAC,UAAU,+KAAA,CAAA,aAAU,GAAG,MAAM,IAAI,MAAM,4BAA4B,OAAO,IAAI,CAAC,+KAAA,CAAA,aAAU,EAAE,IAAI,CAAC;AACxG;AACA,SAAS,aAAa,KAAK;IACzB,IAAI;IACJ,MAAM,YAAY,QAAQ,UAAU,MAAM,MAAM,KAAK;IACrD,MAAM,YAAY,QAAQ,UAAU,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;IAC3F,MAAM,aAAa,QAAQ,SAAS,KAAK,CAAC,EAAE,GAAG;IAE/C,kBAAkB;IAClB,MAAM,YAAY,YAAY,SAAS,YAAY,SAAS,WAAW,UAAU,CAAC,0BAA0B,QAAQ,WAAW,UAAU,CAAC,0BAA0B,QAAQ,WAAW,UAAU,CAAC,qBAAqB,QAAQ,CAAC,wBAAwB,WAAW,KAAK,CAAC,KAAK,GAAG,EAAE,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,sBAAsB,WAAW;IACld,OAAO;QACL;QACA;QACA;IACF;AACF;AACA,SAAS,UAAU,SAAS;IAC1B,MAAM,SAAS,cAAc,SAAS,kJAAA,CAAA,oBAAiB,GAAG,cAAc,QAAQ,2JAAA,CAAA,aAAU,GAAG,cAAc,QAAQ,0JAAA,CAAA,YAAS,GAAG,cAAc,SAAS,cAAc,SAAS,gLAAA,CAAA,eAAY,GAAG,cAAc,SAAS,gLAAA,CAAA,gBAAa,GAAG;IACnO,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/Environment.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, use<PERSON>rame, createPortal, applyProps, extend } from '@react-three/fiber';\nimport { Scene, WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport { GroundProjectedEnv } from 'three-stdlib';\nimport { useEnvironment } from './useEnvironment.js';\n\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, sceneProps = {}) {\n  var _target$backgroundRot, _target$backgroundRot2, _target$environmentRo, _target$environmentRo2;\n  // defaults\n  sceneProps = {\n    backgroundBlurriness: 0,\n    backgroundIntensity: 1,\n    backgroundRotation: [0, 0, 0],\n    environmentIntensity: 1,\n    environmentRotation: [0, 0, 0],\n    ...sceneProps\n  };\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment;\n  const oldSceneProps = {\n    // @ts-ignore\n    backgroundBlurriness: target.backgroundBlurriness,\n    // @ts-ignore\n    backgroundIntensity: target.backgroundIntensity,\n    // @ts-ignore\n    backgroundRotation: (_target$backgroundRot = (_target$backgroundRot2 = target.backgroundRotation) == null || _target$backgroundRot2.clone == null ? void 0 : _target$backgroundRot2.clone()) !== null && _target$backgroundRot !== void 0 ? _target$backgroundRot : [0, 0, 0],\n    // @ts-ignore\n    environmentIntensity: target.environmentIntensity,\n    // @ts-ignore\n    environmentRotation: (_target$environmentRo = (_target$environmentRo2 = target.environmentRotation) == null || _target$environmentRo2.clone == null ? void 0 : _target$environmentRo2.clone()) !== null && _target$environmentRo !== void 0 ? _target$environmentRo : [0, 0, 0]\n  };\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture;\n  applyProps(target, sceneProps);\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg;\n    applyProps(target, oldSceneProps);\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  map,\n  ...config\n}) {\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, config);\n  });\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  ...rest\n}) {\n  const texture = useEnvironment(rest);\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  });\n  React.useEffect(() => {\n    return () => {\n      texture.dispose();\n    };\n  }, [texture]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 0.1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = useThree(state => state.gl);\n  const defaultScene = useThree(state => state.scene);\n  const camera = React.useRef(null);\n  const [virtualScene] = React.useState(() => new Scene());\n  const fbo = React.useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  React.useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  React.useLayoutEffect(() => {\n    if (frames === 1) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n    }\n    return setEnvProps(background, scene, defaultScene, fbo.texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/React.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/React.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = useEnvironment(props);\n  const texture = props.map || textureDefault;\n  React.useMemo(() => extend({\n    GroundProjectedEnvImpl: GroundProjectedEnv\n  }), []);\n  React.useEffect(() => {\n    return () => {\n      textureDefault.dispose();\n    };\n  }, [textureDefault]);\n  const args = React.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(EnvironmentMap, _extends({}, props, {\n    map: texture\n  })), /*#__PURE__*/React.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/React.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/React.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/React.createElement(EnvironmentPortal, props) : /*#__PURE__*/React.createElement(EnvironmentCube, props);\n}\n\nexport { Environment, EnvironmentCube, EnvironmentMap, EnvironmentPortal };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,QAAQ,CAAA,MAAO,IAAI,OAAO,IAAI,IAAI,OAAO,CAAC,OAAO;AACvD,MAAM,eAAe,CAAA,QAAS,MAAM,SAAS,MAAM,OAAO,GAAG;AAC7D,SAAS,YAAY,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO;QAAE,aAAA,iEAAa,CAAC;IAC3E,IAAI,uBAAuB,wBAAwB,uBAAuB;IAC1E,WAAW;IACX,aAAa;QACX,sBAAsB;QACtB,qBAAqB;QACrB,oBAAoB;YAAC;YAAG;YAAG;SAAE;QAC7B,sBAAsB;QACtB,qBAAqB;YAAC;YAAG;YAAG;SAAE;QAC9B,GAAG,UAAU;IACf;IACA,MAAM,SAAS,aAAa,SAAS;IACrC,MAAM,QAAQ,OAAO,UAAU;IAC/B,MAAM,SAAS,OAAO,WAAW;IACjC,MAAM,gBAAgB;QACpB,aAAa;QACb,sBAAsB,OAAO,oBAAoB;QACjD,aAAa;QACb,qBAAqB,OAAO,mBAAmB;QAC/C,aAAa;QACb,oBAAoB,CAAC,wBAAwB,CAAC,yBAAyB,OAAO,kBAAkB,KAAK,QAAQ,uBAAuB,KAAK,IAAI,OAAO,KAAK,IAAI,uBAAuB,KAAK,EAAE,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;YAAC;YAAG;YAAG;SAAE;QAC7Q,aAAa;QACb,sBAAsB,OAAO,oBAAoB;QACjD,aAAa;QACb,qBAAqB,CAAC,wBAAwB,CAAC,yBAAyB,OAAO,mBAAmB,KAAK,QAAQ,uBAAuB,KAAK,IAAI,OAAO,KAAK,IAAI,uBAAuB,KAAK,EAAE,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;YAAC;YAAG;YAAG;SAAE;IACjR;IACA,IAAI,eAAe,QAAQ,OAAO,WAAW,GAAG;IAChD,IAAI,YAAY,OAAO,UAAU,GAAG;IACpC,CAAA,GAAA,oNAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IACnB,OAAO;QACL,IAAI,eAAe,QAAQ,OAAO,WAAW,GAAG;QAChD,IAAI,YAAY,OAAO,UAAU,GAAG;QACpC,CAAA,GAAA,oNAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IACrB;AACF;AACA,SAAS,eAAe,KAKvB;QALuB,EACtB,KAAK,EACL,aAAa,KAAK,EAClB,GAAG,EACH,GAAG,QACJ,GALuB;IAMtB,MAAM,eAAe,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;iDAAE,CAAA,QAAS,MAAM,KAAK;;IAClD,6JAAA,CAAA,kBAAqB;0CAAC;YACpB,IAAI,KAAK,OAAO,YAAY,YAAY,OAAO,cAAc,KAAK;QACpE;;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,KAUxB;QAVwB,EACvB,aAAa,KAAK,EAClB,KAAK,EACL,IAAI,EACJ,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,mBAAmB,EACnB,GAAG,MACJ,GAVwB;IAWvB,MAAM,UAAU,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,MAAM,eAAe,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kDAAE,CAAA,QAAS,MAAM,KAAK;;IAClD,6JAAA,CAAA,kBAAqB;2CAAC;YACpB,OAAO,YAAY,YAAY,OAAO,cAAc,SAAS;gBAC3D,sBAAsB,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO;gBAChE;gBACA;gBACA;gBACA;YACF;QACF;;IACA,6JAAA,CAAA,YAAe;qCAAC;YACd;6CAAO;oBACL,QAAQ,OAAO;gBACjB;;QACF;oCAAG;QAAC;KAAQ;IACZ,OAAO;AACT;AACA,SAAS,kBAAkB,KAmB1B;QAnB0B,EACzB,QAAQ,EACR,OAAO,GAAG,EACV,MAAM,IAAI,EACV,aAAa,GAAG,EAChB,SAAS,CAAC,EACV,GAAG,EACH,aAAa,KAAK,EAClB,IAAI,EACJ,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,mBAAmB,EACnB,KAAK,EACL,KAAK,EACL,IAAI,EACJ,SAAS,SAAS,EAClB,UAAU,EACX,GAnB0B;IAoBzB,MAAM,KAAK,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;0CAAE,CAAA,QAAS,MAAM,EAAE;;IACrC,MAAM,eAAe,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;oDAAE,CAAA,QAAS,MAAM,KAAK;;IAClD,MAAM,SAAS,6JAAA,CAAA,SAAY,CAAC;IAC5B,MAAM,CAAC,aAAa,GAAG,6JAAA,CAAA,WAAc;sCAAC,IAAM,IAAI,kJAAA,CAAA,QAAK;;IACrD,MAAM,MAAM,6JAAA,CAAA,UAAa;0CAAC;YACxB,MAAM,MAAM,IAAI,kJAAA,CAAA,wBAAqB,CAAC;YACtC,IAAI,OAAO,CAAC,IAAI,GAAG,kJAAA,CAAA,gBAAa;YAChC,OAAO;QACT;yCAAG;QAAC;KAAW;IACf,6JAAA,CAAA,YAAe;uCAAC;YACd;+CAAO;oBACL,IAAI,OAAO;gBACb;;QACF;sCAAG;QAAC;KAAI;IACR,6JAAA,CAAA,kBAAqB;6CAAC;YACpB,IAAI,WAAW,GAAG;gBAChB,MAAM,YAAY,GAAG,SAAS;gBAC9B,GAAG,SAAS,GAAG;gBACf,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI;gBAC1B,GAAG,SAAS,GAAG;YACjB;YACA,OAAO,YAAY,YAAY,OAAO,cAAc,IAAI,OAAO,EAAE;gBAC/D,sBAAsB,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO;gBAChE;gBACA;gBACA;gBACA;YACF;QACF;4CAAG;QAAC;QAAU;QAAc,IAAI,OAAO;QAAE;QAAO;QAAc;QAAY;QAAQ;KAAG;IACrF,IAAI,QAAQ;IACZ,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;sCAAE;YACP,IAAI,WAAW,YAAY,QAAQ,QAAQ;gBACzC,MAAM,YAAY,GAAG,SAAS;gBAC9B,GAAG,SAAS,GAAG;gBACf,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI;gBAC1B,GAAG,SAAS,GAAG;gBACf;YACF;QACF;;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,CAAA,GAAA,sNAAA,CAAA,eAAY,AAAD,EAAE,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,UAAU,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,cAAc;QACzL,KAAK;QACL,MAAM;YAAC;YAAM;YAAK;SAAI;IACxB,IAAI,SAAS,SAAS,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iBAAiB;QACtE,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,MAAM;QACN,YAAY;IACd,KAAK,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,gBAAgB;QAC1D,YAAY;QACZ,KAAK;QACL,YAAY;IACd,KAAK,OAAO;AACd;AACA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,eAAe,gBAAgB,QAAQ;IAC3C,MAAM,iBAAiB,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE;IACtC,MAAM,UAAU,MAAM,GAAG,IAAI;IAC7B,6JAAA,CAAA,UAAa;qCAAC,IAAM,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE;gBACzB,wBAAwB,mKAAA,CAAA,qBAAkB;YAC5C;oCAAI,EAAE;IACN,6JAAA,CAAA,YAAe;uCAAC;YACd;+CAAO;oBACL,eAAe,OAAO;gBACxB;;QACF;sCAAG;QAAC;KAAe;IACnB,MAAM,OAAO,6JAAA,CAAA,UAAa;2CAAC,IAAM;gBAAC;aAAQ;0CAAE;QAAC;KAAQ;IACrD,MAAM,SAAS,CAAC,gBAAgB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,cAAc,MAAM;IACrF,MAAM,SAAS,CAAC,iBAAiB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,eAAe,MAAM;IACvF,MAAM,QAAQ,CAAC,SAAS,CAAC,iBAAiB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,eAAe,KAAK,MAAM,QAAQ,WAAW,KAAK,IAAI,SAAS;IAC1I,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,gBAAgB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACjI,KAAK;IACP,KAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0BAA0B;QAC9D,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;AACF;AACA,SAAS,YAAY,KAAK;IACxB,OAAO,MAAM,MAAM,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,mBAAmB,SAAS,MAAM,GAAG,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,gBAAgB,SAAS,MAAM,QAAQ,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,mBAAmB,SAAS,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iBAAiB;AAC3R", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/web/Html.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera, Vector2 } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nconst v1 = /* @__PURE__ */new Vector3();\nconst v2 = /* @__PURE__ */new Vector3();\nconst v3 = /* @__PURE__ */new Vector3();\nconst v4 = /* @__PURE__ */new Vector2();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  v4.set(screenPos.x, screenPos.y);\n  raycaster.setFromCamera(v4, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /* @__PURE__ */React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef(null);\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null);\n  // Append to the connected element, which makes HTML work with views\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null || _root$current.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null || _root$current2.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          } else if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */`\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */`\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\n\nexport { Html };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,KAAK,aAAa,GAAE,IAAI,kJAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,kJAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,kJAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,kJAAA,CAAA,UAAO;AACrC,SAAS,yBAAyB,EAAE,EAAE,MAAM,EAAE,IAAI;IAChD,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,UAAU,OAAO,CAAC;IAClB,MAAM,YAAY,KAAK,KAAK,GAAG;IAC/B,MAAM,aAAa,KAAK,MAAM,GAAG;IACjC,OAAO;QAAC,UAAU,CAAC,GAAG,YAAY;QAAW,CAAC,CAAC,UAAU,CAAC,GAAG,UAAU,IAAI;KAAW;AACxF;AACA,SAAS,qBAAqB,EAAE,EAAE,MAAM;IACtC,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;IAC7D,MAAM,cAAc,UAAU,GAAG,CAAC;IAClC,MAAM,SAAS,OAAO,iBAAiB,CAAC;IACxC,OAAO,YAAY,OAAO,CAAC,UAAU,KAAK,EAAE,GAAG;AACjD;AACA,SAAS,gBAAgB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO;IACrD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACrD,MAAM,YAAY,MAAM,KAAK;IAC7B,UAAU,OAAO,CAAC;IAClB,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;IAC/B,UAAU,aAAa,CAAC,IAAI;IAC5B,MAAM,aAAa,UAAU,gBAAgB,CAAC,SAAS;IACvD,IAAI,WAAW,MAAM,EAAE;QACrB,MAAM,uBAAuB,UAAU,CAAC,EAAE,CAAC,QAAQ;QACnD,MAAM,gBAAgB,MAAM,UAAU,CAAC,UAAU,GAAG,CAAC,MAAM;QAC3D,OAAO,gBAAgB;IACzB;IACA,OAAO;AACT;AACA,SAAS,YAAY,EAAE,EAAE,MAAM;IAC7B,IAAI,kBAAkB,kJAAA,CAAA,qBAAkB,EAAE;QACxC,OAAO,OAAO,IAAI;IACpB,OAAO,IAAI,kBAAkB,kJAAA,CAAA,oBAAiB,EAAE;QAC9C,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QACpC,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,WAAW,IAAI,KAAK,GAAG,CAAC,OAAO,KAAK;QAC1C,OAAO,IAAI;IACb,OAAO;QACL,OAAO;IACT;AACF;AACA,SAAS,aAAa,EAAE,EAAE,MAAM,EAAE,WAAW;IAC3C,IAAI,kBAAkB,kJAAA,CAAA,oBAAiB,IAAI,kBAAkB,kJAAA,CAAA,qBAAkB,EAAE;QAC/E,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,OAAO,IAAI;QACvE,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,IAAI,OAAO,GAAG;QACzC,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO;IAC/B;IACA,OAAO;AACT;AACA,MAAM,UAAU,CAAA,QAAS,KAAK,GAAG,CAAC,SAAS,QAAQ,IAAI;AACvD,SAAS,aAAa,MAAM,EAAE,WAAW;QAAE,UAAA,iEAAU;IACnD,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,IAAK;QAC7B,YAAY,QAAQ,WAAW,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK,MAAM,GAAG;IAClF;IACA,OAAO,UAAU;AACnB;AACA,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAA,SAAU,aAAa,QAAQ;AACxC,CAAC,EAAE;IAAC;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;CAAE;AACvD,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAC,QAAQ,SAAW,aAAa,QAAQ,iBAAiB,SAAS;AAC5E,CAAC,EAAE,CAAA,IAAK;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC;QAAG,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;AAChG,SAAS,YAAY,GAAG;IACtB,OAAO,OAAO,OAAO,QAAQ,YAAY,aAAa;AACxD;AACA,MAAM,OAAO,aAAa,GAAE,6JAAA,CAAA,aAAgB,CAAC,QAwB1C;QAxB2C,EAC5C,QAAQ,EACR,MAAM,KAAK,EACX,KAAK,EACL,SAAS,EACT,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN,cAAc,EACd,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,UAAU,EACV,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,cAAc;QAAC;QAAU;KAAE,EAC3B,oBAAoB,wBAAwB,EAC5C,KAAK,KAAK,EACV,YAAY,EACZ,gBAAgB,MAAM,EACtB,GAAG,OACJ;IACC,MAAM,EACJ,EAAE,EACF,MAAM,EACN,KAAK,EACL,IAAI,EACJ,SAAS,EACT,MAAM,EACN,QAAQ,EACT,GAAG,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;IACX,MAAM,CAAC,GAAG,GAAG,6JAAA,CAAA,WAAc;yBAAC,IAAM,SAAS,aAAa,CAAC;;IACzD,MAAM,OAAO,6JAAA,CAAA,SAAY,CAAC;IAC1B,MAAM,QAAQ,6JAAA,CAAA,SAAY,CAAC;IAC3B,MAAM,UAAU,6JAAA,CAAA,SAAY,CAAC;IAC7B,MAAM,cAAc,6JAAA,CAAA,SAAY,CAAC;QAAC;QAAG;KAAE;IACvC,MAAM,oBAAoB,6JAAA,CAAA,SAAY,CAAC;IACvC,MAAM,oBAAoB,6JAAA,CAAA,SAAY,CAAC;IACvC,oEAAoE;IACpE,MAAM,SAAS,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,OAAO,SAAS,IAAI,GAAG,UAAU,CAAC,UAAU;IACzG,MAAM,mBAAmB,6JAAA,CAAA,SAAY,CAAC;IACtC,MAAM,gBAAgB,6JAAA,CAAA,SAAY,CAAC;IACnC,MAAM,qBAAqB,6JAAA,CAAA,UAAa;4CAAC;YACvC,OAAO,WAAW,YAAY,cAAc,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,IAAI,YAAY,OAAO,CAAC,EAAE;QAChH;2CAAG;QAAC;KAAQ;IACZ,6JAAA,CAAA,kBAAqB;gCAAC;YACpB,MAAM,KAAK,GAAG,UAAU;YACxB,IAAI,WAAW,YAAY,YAAY;gBACrC,GAAG,KAAK,CAAC,MAAM,GAAG,AAAC,GAAiC,OAA/B,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG;gBACjD,GAAG,KAAK,CAAC,QAAQ,GAAG;gBACpB,GAAG,KAAK,CAAC,aAAa,GAAG;YAC3B,OAAO;gBACL,GAAG,KAAK,CAAC,MAAM,GAAG;gBAClB,GAAG,KAAK,CAAC,QAAQ,GAAG;gBACpB,GAAG,KAAK,CAAC,aAAa,GAAG;YAC3B;QACF;+BAAG;QAAC;KAAQ;IACZ,6JAAA,CAAA,kBAAqB;gCAAC;YACpB,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,cAAc,KAAK,OAAO,GAAG,qKAAA,CAAA,aAAmB,CAAC;gBACvD,MAAM,iBAAiB;gBACvB,IAAI,WAAW;oBACb,GAAG,KAAK,CAAC,OAAO,GAAI;gBACtB,OAAO;oBACL,MAAM,MAAM,kBAAkB,MAAM,OAAO,EAAE,QAAQ;oBACrD,GAAG,KAAK,CAAC,OAAO,GAAG,AAAC,wDAAmE,OAAZ,GAAG,CAAC,EAAE,EAAC,OAAY,OAAP,GAAG,CAAC,EAAE,EAAC;gBAChG;gBACA,IAAI,QAAQ;oBACV,IAAI,SAAS,OAAO,OAAO,CAAC;yBAAS,OAAO,WAAW,CAAC;gBAC1D;gBACA;4CAAO;wBACL,IAAI,QAAQ,OAAO,WAAW,CAAC;wBAC/B,YAAY,OAAO;oBACrB;;YACF;QACF;+BAAG;QAAC;QAAQ;KAAU;IACtB,6JAAA,CAAA,kBAAqB;gCAAC;YACpB,IAAI,cAAc,GAAG,SAAS,GAAG;QACnC;+BAAG;QAAC;KAAa;IACjB,MAAM,SAAS,6JAAA,CAAA,UAAa;gCAAC;YAC3B,IAAI,WAAW;gBACb,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;oBACnB,gBAAgB;oBAChB,eAAe;gBACjB;YACF,OAAO;gBACL,OAAO;oBACL,UAAU;oBACV,WAAW,SAAS,6BAA6B;oBACjD,GAAI,cAAc;wBAChB,KAAK,CAAC,KAAK,MAAM,GAAG;wBACpB,MAAM,CAAC,KAAK,KAAK,GAAG;wBACpB,OAAO,KAAK,KAAK;wBACjB,QAAQ,KAAK,MAAM;oBACrB,CAAC;oBACD,GAAG,KAAK;gBACV;YACF;QACF;+BAAG;QAAC;QAAO;QAAQ;QAAY;QAAM;KAAU;IAC/C,MAAM,uBAAuB,6JAAA,CAAA,UAAa;8CAAC,IAAM,CAAC;gBAChD,UAAU;gBACV;YACF,CAAC;6CAAG;QAAC;KAAc;IACnB,6JAAA,CAAA,kBAAqB;gCAAC;YACpB,cAAc,OAAO,GAAG;YACxB,IAAI,WAAW;gBACb,IAAI;gBACJ,CAAC,gBAAgB,KAAK,OAAO,KAAK,QAAQ,cAAc,MAAM,CAAC,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;oBACrG,KAAK;oBACL,OAAO;gBACT,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;oBACzC,KAAK;oBACL,OAAO;gBACT,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;oBACzC,KAAK;oBACL,WAAW;oBACX,OAAO;oBACP,UAAU;gBACZ;YACF,OAAO;gBACL,IAAI;gBACJ,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,MAAM,CAAC,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;oBACvG,KAAK;oBACL,OAAO;oBACP,WAAW;oBACX,UAAU;gBACZ;YACF;QACF;;IACA,MAAM,UAAU,6JAAA,CAAA,SAAY,CAAC;IAC7B,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;yBAAE,CAAA;YACP,IAAI,MAAM,OAAO,EAAE;gBACjB,OAAO,iBAAiB;gBACxB,MAAM,OAAO,CAAC,iBAAiB,CAAC,MAAM;gBACtC,MAAM,MAAM,YAAY,YAAY,OAAO,GAAG,kBAAkB,MAAM,OAAO,EAAE,QAAQ;gBACvF,IAAI,aAAa,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,KAAK;oBACpK,MAAM,iBAAiB,qBAAqB,MAAM,OAAO,EAAE;oBAC3D,IAAI,iBAAiB;oBACrB,IAAI,oBAAoB;wBACtB,IAAI,MAAM,OAAO,CAAC,UAAU;4BAC1B,iBAAiB,QAAQ,GAAG;iDAAC,CAAA,OAAQ,KAAK,OAAO;;wBACnD,OAAO,IAAI,YAAY,YAAY;4BACjC,iBAAiB;gCAAC;6BAAM;wBAC1B;oBACF;oBACA,MAAM,oBAAoB,QAAQ,OAAO;oBACzC,IAAI,gBAAgB;wBAClB,MAAM,YAAY,gBAAgB,MAAM,OAAO,EAAE,QAAQ,WAAW;wBACpE,QAAQ,OAAO,GAAG,aAAa,CAAC;oBAClC,OAAO;wBACL,QAAQ,OAAO,GAAG,CAAC;oBACrB;oBACA,IAAI,sBAAsB,QAAQ,OAAO,EAAE;wBACzC,IAAI,WAAW,UAAU,CAAC,QAAQ,OAAO;6BAAO,GAAG,KAAK,CAAC,OAAO,GAAG,QAAQ,OAAO,GAAG,UAAU;oBACjG;oBACA,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG;oBAC9C,MAAM,SAAS,UAAU,mBAAmB,EAAE;uBAC5C;wBAAC,WAAW,CAAC,EAAE;wBAAE;qBAAU,GAAG;wBAAC,YAAY;wBAAG;qBAAE,GAAG;oBACrD,GAAG,KAAK,CAAC,MAAM,GAAG,AAAC,GAA8C,OAA5C,aAAa,MAAM,OAAO,EAAE,QAAQ;oBACzD,IAAI,WAAW;wBACb,MAAM,CAAC,WAAW,WAAW,GAAG;4BAAC,KAAK,KAAK,GAAG;4BAAG,KAAK,MAAM,GAAG;yBAAE;wBACjE,MAAM,MAAM,OAAO,gBAAgB,CAAC,QAAQ,CAAC,EAAE,GAAG;wBAClD,MAAM,EACJ,oBAAoB,EACpB,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;wBACJ,MAAM,eAAe,mBAAmB,OAAO,kBAAkB;wBACjE,MAAM,kBAAkB,uBAAuB,AAAC,SAAyB,OAAjB,KAAI,eAA+C,OAAlC,QAAQ,CAAC,CAAC,QAAQ,IAAI,IAAI,IAAG,OAAiC,OAA5B,QAAQ,CAAC,MAAM,MAAM,IAAI,IAAG,SAAO,AAAC,cAAiB,OAAJ,KAAI;wBAChK,IAAI,SAAS,MAAM,OAAO,CAAC,WAAW;wBACtC,IAAI,QAAQ;4BACV,SAAS,OAAO,kBAAkB,CAAC,KAAK,GAAG,SAAS,GAAG,YAAY,CAAC,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,KAAK;4BACrG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,GAAG,GAAG;4BAChE,OAAO,QAAQ,CAAC,GAAG,GAAG;wBACxB;wBACA,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG;wBAC9B,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG;wBAChC,GAAG,KAAK,CAAC,WAAW,GAAG,uBAAuB,KAAK,AAAC,GAAM,OAAJ,KAAI;wBAC1D,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,EAAE;4BAC1D,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,AAAC,GAAoB,OAAlB,iBAA2C,OAAzB,cAAa,cAA2B,OAAf,WAAU,OAAgB,OAAX,YAAW;4BACpH,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,mBAAmB,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,GAAG;wBAC1G;oBACF,OAAO;wBACL,MAAM,QAAQ,mBAAmB,YAAY,IAAI,YAAY,MAAM,OAAO,EAAE,UAAU;wBACtF,GAAG,KAAK,CAAC,SAAS,GAAG,AAAC,eAA0B,OAAZ,GAAG,CAAC,EAAE,EAAC,OAA0B,OAArB,GAAG,CAAC,EAAE,EAAC,gBAAoB,OAAN,OAAM;oBAC7E;oBACA,YAAY,OAAO,GAAG;oBACtB,QAAQ,OAAO,GAAG,OAAO,IAAI;gBAC/B;YACF;YACA,IAAI,CAAC,sBAAsB,iBAAiB,OAAO,IAAI,CAAC,cAAc,OAAO,EAAE;gBAC7E,IAAI,WAAW;oBACb,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,MAAM,KAAK,kBAAkB,OAAO,CAAC,QAAQ,CAAC,EAAE;wBAChD,IAAI,MAAM,QAAQ,GAAG,WAAW,IAAI,MAAM,QAAQ,GAAG,YAAY,EAAE;4BACjE,MAAM,EACJ,oBAAoB,EACrB,GAAG;4BACJ,IAAI,wBAAwB,UAAU;gCACpC,IAAI,MAAM,KAAK,EAAE;oCACf,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG;wCAC/B,iBAAiB,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,MAAM,KAAK;oCAC1D,OAAO,IAAI,MAAM,KAAK,YAAY,kJAAA,CAAA,UAAO,EAAE;wCACzC,iBAAiB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC;oCACvE,OAAO;wCACL,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE;oCAC/F;gCACF;4BACF,OAAO;gCACL,MAAM,QAAQ,CAAC,kBAAkB,EAAE,IAAI;gCACvC,MAAM,IAAI,GAAG,WAAW,GAAG;gCAC3B,MAAM,IAAI,GAAG,YAAY,GAAG;gCAC5B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;4BAC3C;4BACA,cAAc,OAAO,GAAG;wBAC1B;oBACF;gBACF,OAAO;oBACL,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE;oBAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,IAAI,OAAO,QAAQ,IAAI,YAAY,EAAE;wBACrE,MAAM,QAAQ,IAAI,SAAS,MAAM;wBACjC,MAAM,IAAI,IAAI,WAAW,GAAG;wBAC5B,MAAM,IAAI,IAAI,YAAY,GAAG;wBAC7B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;wBACzC,cAAc,OAAO,GAAG;oBAC1B;oBACA,iBAAiB,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,QAAQ;gBACpD;YACF;QACF;;IACA,MAAM,UAAU,6JAAA,CAAA,UAAa;iCAAC,IAAM,CAAC;gBACnC,cAAc,CAAC,YAAuB,u8CAiChC;gBACN,gBAA2B;YAK7B,CAAC;gCAAG;QAAC;KAAU;IACf,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACnE,KAAK;IACP,IAAI,WAAW,CAAC,sBAAsB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC7E,YAAY;QACZ,eAAe;QACf,KAAK;IACP,GAAG,YAAY,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iBAAiB,OAAO,YAAY,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,kBAAkB;QACrI,MAAM,kJAAA,CAAA,aAAU;QAChB,cAAc,QAAQ,YAAY;QAClC,gBAAgB,QAAQ,cAAc;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/Progress.js"], "sourcesContent": ["import * as React from 'react';\nimport { DefaultLoadingManager } from 'three';\nimport { create } from 'zustand';\n\nlet saveLastTotalLoaded = 0;\nconst useProgress = /* @__PURE__ */create(set => {\n  DefaultLoadingManager.onStart = (item, loaded, total) => {\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100\n    });\n  };\n  DefaultLoadingManager.onLoad = () => {\n    set({\n      active: false\n    });\n  };\n  DefaultLoadingManager.onError = item => set(state => ({\n    errors: [...state.errors, item]\n  }));\n  DefaultLoadingManager.onProgress = (item, loaded, total) => {\n    if (loaded === total) {\n      saveLastTotalLoaded = total;\n    }\n    set({\n      active: true,\n      item,\n      loaded,\n      total,\n      progress: (loaded - saveLastTotalLoaded) / (total - saveLastTotalLoaded) * 100 || 100\n    });\n  };\n  return {\n    errors: [],\n    active: false,\n    progress: 0,\n    item: '',\n    loaded: 0,\n    total: 0\n  };\n});\n\n//\n\nfunction Progress({\n  children\n}) {\n  const result = useProgress();\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(result));\n}\n\nexport { Progress, useProgress };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,IAAI,sBAAsB;AAC1B,MAAM,cAAc,aAAa,GAAE,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAE,CAAA;IACxC,kJAAA,CAAA,wBAAqB,CAAC,OAAO,GAAG,CAAC,MAAM,QAAQ;QAC7C,IAAI;YACF,QAAQ;YACR;YACA;YACA;YACA,UAAU,CAAC,SAAS,mBAAmB,IAAI,CAAC,QAAQ,mBAAmB,IAAI;QAC7E;IACF;IACA,kJAAA,CAAA,wBAAqB,CAAC,MAAM,GAAG;QAC7B,IAAI;YACF,QAAQ;QACV;IACF;IACA,kJAAA,CAAA,wBAAqB,CAAC,OAAO,GAAG,CAAA,OAAQ,IAAI,CAAA,QAAS,CAAC;gBACpD,QAAQ;uBAAI,MAAM,MAAM;oBAAE;iBAAK;YACjC,CAAC;IACD,kJAAA,CAAA,wBAAqB,CAAC,UAAU,GAAG,CAAC,MAAM,QAAQ;QAChD,IAAI,WAAW,OAAO;YACpB,sBAAsB;QACxB;QACA,IAAI;YACF,QAAQ;YACR;YACA;YACA;YACA,UAAU,CAAC,SAAS,mBAAmB,IAAI,CAAC,QAAQ,mBAAmB,IAAI,OAAO;QACpF;IACF;IACA,OAAO;QACL,QAAQ,EAAE;QACV,QAAQ;QACR,UAAU;QACV,MAAM;QACN,QAAQ;QACR,OAAO;IACT;AACF;AAEA,EAAE;AAEF,SAAS,SAAS,KAEjB;QAFiB,EAChB,QAAQ,EACT,GAFiB;IAGhB,MAAM,SAAS;IACf,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,YAAY,OAAO,KAAK,IAAI,SAAS;AACrG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 915, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/OrbitControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { OrbitControls as OrbitControls$1 } from 'three-stdlib';\n\nconst OrbitControls = /* @__PURE__ */React.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new OrbitControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\n\nexport { OrbitControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,aAAa,GAAE,6JAAA,CAAA,aAAgB,CAAC,QAWnD;QAXoD,EACrD,WAAW,EACX,MAAM,EACN,OAAO,EACP,UAAU,EACV,gBAAgB,IAAI,EACpB,YAAY,KAAK,EACjB,QAAQ,EACR,OAAO,EACP,KAAK,EACL,GAAG,WACJ;IACC,MAAM,aAAa,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;8CAAE,CAAA,QAAS,MAAM,UAAU;;IACrD,MAAM,gBAAgB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;iDAAE,CAAA,QAAS,MAAM,MAAM;;IACpD,MAAM,KAAK,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;sCAAE,CAAA,QAAS,MAAM,EAAE;;IACrC,MAAM,SAAS,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;0CAAE,CAAA,QAAS,MAAM,MAAM;;IAC7C,MAAM,YAAY,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;6CAAE,CAAA,QAAS,MAAM,SAAS;;IACnD,MAAM,MAAM,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;uCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,MAAM,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;uCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,cAAc,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;+CAAE,CAAA,QAAS,MAAM,WAAW;;IACvD,MAAM,aAAa,UAAU;IAC7B,MAAM,iBAAiB,cAAc,OAAO,SAAS,IAAI,GAAG,UAAU;IACtE,MAAM,WAAW,6JAAA,CAAA,UAAa;2CAAC,IAAM,IAAI,+JAAA,CAAA,gBAAe,CAAC;0CAAa;QAAC;KAAW;IAClF,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE;YACP,IAAI,SAAS,OAAO,EAAE,SAAS,MAAM;QACvC;iCAAG,CAAC;IACJ,6JAAA,CAAA,YAAe;mCAAC;YACd,IAAI,WAAW;gBACb,SAAS,OAAO,CAAC,cAAc,OAAO,iBAAiB;YACzD;YACA,SAAS,OAAO,CAAC;YACjB;2CAAO,IAAM,KAAK,SAAS,OAAO;;QACpC;kCAAG;QAAC;QAAW;QAAgB;QAAS;QAAU;KAAW;IAC7D,6JAAA,CAAA,YAAe;mCAAC;YACd,MAAM;oDAAW,CAAA;oBACf;oBACA,IAAI,SAAS,YAAY,OAAO;oBAChC,IAAI,UAAU,SAAS;gBACzB;;YACA,MAAM;qDAAY,CAAA;oBAChB,IAAI,SAAS,QAAQ;gBACvB;;YACA,MAAM;mDAAU,CAAA;oBACd,IAAI,OAAO,MAAM;gBACnB;;YACA,SAAS,gBAAgB,CAAC,UAAU;YACpC,SAAS,gBAAgB,CAAC,SAAS;YACnC,SAAS,gBAAgB,CAAC,OAAO;YACjC;2CAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS;oBACtC,SAAS,mBAAmB,CAAC,OAAO;oBACpC,SAAS,mBAAmB,CAAC,UAAU;gBACzC;;QACF;kCAAG;QAAC;QAAU;QAAS;QAAO;QAAU;QAAY;KAAU;IAC9D,6JAAA,CAAA,YAAe;mCAAC;YACd,IAAI,aAAa;gBACf,MAAM,MAAM,MAAM,QAAQ;gBAC1B,qEAAqE;gBACrE,IAAI;oBACF;gBACF;gBACA;+CAAO,IAAM,IAAI;4BACf,UAAU;wBACZ;;YACF;QACF;kCAAG;QAAC;QAAa;KAAS;IAC1B,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,KAAK;QACL,QAAQ;QACR,eAAe;IACjB,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/Fbo.js"], "sourcesContent": ["import * as React from 'react';\nimport { forwardRef, useImperativeHandle } from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(/** Width in pixels, or settings (will render fullscreen by default) */\nwidth, /** Height in pixels */\nheight, /**Settings */\nsettings) {\n  const size = useThree(state => state.size);\n  const viewport = useThree(state => state.viewport);\n  const _width = typeof width === 'number' ? width : size.width * viewport.dpr;\n  const _height = typeof height === 'number' ? height : size.height * viewport.dpr;\n  const _settings = (typeof width === 'number' ? settings : width) || {};\n  const {\n    samples = 0,\n    depth,\n    ...targetSettings\n  } = _settings;\n  const depthBuffer = depth !== null && depth !== void 0 ? depth : _settings.depthBuffer; // backwards compatibility for deprecated `depth` prop\n\n  const target = React.useMemo(() => {\n    const target = new THREE.WebGLRenderTarget(_width, _height, {\n      minFilter: THREE.LinearFilter,\n      magFilter: THREE.LinearFilter,\n      type: THREE.HalfFloatType,\n      ...targetSettings\n    });\n    if (depthBuffer) {\n      target.depthTexture = new THREE.DepthTexture(_width, _height, THREE.FloatType);\n    }\n    target.samples = samples;\n    return target;\n  }, []);\n  React.useLayoutEffect(() => {\n    target.setSize(_width, _height);\n    if (samples) target.samples = samples;\n  }, [samples, target, _width, _height]);\n  React.useEffect(() => {\n    return () => target.dispose();\n  }, []);\n  return target;\n}\n\n//\n// Fbo component\n//\n\nconst Fbo = /* @__PURE__ */forwardRef(({\n  children,\n  width,\n  height,\n  ...settings\n}, fref) => {\n  const target = useFBO(width, height, settings);\n  useImperativeHandle(fref, () => target, [target]); // expose target through ref\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children == null ? void 0 : children(target));\n});\n\nexport { Fbo, useFBO };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;;AAEA,2DAA2D;AAC3D,iDAAiD;AACjD,SAAS,OAAO,qEAAqE,GACrF,KAAK,EAAE,qBAAqB,GAC5B,MAAM,EAAE,YAAY,GACpB,QAAQ;IACN,MAAM,OAAO,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;iCAAE,CAAA,QAAS,MAAM,IAAI;;IACzC,MAAM,WAAW,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;qCAAE,CAAA,QAAS,MAAM,QAAQ;;IACjD,MAAM,SAAS,OAAO,UAAU,WAAW,QAAQ,KAAK,KAAK,GAAG,SAAS,GAAG;IAC5E,MAAM,UAAU,OAAO,WAAW,WAAW,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG;IAChF,MAAM,YAAY,CAAC,OAAO,UAAU,WAAW,WAAW,KAAK,KAAK,CAAC;IACrE,MAAM,EACJ,UAAU,CAAC,EACX,KAAK,EACL,GAAG,gBACJ,GAAG;IACJ,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,QAAQ,UAAU,WAAW,EAAE,sDAAsD;IAE9I,MAAM,SAAS,6JAAA,CAAA,UAAa;kCAAC;YAC3B,MAAM,SAAS,IAAI,kJAAA,CAAA,oBAAuB,CAAC,QAAQ,SAAS;gBAC1D,WAAW,kJAAA,CAAA,eAAkB;gBAC7B,WAAW,kJAAA,CAAA,eAAkB;gBAC7B,MAAM,kJAAA,CAAA,gBAAmB;gBACzB,GAAG,cAAc;YACnB;YACA,IAAI,aAAa;gBACf,OAAO,YAAY,GAAG,IAAI,kJAAA,CAAA,eAAkB,CAAC,QAAQ,SAAS,kJAAA,CAAA,YAAe;YAC/E;YACA,OAAO,OAAO,GAAG;YACjB,OAAO;QACT;iCAAG,EAAE;IACL,6JAAA,CAAA,kBAAqB;kCAAC;YACpB,OAAO,OAAO,CAAC,QAAQ;YACvB,IAAI,SAAS,OAAO,OAAO,GAAG;QAChC;iCAAG;QAAC;QAAS;QAAQ;QAAQ;KAAQ;IACrC,6JAAA,CAAA,YAAe;4BAAC;YACd;oCAAO,IAAM,OAAO,OAAO;;QAC7B;2BAAG,EAAE;IACL,OAAO;AACT;AAEA,EAAE;AACF,gBAAgB;AAChB,EAAE;AAEF,MAAM,MAAM,aAAa,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,QAKnC;QALoC,EACrC,QAAQ,EACR,KAAK,EACL,MAAM,EACN,GAAG,UACJ;IACC,MAAM,SAAS,OAAO,OAAO,QAAQ;IACrC,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;mCAAM,IAAM;kCAAQ;QAAC;KAAO,GAAG,4BAA4B;IAE/E,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,YAAY,OAAO,KAAK,IAAI,SAAS;AACrG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/PerspectiveCamera.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './Fbo.js';\n\nconst isFunction = node => typeof node === 'function';\nconst PerspectiveCamera = /* @__PURE__ */React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  makeDefault,\n  children,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => cameraRef.current, []);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.aspect = size.width / size.height;\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"perspectiveCamera\", _extends({\n    ref: cameraRef\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\nexport { PerspectiveCamera };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEA,MAAM,aAAa,CAAA,OAAQ,OAAO,SAAS;AAC3C,MAAM,oBAAoB,aAAa,GAAE,6JAAA,CAAA,aAAgB,CAAC,QAOvD;QAPwD,EACzD,MAAM,EACN,aAAa,GAAG,EAChB,SAAS,QAAQ,EACjB,WAAW,EACX,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,MAAM,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;2CAAE;gBAAC,EACpB,GAAG,EACJ;mBAAK;;;IACN,MAAM,SAAS,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;8CAAE;gBAAC,EACvB,MAAM,EACP;mBAAK;;;IACN,MAAM,OAAO,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;4CAAE;gBAAC,EACrB,IAAI,EACL;mBAAK;;;IACN,MAAM,YAAY,6JAAA,CAAA,SAAY,CAAC;IAC/B,6JAAA,CAAA,sBAAyB,CAAC;iDAAK,IAAM,UAAU,OAAO;gDAAE,EAAE;IAC1D,MAAM,WAAW,6JAAA,CAAA,SAAY,CAAC;IAC9B,MAAM,MAAM,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,6JAAA,CAAA,kBAAqB;6CAAC;YACpB,IAAI,CAAC,MAAM,MAAM,EAAE;gBACjB,UAAU,OAAO,CAAC,MAAM,GAAG,KAAK,KAAK,GAAG,KAAK,MAAM;YACrD;QACF;4CAAG;QAAC;QAAM;KAAM;IAChB,6JAAA,CAAA,kBAAqB;6CAAC;YACpB,UAAU,OAAO,CAAC,sBAAsB;QAC1C;;IACA,IAAI,QAAQ;IACZ,IAAI,YAAY;IAChB,MAAM,aAAa,WAAW;IAC9B,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;sCAAE,CAAA;YACP,IAAI,cAAc,CAAC,WAAW,YAAY,QAAQ,MAAM,GAAG;gBACzD,SAAS,OAAO,CAAC,OAAO,GAAG;gBAC3B,MAAM,EAAE,CAAC,eAAe,CAAC;gBACzB,YAAY,MAAM,KAAK,CAAC,UAAU;gBAClC,IAAI,QAAQ,MAAM,KAAK,CAAC,UAAU,GAAG;gBACrC,MAAM,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,EAAE,UAAU,OAAO;gBAC9C,MAAM,KAAK,CAAC,UAAU,GAAG;gBACzB,MAAM,EAAE,CAAC,eAAe,CAAC;gBACzB,SAAS,OAAO,CAAC,OAAO,GAAG;gBAC3B;YACF;QACF;;IACA,6JAAA,CAAA,kBAAqB;6CAAC;YACpB,IAAI,aAAa;gBACf,MAAM,SAAS;gBACf;yDAAI,IAAM,CAAC;4BACT,QAAQ,UAAU,OAAO;wBAC3B,CAAC;;gBACD;yDAAO,IAAM;iEAAI,IAAM,CAAC;oCACtB,QAAQ;gCACV,CAAC;;;YACH;QACA,4GAA4G;QAC5G,wEAAwE;QAC1E;4CAAG;QAAC;QAAW;QAAa;KAAI;IAChC,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,qBAAqB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC3H,KAAK;IACP,GAAG,QAAQ,CAAC,cAAc,WAAW,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC7E,KAAK;IACP,GAAG,cAAc,SAAS,IAAI,OAAO;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/ContactShadows.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { HorizontalBlurShader, VerticalBlurShader } from 'three-stdlib';\n\nconst ContactShadows = /* @__PURE__ */React.forwardRef(({\n  scale = 10,\n  frames = Infinity,\n  opacity = 1,\n  width = 1,\n  height = 1,\n  blur = 1,\n  near = 0,\n  far = 10,\n  resolution = 512,\n  smooth = true,\n  color = '#000000',\n  depthWrite = false,\n  renderOrder,\n  ...props\n}, fref) => {\n  const ref = React.useRef(null);\n  const scene = useThree(state => state.scene);\n  const gl = useThree(state => state.gl);\n  const shadowCamera = React.useRef(null);\n  width = width * (Array.isArray(scale) ? scale[0] : scale || 1);\n  height = height * (Array.isArray(scale) ? scale[1] : scale || 1);\n  const [renderTarget, planeGeometry, depthMaterial, blurPlane, horizontalBlurMaterial, verticalBlurMaterial, renderTargetBlur] = React.useMemo(() => {\n    const renderTarget = new THREE.WebGLRenderTarget(resolution, resolution);\n    const renderTargetBlur = new THREE.WebGLRenderTarget(resolution, resolution);\n    renderTargetBlur.texture.generateMipmaps = renderTarget.texture.generateMipmaps = false;\n    const planeGeometry = new THREE.PlaneGeometry(width, height).rotateX(Math.PI / 2);\n    const blurPlane = new THREE.Mesh(planeGeometry);\n    const depthMaterial = new THREE.MeshDepthMaterial();\n    depthMaterial.depthTest = depthMaterial.depthWrite = false;\n    depthMaterial.onBeforeCompile = shader => {\n      shader.uniforms = {\n        ...shader.uniforms,\n        ucolor: {\n          value: new THREE.Color(color)\n        }\n      };\n      shader.fragmentShader = shader.fragmentShader.replace(`void main() {`,\n      //\n      `uniform vec3 ucolor;\n           void main() {\n          `);\n      shader.fragmentShader = shader.fragmentShader.replace('vec4( vec3( 1.0 - fragCoordZ ), opacity );',\n      // Colorize the shadow, multiply by the falloff so that the center can remain darker\n      'vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );');\n    };\n    const horizontalBlurMaterial = new THREE.ShaderMaterial(HorizontalBlurShader);\n    const verticalBlurMaterial = new THREE.ShaderMaterial(VerticalBlurShader);\n    verticalBlurMaterial.depthTest = horizontalBlurMaterial.depthTest = false;\n    return [renderTarget, planeGeometry, depthMaterial, blurPlane, horizontalBlurMaterial, verticalBlurMaterial, renderTargetBlur];\n  }, [resolution, width, height, scale, color]);\n  const blurShadows = blur => {\n    blurPlane.visible = true;\n    blurPlane.material = horizontalBlurMaterial;\n    horizontalBlurMaterial.uniforms.tDiffuse.value = renderTarget.texture;\n    horizontalBlurMaterial.uniforms.h.value = blur * 1 / 256;\n    gl.setRenderTarget(renderTargetBlur);\n    gl.render(blurPlane, shadowCamera.current);\n    blurPlane.material = verticalBlurMaterial;\n    verticalBlurMaterial.uniforms.tDiffuse.value = renderTargetBlur.texture;\n    verticalBlurMaterial.uniforms.v.value = blur * 1 / 256;\n    gl.setRenderTarget(renderTarget);\n    gl.render(blurPlane, shadowCamera.current);\n    blurPlane.visible = false;\n  };\n  let count = 0;\n  let initialBackground;\n  let initialOverrideMaterial;\n  useFrame(() => {\n    if (shadowCamera.current && (frames === Infinity || count < frames)) {\n      count++;\n      initialBackground = scene.background;\n      initialOverrideMaterial = scene.overrideMaterial;\n      ref.current.visible = false;\n      scene.background = null;\n      scene.overrideMaterial = depthMaterial;\n      gl.setRenderTarget(renderTarget);\n      gl.render(scene, shadowCamera.current);\n      blurShadows(blur);\n      if (smooth) blurShadows(blur * 0.4);\n      gl.setRenderTarget(null);\n      ref.current.visible = true;\n      scene.overrideMaterial = initialOverrideMaterial;\n      scene.background = initialBackground;\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    \"rotation-x\": Math.PI / 2\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"mesh\", {\n    renderOrder: renderOrder,\n    geometry: planeGeometry,\n    scale: [1, -1, 1],\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    map: renderTarget.texture,\n    opacity: opacity,\n    depthWrite: depthWrite\n  })), /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    ref: shadowCamera,\n    args: [-width / 2, width / 2, height / 2, -height / 2, near, far]\n  }));\n});\n\nexport { ContactShadows };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;;;;AAEA,MAAM,iBAAiB,aAAa,GAAE,6JAAA,CAAA,aAAgB,CAAC,QAepD;QAfqD,EACtD,QAAQ,EAAE,EACV,SAAS,QAAQ,EACjB,UAAU,CAAC,EACX,QAAQ,CAAC,EACT,SAAS,CAAC,EACV,OAAO,CAAC,EACR,OAAO,CAAC,EACR,MAAM,EAAE,EACR,aAAa,GAAG,EAChB,SAAS,IAAI,EACb,QAAQ,SAAS,EACjB,aAAa,KAAK,EAClB,WAAW,EACX,GAAG,OACJ;IACC,MAAM,MAAM,6JAAA,CAAA,SAAY,CAAC;IACzB,MAAM,QAAQ,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;0CAAE,CAAA,QAAS,MAAM,KAAK;;IAC3C,MAAM,KAAK,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;uCAAE,CAAA,QAAS,MAAM,EAAE;;IACrC,MAAM,eAAe,6JAAA,CAAA,SAAY,CAAC;IAClC,QAAQ,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC;IAC7D,SAAS,SAAS,CAAC,MAAM,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC;IAC/D,MAAM,CAAC,cAAc,eAAe,eAAe,WAAW,wBAAwB,sBAAsB,iBAAiB,GAAG,6JAAA,CAAA,UAAa;kCAAC;YAC5I,MAAM,eAAe,IAAI,kJAAA,CAAA,oBAAuB,CAAC,YAAY;YAC7D,MAAM,mBAAmB,IAAI,kJAAA,CAAA,oBAAuB,CAAC,YAAY;YACjE,iBAAiB,OAAO,CAAC,eAAe,GAAG,aAAa,OAAO,CAAC,eAAe,GAAG;YAClF,MAAM,gBAAgB,IAAI,kJAAA,CAAA,gBAAmB,CAAC,OAAO,QAAQ,OAAO,CAAC,KAAK,EAAE,GAAG;YAC/E,MAAM,YAAY,IAAI,kJAAA,CAAA,OAAU,CAAC;YACjC,MAAM,gBAAgB,IAAI,kJAAA,CAAA,oBAAuB;YACjD,cAAc,SAAS,GAAG,cAAc,UAAU,GAAG;YACrD,cAAc,eAAe;0CAAG,CAAA;oBAC9B,OAAO,QAAQ,GAAG;wBAChB,GAAG,OAAO,QAAQ;wBAClB,QAAQ;4BACN,OAAO,IAAI,kJAAA,CAAA,QAAW,CAAC;wBACzB;oBACF;oBACA,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,OAAO,CAAE,iBAEtD;oBAGD,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,OAAO,CAAC,8CACtD,oFAAoF;oBACpF;gBACF;;YACA,MAAM,yBAAyB,IAAI,kJAAA,CAAA,iBAAoB,CAAC,qKAAA,CAAA,uBAAoB;YAC5E,MAAM,uBAAuB,IAAI,kJAAA,CAAA,iBAAoB,CAAC,mKAAA,CAAA,qBAAkB;YACxE,qBAAqB,SAAS,GAAG,uBAAuB,SAAS,GAAG;YACpE,OAAO;gBAAC;gBAAc;gBAAe;gBAAe;gBAAW;gBAAwB;gBAAsB;aAAiB;QAChI;iCAAG;QAAC;QAAY;QAAO;QAAQ;QAAO;KAAM;IAC5C,MAAM,cAAc,CAAA;QAClB,UAAU,OAAO,GAAG;QACpB,UAAU,QAAQ,GAAG;QACrB,uBAAuB,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,aAAa,OAAO;QACrE,uBAAuB,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,IAAI;QACrD,GAAG,eAAe,CAAC;QACnB,GAAG,MAAM,CAAC,WAAW,aAAa,OAAO;QACzC,UAAU,QAAQ,GAAG;QACrB,qBAAqB,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,iBAAiB,OAAO;QACvE,qBAAqB,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,OAAO,IAAI;QACnD,GAAG,eAAe,CAAC;QACnB,GAAG,MAAM,CAAC,WAAW,aAAa,OAAO;QACzC,UAAU,OAAO,GAAG;IACtB;IACA,IAAI,QAAQ;IACZ,IAAI;IACJ,IAAI;IACJ,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;mCAAE;YACP,IAAI,aAAa,OAAO,IAAI,CAAC,WAAW,YAAY,QAAQ,MAAM,GAAG;gBACnE;gBACA,oBAAoB,MAAM,UAAU;gBACpC,0BAA0B,MAAM,gBAAgB;gBAChD,IAAI,OAAO,CAAC,OAAO,GAAG;gBACtB,MAAM,UAAU,GAAG;gBACnB,MAAM,gBAAgB,GAAG;gBACzB,GAAG,eAAe,CAAC;gBACnB,GAAG,MAAM,CAAC,OAAO,aAAa,OAAO;gBACrC,YAAY;gBACZ,IAAI,QAAQ,YAAY,OAAO;gBAC/B,GAAG,eAAe,CAAC;gBACnB,IAAI,OAAO,CAAC,OAAO,GAAG;gBACtB,MAAM,gBAAgB,GAAG;gBACzB,MAAM,UAAU,GAAG;YACrB;QACF;;IACA,6JAAA,CAAA,sBAAyB,CAAC;8CAAM,IAAM,IAAI,OAAO;6CAAE,EAAE;IACrD,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACxD,cAAc,KAAK,EAAE,GAAG;IAC1B,GAAG,OAAO;QACR,KAAK;IACP,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,aAAa;QACb,UAAU;QACV,OAAO;YAAC;YAAG,CAAC;YAAG;SAAE;QACjB,UAAU;YAAC,CAAC,KAAK,EAAE,GAAG;YAAG;YAAG;SAAE;IAChC,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,qBAAqB;QACvD,aAAa;QACb,KAAK,aAAa,OAAO;QACzB,SAAS;QACT,YAAY;IACd,KAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,sBAAsB;QAC1D,KAAK;QACL,MAAM;YAAC,CAAC,QAAQ;YAAG,QAAQ;YAAG,SAAS;YAAG,CAAC,SAAS;YAAG;YAAM;SAAI;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/Clone.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { SkeletonUtils } from 'three-stdlib';\n\nfunction createSpread(child, {\n  keys = ['near', 'far', 'color', 'distance', 'decay', 'penumbra', 'angle', 'intensity', 'skeleton', 'visible', 'castShadow', 'receiveShadow', 'morphTargetDictionary', 'morphTargetInfluences', 'name', 'geometry', 'material', 'position', 'rotation', 'scale', 'up', 'userData', 'bindMode', 'bindMatrix', 'bindMatrixInverse', 'skeleton'],\n  deep,\n  inject,\n  castShadow,\n  receiveShadow\n}) {\n  let spread = {};\n  for (const key of keys) {\n    spread[key] = child[key];\n  }\n  if (deep) {\n    if (spread.geometry && deep !== 'materialsOnly') spread.geometry = spread.geometry.clone();\n    if (spread.material && deep !== 'geometriesOnly') spread.material = spread.material.clone();\n  }\n  if (inject) {\n    if (typeof inject === 'function') spread = {\n      ...spread,\n      children: inject(child)\n    };else if (/*#__PURE__*/React.isValidElement(inject)) spread = {\n      ...spread,\n      children: inject\n    };else spread = {\n      ...spread,\n      ...inject\n    };\n  }\n  if (child instanceof THREE.Mesh) {\n    if (castShadow) spread.castShadow = true;\n    if (receiveShadow) spread.receiveShadow = true;\n  }\n  return spread;\n}\nconst Clone = /* @__PURE__ */React.forwardRef(({\n  isChild = false,\n  object,\n  children,\n  deep,\n  castShadow,\n  receiveShadow,\n  inject,\n  keys,\n  ...props\n}, forwardRef) => {\n  const config = {\n    keys,\n    deep,\n    inject,\n    castShadow,\n    receiveShadow\n  };\n  object = React.useMemo(() => {\n    if (isChild === false && !Array.isArray(object)) {\n      let isSkinned = false;\n      object.traverse(object => {\n        if (object.isSkinnedMesh) isSkinned = true;\n      });\n      if (isSkinned) return SkeletonUtils.clone(object);\n    }\n    return object;\n  }, [object, isChild]);\n\n  // Deal with arrayed clones\n  if (Array.isArray(object)) {\n    return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n      ref: forwardRef\n    }), object.map(o => /*#__PURE__*/React.createElement(Clone, _extends({\n      key: o.uuid,\n      object: o\n    }, config))), children);\n  }\n\n  // Singleton clones\n  const {\n    children: injectChildren,\n    ...spread\n  } = createSpread(object, config);\n  const Element = object.type[0].toLowerCase() + object.type.slice(1);\n  return /*#__PURE__*/React.createElement(Element, _extends({}, spread, props, {\n    ref: forwardRef\n  }), object.children.map(child => {\n    if (child.type === 'Bone') return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n      key: child.uuid,\n      object: child\n    }, config));\n    return /*#__PURE__*/React.createElement(Clone, _extends({\n      key: child.uuid,\n      object: child\n    }, config, {\n      isChild: true\n    }));\n  }), children, injectChildren);\n});\n\nexport { Clone };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,SAAS,aAAa,KAAK,EAAE,KAM5B;QAN4B,EAC3B,OAAO;QAAC;QAAQ;QAAO;QAAS;QAAY;QAAS;QAAY;QAAS;QAAa;QAAY;QAAW;QAAc;QAAiB;QAAyB;QAAyB;QAAQ;QAAY;QAAY;QAAY;QAAY;QAAS;QAAM;QAAY;QAAY;QAAc;QAAqB;KAAW,EAC5U,IAAI,EACJ,MAAM,EACN,UAAU,EACV,aAAa,EACd,GAN4B;IAO3B,IAAI,SAAS,CAAC;IACd,KAAK,MAAM,OAAO,KAAM;QACtB,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;IAC1B;IACA,IAAI,MAAM;QACR,IAAI,OAAO,QAAQ,IAAI,SAAS,iBAAiB,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,KAAK;QACxF,IAAI,OAAO,QAAQ,IAAI,SAAS,kBAAkB,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,KAAK;IAC3F;IACA,IAAI,QAAQ;QACV,IAAI,OAAO,WAAW,YAAY,SAAS;YACzC,GAAG,MAAM;YACT,UAAU,OAAO;QACnB;aAAO,IAAI,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,SAAS,SAAS;YAC7D,GAAG,MAAM;YACT,UAAU;QACZ;aAAO,SAAS;YACd,GAAG,MAAM;YACT,GAAG,MAAM;QACX;IACF;IACA,IAAI,iBAAiB,kJAAA,CAAA,OAAU,EAAE;QAC/B,IAAI,YAAY,OAAO,UAAU,GAAG;QACpC,IAAI,eAAe,OAAO,aAAa,GAAG;IAC5C;IACA,OAAO;AACT;AACA,MAAM,QAAQ,aAAa,GAAE,6JAAA,CAAA,aAAgB,CAAC,QAU3C;QAV4C,EAC7C,UAAU,KAAK,EACf,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,aAAa,EACb,MAAM,EACN,IAAI,EACJ,GAAG,OACJ;IACC,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;IACF;IACA,SAAS,6JAAA,CAAA,UAAa;yBAAC;YACrB,IAAI,YAAY,SAAS,CAAC,MAAM,OAAO,CAAC,SAAS;gBAC/C,IAAI,YAAY;gBAChB,OAAO,QAAQ;qCAAC,CAAA;wBACd,IAAI,OAAO,aAAa,EAAE,YAAY;oBACxC;;gBACA,IAAI,WAAW,OAAO,4JAAA,CAAA,gBAAa,CAAC,KAAK,CAAC;YAC5C;YACA,OAAO;QACT;wBAAG;QAAC;QAAQ;KAAQ;IAEpB,2BAA2B;IAC3B,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;YACnE,KAAK;QACP,IAAI,OAAO,GAAG,CAAC,CAAA,IAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;gBACnE,KAAK,EAAE,IAAI;gBACX,QAAQ;YACV,GAAG,WAAW;IAChB;IAEA,mBAAmB;IACnB,MAAM,EACJ,UAAU,cAAc,EACxB,GAAG,QACJ,GAAG,aAAa,QAAQ;IACzB,MAAM,UAAU,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC;IACjE,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,QAAQ,OAAO;QAC3E,KAAK;IACP,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA;QACtB,IAAI,MAAM,IAAI,KAAK,QAAQ,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YACvF,KAAK,MAAM,IAAI;YACf,QAAQ;QACV,GAAG;QACH,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YACtD,KAAK,MAAM,IAAI;YACf,QAAQ;QACV,GAAG,QAAQ;YACT,SAAS;QACX;IACF,IAAI,UAAU;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/Gltf.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { GLTFLoader, DRACOLoader, MeshoptDecoder } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\nimport { Clone } from './Clone.js';\n\nlet dracoLoader = null;\nlet decoderPath = 'https://www.gstatic.com/draco/versioned/decoders/1.5.5/';\nfunction extensions(useDraco = true, useMeshopt = true, extendLoader) {\n  return loader => {\n    if (extendLoader) {\n      extendLoader(loader);\n    }\n    if (useDraco) {\n      if (!dracoLoader) {\n        dracoLoader = new DRACOLoader();\n      }\n      dracoLoader.setDecoderPath(typeof useDraco === 'string' ? useDraco : decoderPath);\n      loader.setDRACOLoader(dracoLoader);\n    }\n    if (useMeshopt) {\n      loader.setMeshoptDecoder(typeof MeshoptDecoder === 'function' ? MeshoptDecoder() : MeshoptDecoder);\n    }\n  };\n}\nconst useGLTF = (path, useDraco, useMeshopt, extendLoader) => useLoader(GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.preload = (path, useDraco, useMeshopt, extendLoader) => useLoader.preload(GLTFLoader, path, extensions(useDraco, useMeshopt, extendLoader));\nuseGLTF.clear = path => useLoader.clear(GLTFLoader, path);\nuseGLTF.setDecoderPath = path => {\n  decoderPath = path;\n};\n\n//\n\nconst Gltf = /* @__PURE__ */React.forwardRef(({\n  src,\n  useDraco,\n  useMeshOpt,\n  extendLoader,\n  ...props\n}, ref) => {\n  const {\n    scene\n  } = useGLTF(src, useDraco, useMeshOpt, extendLoader);\n  return /*#__PURE__*/React.createElement(Clone, _extends({\n    ref: ref\n  }, props, {\n    object: scene\n  }));\n});\n\nexport { Gltf, useGLTF };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAEA,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,SAAS;QAAW,WAAA,iEAAW,MAAM,aAAA,iEAAa,MAAM;IACtD,OAAO,CAAA;QACL,IAAI,cAAc;YAChB,aAAa;QACf;QACA,IAAI,UAAU;YACZ,IAAI,CAAC,aAAa;gBAChB,cAAc,IAAI,4JAAA,CAAA,cAAW;YAC/B;YACA,YAAY,cAAc,CAAC,OAAO,aAAa,WAAW,WAAW;YACrE,OAAO,cAAc,CAAC;QACxB;QACA,IAAI,YAAY;YACd,OAAO,iBAAiB,CAAC,OAAO,4JAAA,CAAA,iBAAc,KAAK,aAAa,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,MAAM,4JAAA,CAAA,iBAAc;QACnG;IACF;AACF;AACA,MAAM,UAAU,CAAC,MAAM,UAAU,YAAY,eAAiB,CAAA,GAAA,mNAAA,CAAA,YAAS,AAAD,EAAE,2JAAA,CAAA,aAAU,EAAE,MAAM,WAAW,UAAU,YAAY;AAC3H,QAAQ,OAAO,GAAG,CAAC,MAAM,UAAU,YAAY,eAAiB,mNAAA,CAAA,YAAS,CAAC,OAAO,CAAC,2JAAA,CAAA,aAAU,EAAE,MAAM,WAAW,UAAU,YAAY;AACrI,QAAQ,KAAK,GAAG,CAAA,OAAQ,mNAAA,CAAA,YAAS,CAAC,KAAK,CAAC,2JAAA,CAAA,aAAU,EAAE;AACpD,QAAQ,cAAc,GAAG,CAAA;IACvB,cAAc;AAChB;AAEA,EAAE;AAEF,MAAM,OAAO,aAAa,GAAE,6JAAA,CAAA,aAAgB,CAAC,QAM1C;QAN2C,EAC5C,GAAG,EACH,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,GAAG,OACJ;IACC,MAAM,EACJ,KAAK,EACN,GAAG,QAAQ,KAAK,UAAU,YAAY;IACvC,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,QAAK,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACtD,KAAK;IACP,GAAG,OAAO;QACR,QAAQ;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/useAnimations.js"], "sourcesContent": ["import * as React from 'react';\nimport { Object3D, AnimationMixer } from 'three';\nimport { useFrame } from '@react-three/fiber';\n\nfunction useAnimations(clips, root) {\n  const ref = React.useRef(null);\n  const [actualRef] = React.useState(() => root ? root instanceof Object3D ? {\n    current: root\n  } : root : ref);\n  const [mixer] = React.useState(() => new AnimationMixer(undefined));\n  React.useLayoutEffect(() => {\n    if (root) actualRef.current = root instanceof Object3D ? root : root.current;\n    mixer._root = actualRef.current;\n  });\n  const lazyActions = React.useRef({});\n  const api = React.useMemo(() => {\n    const actions = {};\n    clips.forEach(clip => Object.defineProperty(actions, clip.name, {\n      enumerable: true,\n      get() {\n        if (actualRef.current) {\n          return lazyActions.current[clip.name] || (lazyActions.current[clip.name] = mixer.clipAction(clip, actualRef.current));\n        }\n      },\n      configurable: true\n    }));\n    return {\n      ref: actualRef,\n      clips,\n      actions,\n      names: clips.map(c => c.name),\n      mixer\n    };\n  }, [clips]);\n  useFrame((state, delta) => mixer.update(delta));\n  React.useEffect(() => {\n    const currentRoot = actualRef.current;\n    return () => {\n      // Clean up only when clips change, wipe out lazy actions and uncache clips\n      lazyActions.current = {};\n      mixer.stopAllAction();\n      Object.values(api.actions).forEach(action => {\n        if (currentRoot) {\n          mixer.uncacheAction(action, currentRoot);\n        }\n      });\n    };\n  }, [clips]);\n  return api;\n}\n\nexport { useAnimations };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,cAAc,KAAK,EAAE,IAAI;IAChC,MAAM,MAAM,6JAAA,CAAA,SAAY,CAAC;IACzB,MAAM,CAAC,UAAU,GAAG,6JAAA,CAAA,WAAc;kCAAC,IAAM,OAAO,gBAAgB,kJAAA,CAAA,WAAQ,GAAG;gBACzE,SAAS;YACX,IAAI,OAAO;;IACX,MAAM,CAAC,MAAM,GAAG,6JAAA,CAAA,WAAc;kCAAC,IAAM,IAAI,kJAAA,CAAA,iBAAc,CAAC;;IACxD,6JAAA,CAAA,kBAAqB;yCAAC;YACpB,IAAI,MAAM,UAAU,OAAO,GAAG,gBAAgB,kJAAA,CAAA,WAAQ,GAAG,OAAO,KAAK,OAAO;YAC5E,MAAM,KAAK,GAAG,UAAU,OAAO;QACjC;;IACA,MAAM,cAAc,6JAAA,CAAA,SAAY,CAAC,CAAC;IAClC,MAAM,MAAM,6JAAA,CAAA,UAAa;sCAAC;YACxB,MAAM,UAAU,CAAC;YACjB,MAAM,OAAO;8CAAC,CAAA,OAAQ,OAAO,cAAc,CAAC,SAAS,KAAK,IAAI,EAAE;wBAC9D,YAAY;wBACZ;4BACE,IAAI,UAAU,OAAO,EAAE;gCACrB,OAAO,YAAY,OAAO,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,KAAK,IAAI,CAAC,GAAG,MAAM,UAAU,CAAC,MAAM,UAAU,OAAO,CAAC;4BACtH;wBACF;wBACA,cAAc;oBAChB;;YACA,OAAO;gBACL,KAAK;gBACL;gBACA;gBACA,OAAO,MAAM,GAAG;kDAAC,CAAA,IAAK,EAAE,IAAI;;gBAC5B;YACF;QACF;qCAAG;QAAC;KAAM;IACV,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE,CAAC,OAAO,QAAU,MAAM,MAAM,CAAC;;IACxC,6JAAA,CAAA,YAAe;mCAAC;YACd,MAAM,cAAc,UAAU,OAAO;YACrC;2CAAO;oBACL,2EAA2E;oBAC3E,YAAY,OAAO,GAAG,CAAC;oBACvB,MAAM,aAAa;oBACnB,OAAO,MAAM,CAAC,IAAI,OAAO,EAAE,OAAO;mDAAC,CAAA;4BACjC,IAAI,aAAa;gCACf,MAAM,aAAa,CAAC,QAAQ;4BAC9B;wBACF;;gBACF;;QACF;kCAAG;QAAC;KAAM;IACV,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/%40react-three/drei/core/Text.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Text as Text$1, preloadFont } from 'troika-three-text';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\n\nconst Text = /* @__PURE__ */React.forwardRef(({\n  sdfGlyphSize = 64,\n  anchorX = 'center',\n  anchorY = 'middle',\n  font,\n  fontSize = 1,\n  children,\n  characters,\n  onSync,\n  ...props\n}, ref) => {\n  const invalidate = useThree(({\n    invalidate\n  }) => invalidate);\n  const [troikaMesh] = React.useState(() => new Text$1());\n  const [nodes, text] = React.useMemo(() => {\n    const n = [];\n    let t = '';\n    React.Children.forEach(children, child => {\n      if (typeof child === 'string' || typeof child === 'number') {\n        t += child;\n      } else {\n        n.push(child);\n      }\n    });\n    return [n, t];\n  }, [children]);\n  suspend(() => new Promise(res => preloadFont({\n    font,\n    characters\n  }, res)), ['troika-text', font, characters]);\n  React.useLayoutEffect(() => void troikaMesh.sync(() => {\n    invalidate();\n    if (onSync) onSync(troikaMesh);\n  }));\n  React.useEffect(() => {\n    return () => troikaMesh.dispose();\n  }, [troikaMesh]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: troikaMesh,\n    ref: ref,\n    font: font,\n    text: text,\n    anchorX: anchorX,\n    anchorY: anchorY,\n    fontSize: fontSize,\n    sdfGlyphSize: sdfGlyphSize\n  }, props), nodes);\n});\n\nexport { Text };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,OAAO,aAAa,GAAE,6JAAA,CAAA,aAAgB,CAAC,QAU1C;QAV2C,EAC5C,eAAe,EAAE,EACjB,UAAU,QAAQ,EAClB,UAAU,QAAQ,EAClB,IAAI,EACJ,WAAW,CAAC,EACZ,QAAQ,EACR,UAAU,EACV,MAAM,EACN,GAAG,OACJ;IACC,MAAM,aAAa,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;qCAAE;gBAAC,EAC3B,UAAU,EACX;mBAAK;;;IACN,MAAM,CAAC,WAAW,GAAG,6JAAA,CAAA,WAAc;yBAAC,IAAM,IAAI,oLAAA,CAAA,OAAM;;IACpD,MAAM,CAAC,OAAO,KAAK,GAAG,6JAAA,CAAA,UAAa;wBAAC;YAClC,MAAM,IAAI,EAAE;YACZ,IAAI,IAAI;YACR,6JAAA,CAAA,WAAc,CAAC,OAAO,CAAC;gCAAU,CAAA;oBAC/B,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;wBAC1D,KAAK;oBACP,OAAO;wBACL,EAAE,IAAI,CAAC;oBACT;gBACF;;YACA,OAAO;gBAAC;gBAAG;aAAE;QACf;uBAAG;QAAC;KAAS;IACb,CAAA,GAAA,4IAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,QAAQ,CAAA,MAAO,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE;gBAC3C;gBACA;YACF,GAAG,OAAO;QAAC;QAAe;QAAM;KAAW;IAC3C,6JAAA,CAAA,kBAAqB;gCAAC,IAAM,KAAK,WAAW,IAAI;wCAAC;oBAC/C;oBACA,IAAI,QAAQ,OAAO;gBACrB;;;IACA,6JAAA,CAAA,YAAe;0BAAC;YACd;kCAAO,IAAM,WAAW,OAAO;;QACjC;yBAAG;QAAC;KAAW;IACf,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,QAAQ;QACR,KAAK;QACL,MAAM;QACN,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,cAAc;IAChB,GAAG,QAAQ;AACb", "ignoreList": [0], "debugId": null}}]}