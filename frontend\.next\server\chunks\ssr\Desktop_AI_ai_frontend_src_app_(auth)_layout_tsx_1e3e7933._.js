module.exports = {

"[project]/Desktop/AI/ai/frontend/src/app/(auth)/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Auth layout for authentication pages
__turbopack_context__.s({
    "default": ()=>AuthLayout
});
var __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$AI$2f$ai$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
function AuthLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Desktop$2f$AI$2f$ai$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",
        children: children
    }, void 0, false, {
        fileName: "[project]/Desktop/AI/ai/frontend/src/app/(auth)/layout.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=Desktop_AI_ai_frontend_src_app_%28auth%29_layout_tsx_1e3e7933._.js.map