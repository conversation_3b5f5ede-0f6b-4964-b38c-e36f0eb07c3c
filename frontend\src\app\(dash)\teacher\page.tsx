'use client';

// Teacher Dashboard - Manage subjects, materials, and AI avatar

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BookOpen, 
  Users, 
  Upload, 
  Settings,
  MessageSquare,
  FileText,
  Bot,
  Plus,
  Eye,
  Edit
} from 'lucide-react';
import { useSubjects, useDataLoaders } from '@/store/useUser';
import { mockData } from '@/lib/api';

export default function TeacherDashboard() {
  const { data: session } = useSession();
  const router = useRouter();
  const subjects = useSubjects();
  const { loadSubjects } = useDataLoaders();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      await loadSubjects();
      setIsLoading(false);
    };
    loadData();
  }, [loadSubjects]);

  const handleCreateSubject = () => {
    router.push('/teacher/subjects/create');
  };

  const handleEditSubject = (subjectId: string) => {
    router.push(`/teacher/subjects/${subjectId}/edit`);
  };

  const handleManageMaterials = (subjectId: string) => {
    router.push(`/teacher/subjects/${subjectId}/materials`);
  };

  const handleSetupAvatar = () => {
    router.push('/teacher/avatar');
  };

  const handleViewChats = (subjectId: string) => {
    router.push(`/teacher/subjects/${subjectId}/chats`);
  };

  // Get teacher's subjects
  const teacherSubjects = subjects.filter(subject => 
    subject.teacherId === session?.user?.id
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-green-600 to-teal-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome, {session?.user?.name}!
        </h1>
        <p className="text-green-100">
          Manage your subjects, upload materials, and customize your AI avatar to enhance student learning.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BookOpen className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">My Subjects</p>
                <p className="text-2xl font-bold">{teacherSubjects.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Students</p>
                <p className="text-2xl font-bold">48</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FileText className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Materials</p>
                <p className="text-2xl font-bold">23</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <MessageSquare className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">AI Interactions</p>
                <p className="text-2xl font-bold">156</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks to manage your teaching</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button onClick={handleCreateSubject} className="h-20 flex-col space-y-2">
              <Plus className="h-6 w-6" />
              <span>Create Subject</span>
            </Button>
            <Button onClick={handleSetupAvatar} variant="outline" className="h-20 flex-col space-y-2">
              <Bot className="h-6 w-6" />
              <span>Setup AI Avatar</span>
            </Button>
            <Button onClick={() => router.push('/teacher/materials/upload')} variant="outline" className="h-20 flex-col space-y-2">
              <Upload className="h-6 w-6" />
              <span>Upload Materials</span>
            </Button>
            <Button onClick={() => router.push('/teacher/analytics')} variant="outline" className="h-20 flex-col space-y-2">
              <Eye className="h-6 w-6" />
              <span>View Analytics</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* My Subjects */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">My Subjects</h2>
          <Button onClick={handleCreateSubject}>
            <Plus className="h-4 w-4 mr-2" />
            Create New Subject
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {(teacherSubjects.length > 0 ? teacherSubjects : mockData.subjects).map((subject) => (
            <Card key={subject.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="text-lg">{subject.name}</span>
                  <Badge variant="secondary">Active</Badge>
                </CardTitle>
                <CardDescription>
                  {subject.description || 'No description provided'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Students enrolled:</span>
                    <span className="font-medium">24</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Materials:</span>
                    <span className="font-medium">8</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">AI Chats:</span>
                    <span className="font-medium">67</span>
                  </div>
                  
                  <div className="pt-2 space-y-2">
                    <div className="grid grid-cols-2 gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleEditSubject(subject.id)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleViewChats(subject.id)}
                      >
                        <MessageSquare className="h-4 w-4 mr-1" />
                        Chats
                      </Button>
                    </div>
                    <Button 
                      onClick={() => handleManageMaterials(subject.id)}
                      className="w-full"
                      size="sm"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Manage Materials
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* AI Avatar Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <span>AI Avatar Configuration</span>
          </CardTitle>
          <CardDescription>
            Customize your AI teaching assistant&apos;s appearance and personality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Bot className="h-6 w-6 text-white" />
              </div>
              <div>
                <p className="font-medium">Default Avatar</p>
                <p className="text-sm text-gray-600">Professional, Friendly tone</p>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleSetupAvatar}>
                <Settings className="h-4 w-4 mr-2" />
                Customize
              </Button>
              <Button onClick={() => router.push('/teacher/avatar/preview')}>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest interactions and updates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <div className="flex-1">
                <p className="font-medium">Student asked about grammar patterns</p>
                <p className="text-sm text-gray-600">Japanese Language - 30 minutes ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <Upload className="h-5 w-5 text-green-600" />
              <div className="flex-1">
                <p className="font-medium">New material uploaded</p>
                <p className="text-sm text-gray-600">Chapter 6 exercises - 2 hours ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <Users className="h-5 w-5 text-purple-600" />
              <div className="flex-1">
                <p className="font-medium">3 new students enrolled</p>
                <p className="text-sm text-gray-600">Mathematics course - 1 day ago</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
