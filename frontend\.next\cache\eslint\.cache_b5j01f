[{"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(auth)\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(auth)\\sign-in\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\admin\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\hod\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\chat\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\avatar\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\layout.tsx": "11", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\Blackboard.tsx": "13", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\Experience.tsx": "14", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\TeacherModel.tsx": "15", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\AuthProvider.tsx": "16", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\RouteGuard.tsx": "17", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\chat\\MessageList.tsx": "18", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\chat\\TypingBox.tsx": "19", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\alert.tsx": "20", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\badge.tsx": "21", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\button.tsx": "22", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\card.tsx": "23", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\input.tsx": "24", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\label.tsx": "25", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\select.tsx": "26", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\slider.tsx": "27", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\tabs.tsx": "28", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\textarea.tsx": "29", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\lib\\api.ts": "30", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\lib\\utils.ts": "31", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useAITeacher.ts": "32", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useChat.ts": "33", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useUser.ts": "34", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\types\\index.ts": "35", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\types\\next-auth.d.ts": "36"}, {"size": 255, "mtime": 1755185507434, "results": "37", "hashOfConfig": "38"}, {"size": 4692, "mtime": 1755409245261, "results": "39", "hashOfConfig": "38"}, {"size": 14646, "mtime": 1755409188974, "results": "40", "hashOfConfig": "38"}, {"size": 12461, "mtime": 1755187232244, "results": "41", "hashOfConfig": "38"}, {"size": 4534, "mtime": 1755185553450, "results": "42", "hashOfConfig": "38"}, {"size": 9186, "mtime": 1755437895590, "results": "43", "hashOfConfig": "38"}, {"size": 11247, "mtime": 1755437956652, "results": "44", "hashOfConfig": "38"}, {"size": 15317, "mtime": 1755438078392, "results": "45", "hashOfConfig": "38"}, {"size": 11836, "mtime": 1755438105459, "results": "46", "hashOfConfig": "38"}, {"size": 3166, "mtime": 1755187428583, "results": "47", "hashOfConfig": "38"}, {"size": 814, "mtime": 1755185591301, "results": "48", "hashOfConfig": "38"}, {"size": 1638, "mtime": 1755186440719, "results": "49", "hashOfConfig": "38"}, {"size": 6592, "mtime": 1755438186546, "results": "50", "hashOfConfig": "38"}, {"size": 5188, "mtime": 1755438213625, "results": "51", "hashOfConfig": "38"}, {"size": 7530, "mtime": 1755186121324, "results": "52", "hashOfConfig": "38"}, {"size": 297, "mtime": 1755185514199, "results": "53", "hashOfConfig": "38"}, {"size": 1861, "mtime": 1755185527629, "results": "54", "hashOfConfig": "38"}, {"size": 9229, "mtime": 1755438270397, "results": "55", "hashOfConfig": "38"}, {"size": 8373, "mtime": 1755187710692, "results": "56", "hashOfConfig": "38"}, {"size": 1584, "mtime": 1755186355864, "results": "57", "hashOfConfig": "38"}, {"size": 1127, "mtime": 1755435112205, "results": "58", "hashOfConfig": "38"}, {"size": 1835, "mtime": 1755186284610, "results": "59", "hashOfConfig": "38"}, {"size": 1849, "mtime": 1755186301693, "results": "60", "hashOfConfig": "38"}, {"size": 771, "mtime": 1755438321854, "results": "61", "hashOfConfig": "38"}, {"size": 710, "mtime": 1755186322551, "results": "62", "hashOfConfig": "38"}, {"size": 5615, "mtime": 1755186382032, "results": "63", "hashOfConfig": "38"}, {"size": 1077, "mtime": 1755186457868, "results": "64", "hashOfConfig": "38"}, {"size": 1883, "mtime": 1755186472035, "results": "65", "hashOfConfig": "38"}, {"size": 713, "mtime": 1755438346565, "results": "66", "hashOfConfig": "38"}, {"size": 9460, "mtime": 1755438418457, "results": "67", "hashOfConfig": "38"}, {"size": 6236, "mtime": 1755438442557, "results": "68", "hashOfConfig": "38"}, {"size": 5691, "mtime": 1755409117555, "results": "69", "hashOfConfig": "38"}, {"size": 7409, "mtime": 1755409362484, "results": "70", "hashOfConfig": "38"}, {"size": 10015, "mtime": 1755438676777, "results": "71", "hashOfConfig": "38"}, {"size": 4876, "mtime": 1755408908733, "results": "72", "hashOfConfig": "38"}, {"size": 545, "mtime": 1755187583515, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k6xgq6", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(auth)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(auth)\\sign-in\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\hod\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\chat\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\avatar\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], ["182", "183"], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\Blackboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\Experience.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\TeacherModel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\AuthProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\RouteGuard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\chat\\MessageList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\chat\\TypingBox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useAITeacher.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useChat.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useUser.ts", ["184", "185", "186", "187", "188", "189", "190", "191"], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\types\\next-auth.d.ts", [], [], {"ruleId": "192", "severity": 2, "message": "193", "line": 94, "column": 32, "nodeType": "194", "messageId": "195", "endLine": 94, "endColumn": 35, "suggestions": "196", "suppressions": "197"}, {"ruleId": "192", "severity": 2, "message": "193", "line": 104, "column": 39, "nodeType": "194", "messageId": "195", "endLine": 104, "endColumn": 42, "suggestions": "198", "suppressions": "199"}, {"ruleId": null, "message": "200", "line": 139, "column": 3, "severity": 1, "nodeType": null, "fix": "201"}, {"ruleId": "202", "severity": 1, "message": "203", "line": 153, "column": 6, "nodeType": "204", "endLine": 153, "endColumn": 8, "suggestions": "205"}, {"ruleId": null, "message": "200", "line": 155, "column": 3, "severity": 1, "nodeType": null, "fix": "206"}, {"ruleId": "202", "severity": 1, "message": "207", "line": 169, "column": 6, "nodeType": "204", "endLine": 169, "endColumn": 8, "suggestions": "208"}, {"ruleId": null, "message": "200", "line": 171, "column": 3, "severity": 1, "nodeType": null, "fix": "209"}, {"ruleId": "202", "severity": 1, "message": "210", "line": 185, "column": 6, "nodeType": "204", "endLine": 185, "endColumn": 8, "suggestions": "211"}, {"ruleId": null, "message": "200", "line": 187, "column": 3, "severity": 1, "nodeType": null, "fix": "212"}, {"ruleId": "202", "severity": 1, "message": "213", "line": 225, "column": 6, "nodeType": "204", "endLine": 225, "endColumn": 18, "suggestions": "214"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["215", "216"], ["217"], ["218", "219"], ["220"], "Unused eslint-disable directive (no problems were reported from 'react-hooks/exhaustive-deps').", {"range": "221", "text": "222"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'setSubjects'. Either include it or remove the dependency array.", "ArrayExpression", ["223"], {"range": "224", "text": "222"}, "React Hook useCallback has a missing dependency: 'setMaterials'. Either include it or remove the dependency array.", ["225"], {"range": "226", "text": "222"}, "React Hook useCallback has a missing dependency: 'setAnnouncements'. Either include it or remove the dependency array.", ["227"], {"range": "228", "text": "222"}, "React Hook useCallback has missing dependencies: 'setAnalytics' and 'user'. Either include them or remove the dependency array.", ["229"], {"messageId": "230", "fix": "231", "desc": "232"}, {"messageId": "233", "fix": "234", "desc": "235"}, {"kind": "236", "justification": "237"}, {"messageId": "230", "fix": "238", "desc": "232"}, {"messageId": "233", "fix": "239", "desc": "235"}, {"kind": "236", "justification": "237"}, [4072, 4127], " ", {"desc": "240", "fix": "241"}, [4686, 4741], {"desc": "242", "fix": "243"}, [5346, 5401], {"desc": "244", "fix": "245"}, [5987, 6042], {"desc": "246", "fix": "247"}, "suggestUnknown", {"range": "248", "text": "249"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "250", "text": "251"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", {"range": "252", "text": "249"}, {"range": "253", "text": "251"}, "Update the dependencies array to be: [setSubjects]", {"range": "254", "text": "255"}, "Update the dependencies array to be: [setMaterials]", {"range": "256", "text": "257"}, "Update the dependencies array to be: [setAnnouncements]", {"range": "258", "text": "259"}, "Update the dependencies array to be: [setAnalytics, user]", {"range": "260", "text": "261"}, [2344, 2347], "unknown", [2344, 2347], "never", [2720, 2723], [2720, 2723], [4606, 4608], "[setSubjects]", [5265, 5267], "[setMaterials]", [5902, 5904], "[setAnnouncements]", [7303, 7315], "[setAnalytics, user]"]