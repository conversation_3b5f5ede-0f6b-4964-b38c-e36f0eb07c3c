[{"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(auth)\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(auth)\\sign-in\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\admin\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\hod\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\chat\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\avatar\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\layout.tsx": "11", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\Blackboard.tsx": "13", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\Experience.tsx": "14", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\TeacherModel.tsx": "15", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\AuthProvider.tsx": "16", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\RouteGuard.tsx": "17", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\chat\\MessageList.tsx": "18", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\chat\\TypingBox.tsx": "19", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\alert.tsx": "20", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\badge.tsx": "21", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\button.tsx": "22", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\card.tsx": "23", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\input.tsx": "24", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\label.tsx": "25", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\select.tsx": "26", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\slider.tsx": "27", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\tabs.tsx": "28", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\textarea.tsx": "29", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\lib\\api.ts": "30", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\lib\\utils.ts": "31", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useAITeacher.ts": "32", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useChat.ts": "33", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useUser.ts": "34", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\types\\index.ts": "35", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\types\\next-auth.d.ts": "36", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\materials\\page.tsx": "37", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\practice\\page.tsx": "38", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\materials\\upload\\page.tsx": "39", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\subjects\\create\\page.tsx": "40", "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\progress.tsx": "41"}, {"size": 255, "mtime": 1755185507434, "results": "42", "hashOfConfig": "43"}, {"size": 4692, "mtime": 1755409245261, "results": "44", "hashOfConfig": "43"}, {"size": 14646, "mtime": 1755409188974, "results": "45", "hashOfConfig": "43"}, {"size": 12461, "mtime": 1755187232244, "results": "46", "hashOfConfig": "43"}, {"size": 4534, "mtime": 1755185553450, "results": "47", "hashOfConfig": "43"}, {"size": 9186, "mtime": 1755437895590, "results": "48", "hashOfConfig": "43"}, {"size": 11247, "mtime": 1755437956652, "results": "49", "hashOfConfig": "43"}, {"size": 15317, "mtime": 1755438078392, "results": "50", "hashOfConfig": "43"}, {"size": 11836, "mtime": 1755438105459, "results": "51", "hashOfConfig": "43"}, {"size": 3166, "mtime": 1755187428583, "results": "52", "hashOfConfig": "43"}, {"size": 844, "mtime": 1755439047865, "results": "53", "hashOfConfig": "43"}, {"size": 1638, "mtime": 1755186440719, "results": "54", "hashOfConfig": "43"}, {"size": 6592, "mtime": 1755438186546, "results": "55", "hashOfConfig": "43"}, {"size": 5188, "mtime": 1755438213625, "results": "56", "hashOfConfig": "43"}, {"size": 7530, "mtime": 1755186121324, "results": "57", "hashOfConfig": "43"}, {"size": 297, "mtime": 1755185514199, "results": "58", "hashOfConfig": "43"}, {"size": 1861, "mtime": 1755185527629, "results": "59", "hashOfConfig": "43"}, {"size": 9229, "mtime": 1755438270397, "results": "60", "hashOfConfig": "43"}, {"size": 8373, "mtime": 1755187710692, "results": "61", "hashOfConfig": "43"}, {"size": 1584, "mtime": 1755186355864, "results": "62", "hashOfConfig": "43"}, {"size": 1127, "mtime": 1755435112205, "results": "63", "hashOfConfig": "43"}, {"size": 1835, "mtime": 1755186284610, "results": "64", "hashOfConfig": "43"}, {"size": 1849, "mtime": 1755186301693, "results": "65", "hashOfConfig": "43"}, {"size": 771, "mtime": 1755438321854, "results": "66", "hashOfConfig": "43"}, {"size": 710, "mtime": 1755186322551, "results": "67", "hashOfConfig": "43"}, {"size": 5615, "mtime": 1755186382032, "results": "68", "hashOfConfig": "43"}, {"size": 1077, "mtime": 1755186457868, "results": "69", "hashOfConfig": "43"}, {"size": 1883, "mtime": 1755186472035, "results": "70", "hashOfConfig": "43"}, {"size": 713, "mtime": 1755438346565, "results": "71", "hashOfConfig": "43"}, {"size": 9460, "mtime": 1755438418457, "results": "72", "hashOfConfig": "43"}, {"size": 6236, "mtime": 1755438442557, "results": "73", "hashOfConfig": "43"}, {"size": 5691, "mtime": 1755409117555, "results": "74", "hashOfConfig": "43"}, {"size": 7409, "mtime": 1755409362484, "results": "75", "hashOfConfig": "43"}, {"size": 9445, "mtime": 1755439183493, "results": "76", "hashOfConfig": "43"}, {"size": 4876, "mtime": 1755408908733, "results": "77", "hashOfConfig": "43"}, {"size": 545, "mtime": 1755187583515, "results": "78", "hashOfConfig": "43"}, {"size": 10167, "mtime": 1755439698469, "results": "79", "hashOfConfig": "43"}, {"size": 11513, "mtime": 1755439637600, "results": "80", "hashOfConfig": "43"}, {"size": 9012, "mtime": 1755439749059, "results": "81", "hashOfConfig": "43"}, {"size": 5908, "mtime": 1755439305176, "results": "82", "hashOfConfig": "43"}, {"size": 791, "mtime": 1755439504054, "results": "83", "hashOfConfig": "43"}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k6xgq6", {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(auth)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(auth)\\sign-in\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\hod\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\chat\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\avatar\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], ["207", "208"], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\Blackboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\Experience.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\3d\\TeacherModel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\AuthProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\auth\\RouteGuard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\chat\\MessageList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\chat\\TypingBox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useAITeacher.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useChat.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\store\\useUser.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\types\\next-auth.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\materials\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\practice\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\materials\\upload\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\teacher\\subjects\\create\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\components\\ui\\progress.tsx", [], [], {"ruleId": "209", "severity": 2, "message": "210", "line": 94, "column": 32, "nodeType": "211", "messageId": "212", "endLine": 94, "endColumn": 35, "suggestions": "213", "suppressions": "214"}, {"ruleId": "209", "severity": 2, "message": "210", "line": 104, "column": 39, "nodeType": "211", "messageId": "212", "endLine": 104, "endColumn": 42, "suggestions": "215", "suppressions": "216"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["217", "218"], ["219"], ["220", "221"], ["222"], {"messageId": "223", "fix": "224", "desc": "225"}, {"messageId": "226", "fix": "227", "desc": "228"}, {"kind": "229", "justification": "230"}, {"messageId": "223", "fix": "231", "desc": "225"}, {"messageId": "226", "fix": "232", "desc": "228"}, {"kind": "229", "justification": "230"}, "suggestUnknown", {"range": "233", "text": "234"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "235", "text": "236"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", {"range": "237", "text": "234"}, {"range": "238", "text": "236"}, [2344, 2347], "unknown", [2344, 2347], "never", [2720, 2723], [2720, 2723]]