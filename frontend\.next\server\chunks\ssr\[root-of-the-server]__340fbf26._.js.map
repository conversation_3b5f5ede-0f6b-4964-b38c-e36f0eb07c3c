{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/lib/utils.ts"], "sourcesContent": ["// Utility functions for the AI Tutor Platform\n\nimport { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n// Utility function to merge Tailwind CSS classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Format date to readable string\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date);\n}\n\n// Format time to readable string\nexport function formatTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\n// Format relative time (e.g., \"2 hours ago\")\nexport function formatRelativeTime(date: Date): string {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n  \n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n  \n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n  \n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n  \n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n  \n  return formatDate(date);\n}\n\n// Truncate text to specified length\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n// Generate random ID\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n// Validate email format\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// Debounce function\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle function\nexport function throttle<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n// Deep clone object\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj;\n  if (obj instanceof Date) return new Date(obj.getTime()) as T;\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T;\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T;\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key]);\n      }\n    }\n    return clonedObj;\n  }\n  return obj;\n}\n\n// Check if object is empty\nexport function isEmpty(obj: unknown): boolean {\n  if (obj == null) return true;\n  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;\n  if (typeof obj === 'object') return Object.keys(obj).length === 0;\n  return false;\n}\n\n// Capitalize first letter\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n// Convert camelCase to Title Case\nexport function camelToTitle(str: string): string {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n}\n\n// Sleep function for async operations\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n// Local storage helpers with error handling\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : defaultValue || null;\n    } catch (error) {\n      console.error(`Error reading from localStorage:`, error);\n      return defaultValue || null;\n    }\n  },\n  \n  set: (key: string, value: unknown): boolean => {\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n      return true;\n    } catch (error) {\n      console.error(`Error writing to localStorage:`, error);\n      return false;\n    }\n  },\n  \n  remove: (key: string): boolean => {\n    try {\n      localStorage.removeItem(key);\n      return true;\n    } catch (error) {\n      console.error(`Error removing from localStorage:`, error);\n      return false;\n    }\n  },\n  \n  clear: (): boolean => {\n    try {\n      localStorage.clear();\n      return true;\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error);\n      return false;\n    }\n  }\n};\n\n// URL helpers\nexport const url = {\n  addParams: (baseUrl: string, params: Record<string, string>): string => {\n    const url = new URL(baseUrl, window.location.origin);\n    Object.entries(params).forEach(([key, value]) => {\n      url.searchParams.set(key, value);\n    });\n    return url.toString();\n  },\n  \n  removeParams: (baseUrl: string, paramsToRemove: string[]): string => {\n    const url = new URL(baseUrl, window.location.origin);\n    paramsToRemove.forEach(param => {\n      url.searchParams.delete(param);\n    });\n    return url.toString();\n  }\n};\n\n// Color helpers\nexport const color = {\n  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result ? {\n      r: parseInt(result[1], 16),\n      g: parseInt(result[2], 16),\n      b: parseInt(result[3], 16)\n    } : null;\n  },\n  \n  rgbToHex: (r: number, g: number, b: number): string => {\n    return \"#\" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);\n  }\n};\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;;;;;;;;;;;;;;;;AAE9C;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8LAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,mBAAmB,IAAU;IAC3C,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,eAAe,OAAO,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IAC3D,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,QAAQ,GAAY;IAClC,IAAI,OAAO,MAAM,OAAO;IACxB,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,QAAQ,UAAU,OAAO,IAAI,MAAM,KAAK;IACzE,IAAI,OAAO,QAAQ,UAAU,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;IAChE,OAAO;AACT;AAGO,SAAS,WAAW,GAAW;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,gBAAgB;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;YAClD,OAAO,gBAAgB;QACzB;IACF;IAEA,KAAK,CAAC,KAAa;QACjB,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE;YAChD,OAAO;QACT;IACF;IAEA,QAAQ,CAAC;QACP,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;YACnD,OAAO;QACT;IACF;IAEA,OAAO;QACL,IAAI;YACF,aAAa,KAAK;YAClB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;YAC9C,OAAO;QACT;IACF;AACF;AAGO,MAAM,MAAM;IACjB,WAAW,CAAC,SAAiB;QAC3B,MAAM,MAAM,IAAI,IAAI,SAAS,OAAO,QAAQ,CAAC,MAAM;QACnD,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK;QAC5B;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,cAAc,CAAC,SAAiB;QAC9B,MAAM,MAAM,IAAI,IAAI,SAAS,OAAO,QAAQ,CAAC,MAAM;QACnD,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,YAAY,CAAC,MAAM,CAAC;QAC1B;QACA,OAAO,IAAI,QAAQ;IACrB;AACF;AAGO,MAAM,QAAQ;IACnB,UAAU,CAAC;QACT,MAAM,SAAS,4CAA4C,IAAI,CAAC;QAChE,OAAO,SAAS;YACd,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACzB,IAAI;IACN;IAEA,UAAU,CAAC,GAAW,GAAW;QAC/B,OAAO,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;IACzE;AACF", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mMAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,wOAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mMAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,iRAAC;QACC,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,wOAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,iRAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mMAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,wOAAA,CAAA,aAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC,oMAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,oMAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,wOAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,wOAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,wOAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,wOAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,wOAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,wOAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mMAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,wOAAA,CAAA,aAAgB,CAG5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,iRAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,wOAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,wOAAA,CAAA,aAAgB,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/%28auth%29/sign-in/page.tsx"], "sourcesContent": ["'use client';\n\n// Sign-in page for the AI Tutor Platform\n\nimport { useState } from 'react';\nimport { signIn, getSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Loader2, GraduationCap } from 'lucide-react';\n\nexport default function SignInPage() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const router = useRouter();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const result = await signIn('credentials', {\n        email,\n        password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('Invalid email or password');\n      } else {\n        // Get the session to determine user role and redirect accordingly\n        const session = await getSession();\n        if (session?.user?.role) {\n          const role = session.user.role;\n          switch (role) {\n            case 'student':\n              router.push('/student');\n              break;\n            case 'teacher':\n              router.push('/teacher');\n              break;\n            case 'hod':\n              router.push('/hod');\n              break;\n            case 'admin':\n              router.push('/admin');\n              break;\n            default:\n              router.push('/');\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Sign-in error:', error);\n      setError('An error occurred. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <div className=\"p-3 bg-blue-600 rounded-full\">\n              <GraduationCap className=\"h-8 w-8 text-white\" />\n            </div>\n          </div>\n          <CardTitle className=\"text-2xl font-bold\">AI Tutor Platform</CardTitle>\n          <CardDescription>\n            Sign in to access your personalized learning experience\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                placeholder=\"Enter your password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                disabled={isLoading}\n              />\n            </div>\n            \n            {error && (\n              <Alert variant=\"destructive\">\n                <AlertDescription>{error}</AlertDescription>\n              </Alert>\n            )}\n\n            <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Signing in...\n                </>\n              ) : (\n                'Sign In'\n              )}\n            </Button>\n          </form>\n\n          <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm font-medium text-gray-700 mb-2\">Demo Accounts:</p>\n            <div className=\"space-y-1 text-xs text-gray-600\">\n              <p><strong>Student:</strong> <EMAIL></p>\n              <p><strong>Teacher:</strong> <EMAIL></p>\n              <p><strong>HOD:</strong> <EMAIL></p>\n              <p><strong>Admin:</strong> <EMAIL></p>\n              <p className=\"mt-2\"><strong>Password:</strong> password</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,yCAAyC;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAZA;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,wOAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,wOAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,wOAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,wOAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iLAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC;gBACA;gBACA,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;YACX,OAAO;gBACL,kEAAkE;gBAClE,MAAM,UAAU,MAAM,CAAA,GAAA,iLAAA,CAAA,aAAU,AAAD;gBAC/B,IAAI,SAAS,MAAM,MAAM;oBACvB,MAAM,OAAO,QAAQ,IAAI,CAAC,IAAI;oBAC9B,OAAQ;wBACN,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF;4BACE,OAAO,IAAI,CAAC;oBAChB;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,iRAAC;QAAI,WAAU;kBACb,cAAA,iRAAC,mKAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,iRAAC,mKAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,iRAAC;4BAAI,WAAU;sCACb,cAAA,iRAAC;gCAAI,WAAU;0CACb,cAAA,iRAAC,2PAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG7B,iRAAC,mKAAA,CAAA,YAAS;4BAAC,WAAU;sCAAqB;;;;;;sCAC1C,iRAAC,mKAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,iRAAC,mKAAA,CAAA,cAAW;;sCACV,iRAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,iRAAC;oCAAI,WAAU;;sDACb,iRAAC,oKAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,iRAAC,oKAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,QAAQ;4CACR,UAAU;;;;;;;;;;;;8CAGd,iRAAC;oCAAI,WAAU;;sDACb,iRAAC,oKAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,iRAAC,oKAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,QAAQ;4CACR,UAAU;;;;;;;;;;;;gCAIb,uBACC,iRAAC,oKAAA,CAAA,QAAK;oCAAC,SAAQ;8CACb,cAAA,iRAAC,oKAAA,CAAA,mBAAgB;kDAAE;;;;;;;;;;;8CAIvB,iRAAC,qKAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,WAAU;oCAAS,UAAU;8CAChD,0BACC;;0DACE,iRAAC,oPAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;sCAKN,iRAAC;4BAAI,WAAU;;8CACb,iRAAC;oCAAE,WAAU;8CAAyC;;;;;;8CACtD,iRAAC;oCAAI,WAAU;;sDACb,iRAAC;;8DAAE,iRAAC;8DAAO;;;;;;gDAAiB;;;;;;;sDAC5B,iRAAC;;8DAAE,iRAAC;8DAAO;;;;;;gDAAiB;;;;;;;sDAC5B,iRAAC;;8DAAE,iRAAC;8DAAO;;;;;;gDAAa;;;;;;;sDACxB,iRAAC;;8DAAE,iRAAC;8DAAO;;;;;;gDAAe;;;;;;;sDAC1B,iRAAC;4CAAE,WAAU;;8DAAO,iRAAC;8DAAO;;;;;;gDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D", "debugId": null}}]}