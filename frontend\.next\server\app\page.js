const CHUNK_PUBLIC_PATH = "server/app/page.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/27da8_6d049e75._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__d8593af1._.js");
runtime.loadChunk("server/chunks/ssr/Desktop_AI_ai_frontend_src_app_db014353._.js");
runtime.loadChunk("server/chunks/ssr/Desktop_AI_ai_frontend_src_app_96ba0d83._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__849ab5ea._.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_client_components_cd516cf2._.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_client_components_builtin_forbidden_c184db15.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_client_components_builtin_unauthorized_85d6e933.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_client_components_builtin_global-error_d2ce9306.js");
runtime.loadChunk("server/chunks/ssr/27da8_next_dist_fe6a352d._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__b96e193a._.js");
runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Desktop/AI/ai/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Desktop/AI/ai/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", METADATA_1 => \"[project]/Desktop/AI/ai/frontend/src/app/icon.svg.mjs { IMAGE => \\\"[project]/Desktop/AI/ai/frontend/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Desktop/AI/ai/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_7 => \"[project]/Desktop/AI/ai/frontend/src/app/icon.svg.mjs { IMAGE => \\\"[project]/Desktop/AI/ai/frontend/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/Desktop/AI/ai/frontend/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Desktop/AI/ai/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Desktop/AI/ai/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", METADATA_1 => \"[project]/Desktop/AI/ai/frontend/src/app/icon.svg.mjs { IMAGE => \\\"[project]/Desktop/AI/ai/frontend/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Desktop/AI/ai/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Desktop/AI/ai/frontend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_7 => \"[project]/Desktop/AI/ai/frontend/src/app/icon.svg.mjs { IMAGE => \\\"[project]/Desktop/AI/ai/frontend/src/app/icon.svg (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/Desktop/AI/ai/frontend/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
