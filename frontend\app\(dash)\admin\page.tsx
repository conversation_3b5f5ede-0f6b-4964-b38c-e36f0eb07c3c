import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  Building2, 
  GraduationCap,
  TrendingUp,
  Server,
  Shield,
  Settings,
  BarChart3,
  UserPlus,
  Database,
  Globe,
  Activity
} from 'lucide-react';
import Link from 'next/link';

// Mock data - would be fetched from API in production
const mockAdminData = {
  user: {
    name: 'Administrator',
    role: 'Super Admin'
  },
  globalStats: {
    totalOrganizations: 25,
    totalUsers: 2840,
    totalTeachers: 340,
    totalStudents: 2500,
    totalSubjects: 180,
    activeChats: 450,
    systemUptime: 99.9,
    storageUsed: 78
  },
  organizations: [
    {
      id: '1',
      name: 'Tokyo International School',
      type: 'University',
      users: 450,
      teachers: 45,
      students: 405,
      status: 'active',
      plan: 'Enterprise',
      lastActive: '2 minutes ago'
    },
    {
      id: '2',
      name: 'Global Language Institute',
      type: 'Language School',
      users: 280,
      teachers: 28,
      students: 252,
      status: 'active',
      plan: 'Professional',
      lastActive: '15 minutes ago'
    },
    {
      id: '3',
      name: 'Digital Learning Academy',
      type: 'Online School',
      users: 650,
      teachers: 65,
      students: 585,
      status: 'active',
      plan: 'Enterprise',
      lastActive: '5 minutes ago'
    }
  ],
  systemHealth: {
    apiResponseTime: '120ms',
    databaseConnections: 45,
    activeUsers: 1250,
    errorRate: 0.02,
    cpuUsage: 35,
    memoryUsage: 68,
    diskUsage: 45
  },
  recentActivity: [
    {
      id: '1',
      type: 'user_created',
      message: 'New teacher registered at Tokyo International School',
      timestamp: '5 minutes ago',
      severity: 'info'
    },
    {
      id: '2',
      type: 'organization_created',
      message: 'New organization "Berlin Language Center" created',
      timestamp: '2 hours ago',
      severity: 'success'
    },
    {
      id: '3',
      type: 'system_alert',
      message: 'High API usage detected - scaling initiated',
      timestamp: '4 hours ago',
      severity: 'warning'
    }
  ],
  analytics: {
    userGrowth: 23,
    organizationGrowth: 15,
    revenueGrowth: 31,
    supportTickets: 12
  }
};

export default function AdminDashboard() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            System Administration 🛡️
          </h1>
          <p className="text-gray-600 mt-1">
            Global platform management and monitoring
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Badge variant="secondary" className="px-3 py-1">
            <Activity className="w-4 h-4 mr-1" />
            {mockAdminData.systemHealth.activeUsers} active users
          </Badge>
          <Badge variant={mockAdminData.globalStats.systemUptime > 99 ? 'default' : 'destructive'} className="px-3 py-1">
            <Server className="w-4 h-4 mr-1" />
            {mockAdminData.globalStats.systemUptime}% uptime
          </Badge>
        </div>
      </div>

      {/* Global Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Building2 className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Organizations</p>
                <p className="text-xl font-bold">{mockAdminData.globalStats.totalOrganizations}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Users</p>
                <p className="text-xl font-bold">{mockAdminData.globalStats.totalUsers.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <GraduationCap className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Teachers</p>
                <p className="text-xl font-bold">{mockAdminData.globalStats.totalTeachers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Database className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Storage Used</p>
                <p className="text-xl font-bold">{mockAdminData.globalStats.storageUsed}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Organizations Management */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Building2 className="w-5 h-5" />
                  <span>Organizations</span>
                </div>
                <Button size="sm" asChild>
                  <Link href="/admin/organizations/create">
                    Add Organization
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Manage all organizations on the platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockAdminData.organizations.map((org) => (
                <div key={org.id} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-lg">{org.name}</h3>
                      <p className="text-sm text-gray-600">{org.type}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={org.status === 'active' ? 'default' : 'secondary'}>
                        {org.status}
                      </Badge>
                      <Badge variant="outline">
                        {org.plan}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Total Users</p>
                      <p className="font-semibold">{org.users}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Teachers</p>
                      <p className="font-semibold">{org.teachers}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Students</p>
                      <p className="font-semibold">{org.students}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Last Active</p>
                      <p className="font-semibold">{org.lastActive}</p>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 mt-3">
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/admin/organizations/${org.id}`}>
                        <BarChart3 className="w-4 h-4 mr-1" />
                        Analytics
                      </Link>
                    </Button>
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/admin/organizations/${org.id}/users`}>
                        <Users className="w-4 h-4 mr-1" />
                        Users
                      </Link>
                    </Button>
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/admin/organizations/${org.id}/settings`}>
                        <Settings className="w-4 h-4 mr-1" />
                        Settings
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* System Health */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="w-5 h-5" />
                <span>System Health</span>
              </CardTitle>
              <CardDescription>
                Real-time system performance monitoring
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">CPU Usage</span>
                    <span className="font-semibold">{mockAdminData.systemHealth.cpuUsage}%</span>
                  </div>
                  <Progress value={mockAdminData.systemHealth.cpuUsage} className="h-2" />
                </div>
                
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Memory Usage</span>
                    <span className="font-semibold">{mockAdminData.systemHealth.memoryUsage}%</span>
                  </div>
                  <Progress value={mockAdminData.systemHealth.memoryUsage} className="h-2" />
                </div>
                
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Disk Usage</span>
                    <span className="font-semibold">{mockAdminData.systemHealth.diskUsage}%</span>
                  </div>
                  <Progress value={mockAdminData.systemHealth.diskUsage} className="h-2" />
                </div>
                
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Error Rate</span>
                    <span className="font-semibold">{mockAdminData.systemHealth.errorRate}%</span>
                  </div>
                  <Progress value={mockAdminData.systemHealth.errorRate} className="h-2" />
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <p className="text-gray-600">API Response</p>
                  <p className="font-semibold">{mockAdminData.systemHealth.apiResponseTime}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600">DB Connections</p>
                  <p className="font-semibold">{mockAdminData.systemHealth.databaseConnections}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600">Active Users</p>
                  <p className="font-semibold">{mockAdminData.systemHealth.activeUsers.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Admin Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" asChild>
                <Link href="/admin/organizations/create">
                  <Building2 className="w-4 h-4 mr-2" />
                  Add Organization
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/admin/users">
                  <Users className="w-4 h-4 mr-2" />
                  Manage Users
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/admin/analytics">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Global Analytics
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/admin/system">
                  <Server className="w-4 h-4 mr-2" />
                  System Settings
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/admin/security">
                  <Shield className="w-4 h-4 mr-2" />
                  Security Center
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {mockAdminData.recentActivity.map((activity) => (
                <div key={activity.id} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between mb-1">
                    <Badge 
                      variant={
                        activity.severity === 'success' ? 'default' :
                        activity.severity === 'warning' ? 'destructive' : 'secondary'
                      }
                      className="text-xs"
                    >
                      {activity.type.replace('_', ' ')}
                    </Badge>
                    <span className="text-xs text-gray-500">{activity.timestamp}</span>
                  </div>
                  <p className="text-sm text-gray-700">{activity.message}</p>
                </div>
              ))}
              
              <Button variant="outline" className="w-full" asChild>
                <Link href="/admin/activity">View All Activity</Link>
              </Button>
            </CardContent>
          </Card>

          {/* Growth Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Growth Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">User Growth</span>
                <span className="font-semibold text-green-600">+{mockAdminData.analytics.userGrowth}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Organizations</span>
                <span className="font-semibold text-green-600">+{mockAdminData.analytics.organizationGrowth}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Revenue</span>
                <span className="font-semibold text-green-600">+{mockAdminData.analytics.revenueGrowth}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Support Tickets</span>
                <span className="font-semibold">{mockAdminData.analytics.supportTickets}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
