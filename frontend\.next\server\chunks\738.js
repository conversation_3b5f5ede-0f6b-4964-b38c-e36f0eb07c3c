"use strict";exports.id=738,exports.ids=[738],exports.modules={43:(a,b,c)=>{c.d(b,{jH:()=>f});var d=c(43210);c(60687);var e=d.createContext(void 0);function f(a){let b=d.useContext(e);return a||b||"ltr"}},3589:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9510:(a,b,c)=>{c.d(b,{N:()=>i});var d=c(43210),e=c(11273),f=c(98599),g=c(8730),h=c(60687);function i(a){let b=a+"CollectionProvider",[c,i]=(0,e.A)(b),[j,k]=c(b,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:b,children:c}=a,e=d.useRef(null),f=d.useRef(new Map).current;return(0,h.jsx)(j,{scope:b,itemMap:f,collectionRef:e,children:c})};l.displayName=b;let m=a+"CollectionSlot",n=(0,g.TL)(m),o=d.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=k(m,c),g=(0,f.s)(b,e.collectionRef);return(0,h.jsx)(n,{ref:g,children:d})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,g.TL)(p),s=d.forwardRef((a,b)=>{let{scope:c,children:e,...g}=a,i=d.useRef(null),j=(0,f.s)(b,i),l=k(p,c);return d.useEffect(()=>(l.itemMap.set(i,{ref:i,...g}),()=>void l.itemMap.delete(i))),(0,h.jsx)(r,{...{[q]:""},ref:j,children:e})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(b){let c=k(a+"CollectionConsumer",b);return d.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},i]}var j=new WeakMap;function k(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=l(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function l(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],j.set(this,!0)}set(a,b){return j.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=l(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],m=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||k[a-1]!==b||(m=!0);let c=k[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=k(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=k(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return k(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}})},11273:(a,b,c)=>{c.d(b,{A:()=>f});var d=c(43210),e=c(60687);function f(a,b=[]){let c=[],g=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return g.scopeName=a,[function(b,f){let g=d.createContext(f),h=c.length;c=[...c,f];let i=b=>{let{scope:c,children:f,...i}=b,j=c?.[a]?.[h]||g,k=d.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=d.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(g,...b)]}},13495:(a,b,c)=>{c.d(b,{c:()=>e});var d=c(43210);function e(a){let b=d.useRef(a);return d.useEffect(()=>{b.current=a}),d.useMemo(()=>(...a)=>b.current?.(...a),[])}},13964:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(a,b,c)=>{c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},18224:(a,b,c)=>{c.d(b,{UC:()=>da,YJ:()=>dc,In:()=>c8,q7:()=>de,VF:()=>dg,p4:()=>df,JU:()=>dd,ZL:()=>c9,bL:()=>c5,wn:()=>di,PP:()=>dh,wv:()=>dj,l9:()=>c6,WT:()=>c7,LM:()=>db});var d,e,f,g=c(43210),h=c(51215),i=c(67969),j=c(70569),k=c(9510),l=c(98599),m=c(11273),n=c(43),o=c(14163),p=c(13495),q=c(60687),r="dismissableLayer.update",s=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),t=g.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:i,onDismiss:k,...m}=a,n=g.useContext(s),[t,w]=g.useState(null),x=t?.ownerDocument??globalThis?.document,[,y]=g.useState({}),z=(0,l.s)(b,a=>w(a)),A=Array.from(n.layers),[B]=[...n.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=t?A.indexOf(t):-1,E=n.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=(0,p.c)(a),d=g.useRef(!1),e=g.useRef(()=>{});return g.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){v("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...n.branches].some(a=>a.contains(b));F&&!c&&(f?.(a),i?.(a),a.defaultPrevented||k?.())},x),H=function(a,b=globalThis?.document){let c=(0,p.c)(a),d=g.useRef(!1);return g.useEffect(()=>{let a=a=>{a.target&&!d.current&&v("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...n.branches].some(a=>a.contains(b))&&(h?.(a),i?.(a),a.defaultPrevented||k?.())},x);return!function(a,b=globalThis?.document){let c=(0,p.c)(a);g.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===n.layers.size-1&&(d?.(a),!a.defaultPrevented&&k&&(a.preventDefault(),k()))},x),g.useEffect(()=>{if(t)return c&&(0===n.layersWithOutsidePointerEventsDisabled.size&&(e=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),n.layersWithOutsidePointerEventsDisabled.add(t)),n.layers.add(t),u(),()=>{c&&1===n.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=e)}},[t,x,c,n]),g.useEffect(()=>()=>{t&&(n.layers.delete(t),n.layersWithOutsidePointerEventsDisabled.delete(t),u())},[t,n]),g.useEffect(()=>{let a=()=>y({});return document.addEventListener(r,a),()=>document.removeEventListener(r,a)},[]),(0,q.jsx)(o.sG.div,{...m,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,j.mK)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,j.mK)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,j.mK)(a.onPointerDownCapture,G.onPointerDownCapture)})});function u(){let a=new CustomEvent(r);document.dispatchEvent(a)}function v(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,o.hO)(e,f):e.dispatchEvent(f)}t.displayName="DismissableLayer",g.forwardRef((a,b)=>{let c=g.useContext(s),d=g.useRef(null),e=(0,l.s)(b,d);return g.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,q.jsx)(o.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var w=0;function x(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var y="focusScope.autoFocusOnMount",z="focusScope.autoFocusOnUnmount",A={bubbles:!1,cancelable:!0},B=g.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...h}=a,[i,j]=g.useState(null),k=(0,p.c)(e),m=(0,p.c)(f),n=g.useRef(null),r=(0,l.s)(b,a=>j(a)),s=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(d){let a=function(a){if(s.paused||!i)return;let b=a.target;i.contains(b)?n.current=b:E(n.current,{select:!0})},b=function(a){if(s.paused||!i)return;let b=a.relatedTarget;null!==b&&(i.contains(b)||E(n.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&E(i)});return i&&c.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,i,s.paused]),g.useEffect(()=>{if(i){F.add(s);let a=document.activeElement;if(!i.contains(a)){let b=new CustomEvent(y,A);i.addEventListener(y,k),i.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(E(d,{select:b}),document.activeElement!==c)return}(C(i).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&E(i))}return()=>{i.removeEventListener(y,k),setTimeout(()=>{let b=new CustomEvent(z,A);i.addEventListener(z,m),i.dispatchEvent(b),b.defaultPrevented||E(a??document.body,{select:!0}),i.removeEventListener(z,m),F.remove(s)},0)}}},[i,k,m,s]);let t=g.useCallback(a=>{if(!c&&!d||s.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=C(a);return[D(b,a),D(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&E(f,{select:!0})):(a.preventDefault(),c&&E(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,s.paused]);return(0,q.jsx)(o.sG.div,{tabIndex:-1,...h,ref:r,onKeyDown:t})});function C(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function D(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function E(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}B.displayName="FocusScope";var F=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=G(a,b)).unshift(b)},remove(b){a=G(a,b),a[0]?.resume()}}}();function G(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var H=c(96963);let I=["top","right","bottom","left"],J=Math.min,K=Math.max,L=Math.round,M=Math.floor,N=a=>({x:a,y:a}),O={left:"right",right:"left",bottom:"top",top:"bottom"},P={start:"end",end:"start"};function Q(a,b){return"function"==typeof a?a(b):a}function R(a){return a.split("-")[0]}function S(a){return a.split("-")[1]}function T(a){return"x"===a?"y":"x"}function U(a){return"y"===a?"height":"width"}let V=new Set(["top","bottom"]);function W(a){return V.has(R(a))?"y":"x"}function X(a){return a.replace(/start|end/g,a=>P[a])}let Y=["left","right"],Z=["right","left"],$=["top","bottom"],_=["bottom","top"];function aa(a){return a.replace(/left|right|bottom|top/g,a=>O[a])}function ab(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function ac(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function ad(a,b,c){let d,{reference:e,floating:f}=a,g=W(b),h=T(W(b)),i=U(h),j=R(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(S(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let ae=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=ad(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=ad(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function af(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=Q(b,a),o=ab(n),p=h[m?"floating"===l?"reference":"floating":l],q=ac(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=ac(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function ag(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function ah(a){return I.some(b=>a[b]>=0)}let ai=new Set(["left","top"]);async function aj(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=R(c),h=S(c),i="y"===W(c),j=ai.has(g)?-1:1,k=f&&i?-1:1,l=Q(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function ak(){return"undefined"!=typeof window}function al(a){return ao(a)?(a.nodeName||"").toLowerCase():"#document"}function am(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function an(a){var b;return null==(b=(ao(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function ao(a){return!!ak()&&(a instanceof Node||a instanceof am(a).Node)}function ap(a){return!!ak()&&(a instanceof Element||a instanceof am(a).Element)}function aq(a){return!!ak()&&(a instanceof HTMLElement||a instanceof am(a).HTMLElement)}function ar(a){return!!ak()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof am(a).ShadowRoot)}let as=new Set(["inline","contents"]);function at(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aE(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!as.has(e)}let au=new Set(["table","td","th"]),av=[":popover-open",":modal"];function aw(a){return av.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let ax=["transform","translate","scale","rotate","perspective"],ay=["transform","translate","scale","rotate","perspective","filter"],az=["paint","layout","strict","content"];function aA(a){let b=aB(),c=ap(a)?aE(a):a;return ax.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||ay.some(a=>(c.willChange||"").includes(a))||az.some(a=>(c.contain||"").includes(a))}function aB(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aC=new Set(["html","body","#document"]);function aD(a){return aC.has(al(a))}function aE(a){return am(a).getComputedStyle(a)}function aF(a){return ap(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function aG(a){if("html"===al(a))return a;let b=a.assignedSlot||a.parentNode||ar(a)&&a.host||an(a);return ar(b)?b.host:b}function aH(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=aG(b);return aD(c)?b.ownerDocument?b.ownerDocument.body:b.body:aq(c)&&at(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=am(e);if(f){let a=aI(g);return b.concat(g,g.visualViewport||[],at(e)?e:[],a&&c?aH(a):[])}return b.concat(e,aH(e,[],c))}function aI(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aJ(a){let b=aE(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=aq(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=L(c)!==f||L(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function aK(a){return ap(a)?a:a.contextElement}function aL(a){let b=aK(a);if(!aq(b))return N(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aJ(b),g=(f?L(c.width):c.width)/d,h=(f?L(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let aM=N(0);function aN(a){let b=am(a);return aB()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:aM}function aO(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aK(a),h=N(1);b&&(d?ap(d)&&(h=aL(d)):h=aL(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===am(g))&&e)?aN(g):N(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=am(g),b=d&&ap(d)?am(d):d,c=a,e=aI(c);for(;e&&d&&b!==c;){let a=aL(e),b=e.getBoundingClientRect(),d=aE(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=aI(c=am(e))}}return ac({width:l,height:m,x:j,y:k})}function aP(a,b){let c=aF(a).scrollLeft;return b?b.left+c:aO(an(a)).left+c}function aQ(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:aP(a,d)),y:d.top+b.scrollTop}}let aR=new Set(["absolute","fixed"]);function aS(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=am(a),d=an(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=aB();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=an(a),c=aF(a),d=a.ownerDocument.body,e=K(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=K(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aP(a),h=-c.scrollTop;return"rtl"===aE(d).direction&&(g+=K(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(an(a));else if(ap(b))d=function(a,b){let c=aO(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=aq(a)?aL(a):N(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aN(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return ac(d)}function aT(a){return"static"===aE(a).position}function aU(a,b){if(!aq(a)||"fixed"===aE(a).position)return null;if(b)return b(a);let c=a.offsetParent;return an(a)===c&&(c=c.ownerDocument.body),c}function aV(a,b){var c;let d=am(a);if(aw(a))return d;if(!aq(a)){let b=aG(a);for(;b&&!aD(b);){if(ap(b)&&!aT(b))return b;b=aG(b)}return d}let e=aU(a,b);for(;e&&(c=e,au.has(al(c)))&&aT(e);)e=aU(e,b);return e&&aD(e)&&aT(e)&&!aA(e)?d:e||function(a){let b=aG(a);for(;aq(b)&&!aD(b);){if(aA(b))return b;if(aw(b))break;b=aG(b)}return null}(a)||d}let aW=async function(a){let b=this.getOffsetParent||aV,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=aq(b),e=an(b),f="fixed"===c,g=aO(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=N(0);if(d||!d&&!f)if(("body"!==al(b)||at(e))&&(h=aF(b)),d){let a=aO(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=aP(e));f&&!d&&e&&(i.x=aP(e));let j=!e||d||f?N(0):aQ(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},aX={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=an(d),h=!!b&&aw(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=N(1),k=N(0),l=aq(d);if((l||!l&&!f)&&(("body"!==al(d)||at(g))&&(i=aF(d)),aq(d))){let a=aO(d);j=aL(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?N(0):aQ(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:an,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?aw(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=aH(a,[],!1).filter(a=>ap(a)&&"body"!==al(a)),e=null,f="fixed"===aE(a).position,g=f?aG(a):a;for(;ap(g)&&!aD(g);){let b=aE(g),c=aA(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&aR.has(e.position)||at(g)&&!c&&function a(b,c){let d=aG(b);return!(d===c||!ap(d)||aD(d))&&("fixed"===aE(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=aG(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=aS(b,c,e);return a.top=K(d.top,a.top),a.right=J(d.right,a.right),a.bottom=J(d.bottom,a.bottom),a.left=K(d.left,a.left),a},aS(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:aV,getElementRects:aW,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aJ(a);return{width:b,height:c}},getScale:aL,isElement:ap,isRTL:function(a){return"rtl"===aE(a).direction}};function aY(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let aZ=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=Q(a,b)||{};if(null==j)return{};let l=ab(k),m={x:c,y:d},n=T(W(e)),o=U(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",s=f.reference[o]+f.reference[n]-m[n]-f.floating[o],t=m[n]-f.reference[n],u=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),v=u?u[r]:0;v&&await (null==g.isElement?void 0:g.isElement(u))||(v=h.floating[r]||f.floating[o]);let w=v/2-p[o]/2-1,x=J(l[q?"top":"left"],w),y=J(l[q?"bottom":"right"],w),z=v-p[o]-y,A=v/2-p[o]/2+(s/2-t/2),B=K(x,J(A,z)),C=!i.arrow&&null!=S(e)&&A!==B&&f.reference[o]/2-(A<x?x:y)-p[o]/2<0,D=C?A<x?A-x:A-z:0;return{[n]:m[n]+D,data:{[n]:B,centerOffset:A-B-D,...C&&{alignmentOffset:D}},reset:C}}});var a$="undefined"!=typeof document?g.useLayoutEffect:function(){};function a_(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!a_(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!a_(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function a0(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function a1(a,b){let c=a0(a);return Math.round(b*c)/c}function a2(a){let b=g.useRef(a);return a$(()=>{b.current=a}),b}var a3=g.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,q.jsx)(o.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,q.jsx)("polygon",{points:"0,0 30,0 15,10"})})});a3.displayName="Arrow";var a4=c(66156),a5=c(18853),a6="Popper",[a7,a8]=(0,m.A)(a6),[a9,ba]=a7(a6),bb=a=>{let{__scopePopper:b,children:c}=a,[d,e]=g.useState(null);return(0,q.jsx)(a9,{scope:b,anchor:d,onAnchorChange:e,children:c})};bb.displayName=a6;var bc="PopperAnchor",bd=g.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...e}=a,f=ba(bc,c),h=g.useRef(null),i=(0,l.s)(b,h),j=g.useRef(null);return g.useEffect(()=>{let a=j.current;j.current=d?.current||h.current,a!==j.current&&f.onAnchorChange(j.current)}),d?null:(0,q.jsx)(o.sG.div,{...e,ref:i})});bd.displayName=bc;var be="PopperContent",[bf,bg]=a7(be),bh=g.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:e=0,align:f="center",alignOffset:i=0,arrowPadding:j=0,avoidCollisions:k=!0,collisionBoundary:m=[],collisionPadding:n=0,sticky:r="partial",hideWhenDetached:s=!1,updatePositionStrategy:t="optimized",onPlaced:u,...v}=a,w=ba(be,c),[x,y]=g.useState(null),z=(0,l.s)(b,a=>y(a)),[A,B]=g.useState(null),C=(0,a5.X)(A),D=C?.width??0,E=C?.height??0,F="number"==typeof n?n:{top:0,right:0,bottom:0,left:0,...n},G=Array.isArray(m)?m:[m],H=G.length>0,I={padding:F,boundary:G.filter(bl),altBoundary:H},{refs:L,floatingStyles:N,placement:O,isPositioned:P,middlewareData:V}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:d=[],platform:e,elements:{reference:f,floating:i}={},transform:j=!0,whileElementsMounted:k,open:l}=a,[m,n]=g.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[o,p]=g.useState(d);a_(o,d)||p(d);let[q,r]=g.useState(null),[s,t]=g.useState(null),u=g.useCallback(a=>{a!==y.current&&(y.current=a,r(a))},[]),v=g.useCallback(a=>{a!==z.current&&(z.current=a,t(a))},[]),w=f||q,x=i||s,y=g.useRef(null),z=g.useRef(null),A=g.useRef(m),B=null!=k,C=a2(k),D=a2(e),E=a2(l),F=g.useCallback(()=>{if(!y.current||!z.current)return;let a={placement:b,strategy:c,middleware:o};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:aX,...c},f={...e.platform,_c:d};return ae(a,b,{...e,platform:f})})(y.current,z.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!a_(A.current,b)&&(A.current=b,h.flushSync(()=>{n(b)}))})},[o,b,c,D,E]);a$(()=>{!1===l&&A.current.isPositioned&&(A.current.isPositioned=!1,n(a=>({...a,isPositioned:!1})))},[l]);let G=g.useRef(!1);a$(()=>(G.current=!0,()=>{G.current=!1}),[]),a$(()=>{if(w&&(y.current=w),x&&(z.current=x),w&&x){if(C.current)return C.current(w,x,F);F()}},[w,x,F,C,B]);let H=g.useMemo(()=>({reference:y,floating:z,setReference:u,setFloating:v}),[u,v]),I=g.useMemo(()=>({reference:w,floating:x}),[w,x]),J=g.useMemo(()=>{let a={position:c,left:0,top:0};if(!I.floating)return a;let b=a1(I.floating,m.x),d=a1(I.floating,m.y);return j?{...a,transform:"translate("+b+"px, "+d+"px)",...a0(I.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,j,I.floating,m.x,m.y]);return g.useMemo(()=>({...m,update:F,refs:H,elements:I,floatingStyles:J}),[m,F,H,I,J])}({strategy:"fixed",placement:d+("center"!==f?"-"+f:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=aK(a),l=f||g?[...k?aH(k):[],...aH(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=an(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=M(l),p=M(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-M(e.clientHeight-(l+n))+"px "+-M(k)+"px",threshold:K(0,J(1,i))||1},r=!0;function s(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||aY(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(s,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(s,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?aO(a):null;return j&&function b(){let d=aO(a);p&&!aY(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===t}),elements:{reference:w.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await aj(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:e+E,alignmentAxis:i}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=Q(a,b),j={x:c,y:d},k=await af(b,i),l=W(R(e)),m=T(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=K(c,J(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=K(c,J(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===r?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=Q(a,b),k={x:c,y:d},l=W(e),m=T(l),n=k[m],o=k[l],p=Q(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=ai.has(R(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...I}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=Q(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=R(h),v=W(k),w=R(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[aa(k)]:function(a){let b=aa(a);return[X(a),b,X(b)]}(k)),z="none"!==r;!p&&z&&y.push(...function(a,b,c,d){let e=S(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?Z:Y;return b?Y:Z;case"left":case"right":return b?$:_;default:return[]}}(R(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(X)))),f}(k,s,r,x));let A=[k,...y],B=await af(b,t),C=[],D=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&C.push(B[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=S(a),e=T(W(a)),f=U(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=aa(g)),[g,aa(g)]}(h,j,x);C.push(B[a[0]],B[a[1]])}if(D=[...D,{placement:h,overflows:C}],!C.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=A[a];if(b&&("alignment"!==o||v===W(b)||D.every(a=>W(a.placement)!==v||a.overflows[0]>0)))return{data:{index:a,overflows:D},reset:{placement:b}};let c=null==(f=D.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=D.filter(a=>{if(z){let b=W(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...I}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=Q(a,b),m=await af(b,l),n=R(g),o=S(g),p="y"===W(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let s=r-m.top-m.bottom,t=q-m.left-m.right,u=J(r-m[e],s),v=J(q-m[f],t),w=!b.middlewareData.shift,x=u,y=v;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(y=t),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(x=s),w&&!o){let a=K(m.left,0),b=K(m.right,0),c=K(m.top,0),d=K(m.bottom,0);p?y=q-2*(0!==a||0!==b?a+b:K(m.left,m.right)):x=r-2*(0!==c||0!==d?c+d:K(m.top,m.bottom))}await k({...b,availableWidth:y,availableHeight:x});let z=await i.getDimensions(j.floating);return q!==z.width||r!==z.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...I,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),A&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?aZ({element:c.current,padding:d}).fn(b):{}:c?aZ({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:A,padding:j}),bm({arrowWidth:D,arrowHeight:E}),s&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=Q(a,b);switch(d){case"referenceHidden":{let a=ag(await af(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:ah(a)}}}case"escaped":{let a=ag(await af(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:ah(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...I})]}),[ab,ac]=bn(O),ad=(0,p.c)(u);(0,a4.N)(()=>{P&&ad?.()},[P,ad]);let ak=V.arrow?.x,al=V.arrow?.y,am=V.arrow?.centerOffset!==0,[ao,ap]=g.useState();return(0,a4.N)(()=>{x&&ap(window.getComputedStyle(x).zIndex)},[x]),(0,q.jsx)("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...N,transform:P?N.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ao,"--radix-popper-transform-origin":[V.transformOrigin?.x,V.transformOrigin?.y].join(" "),...V.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,q.jsx)(bf,{scope:c,placedSide:ab,onArrowChange:B,arrowX:ak,arrowY:al,shouldHideArrow:am,children:(0,q.jsx)(o.sG.div,{"data-side":ab,"data-align":ac,...v,ref:z,style:{...v.style,animation:P?void 0:"none"}})})})});bh.displayName=be;var bi="PopperArrow",bj={top:"bottom",right:"left",bottom:"top",left:"right"},bk=g.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=bg(bi,c),f=bj[e.placedSide];return(0,q.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,q.jsx)(a3,{...d,ref:b,style:{...d.style,display:"block"}})})});function bl(a){return null!==a}bk.displayName=bi;var bm=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=bn(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function bn(a){let[b,c="center"]=a.split("-");return[b,c]}var bo=g.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=g.useState(!1);(0,a4.N)(()=>f(!0),[]);let i=c||e&&globalThis?.document?.body;return i?h.createPortal((0,q.jsx)(o.sG.div,{...d,ref:b}),i):null});bo.displayName="Portal";var bp=c(8730),bq=c(65551),br=c(83721),bs=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});g.forwardRef((a,b)=>(0,q.jsx)(o.sG.span,{...a,ref:b,style:{...bs,...a.style}})).displayName="VisuallyHidden";var bt=new WeakMap,bu=new WeakMap,bv={},bw=0,bx=function(a){return a&&(a.host||bx(a.parentNode))},by=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=bx(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});bv[c]||(bv[c]=new WeakMap);var f=bv[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(bt.get(a)||0)+1,j=(f.get(a)||0)+1;bt.set(a,i),f.set(a,j),g.push(a),1===i&&e&&bu.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),bw++,function(){g.forEach(function(a){var b=bt.get(a)-1,e=f.get(a)-1;bt.set(a,b),f.set(a,e),b||(bu.has(a)||a.removeAttribute(d),bu.delete(a)),e||a.removeAttribute(c)}),--bw||(bt=new WeakMap,bt=new WeakMap,bu=new WeakMap,bv={})}},bz=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),by(d,e,c,"aria-hidden")):function(){return null}},bA=function(){return(bA=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function bB(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var bC=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),bD="width-before-scroll-bar";function bE(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var bF="undefined"!=typeof window?g.useLayoutEffect:g.useEffect,bG=new WeakMap;function bH(a){return a}var bI=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=bH),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=bA({async:!0,ssr:!1},a),e}(),bJ=function(){},bK=g.forwardRef(function(a,b){var c,d,e,f,h=g.useRef(null),i=g.useState({onScrollCapture:bJ,onWheelCapture:bJ,onTouchMoveCapture:bJ}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=bB(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[h,b],d=function(a){return c.forEach(function(b){return bE(b,a)})},(e=(0,g.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,bF(function(){var a=bG.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||bE(a,null)}),d.forEach(function(a){b.has(a)||bE(a,e)})}bG.set(f,c)},[c]),f),A=bA(bA({},y),j);return g.createElement(g.Fragment,null,p&&g.createElement(r,{sideCar:bI,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:h,gapMode:x}),l?g.cloneElement(g.Children.only(m),bA(bA({},A),{ref:z})):g.createElement(void 0===w?"div":w,bA({},A,{className:n,ref:z}),m))});bK.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},bK.classNames={fullWidth:bD,zeroRight:bC};var bL=function(a){var b=a.sideCar,c=bB(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return g.createElement(d,bA({},c))};bL.isSideCarExport=!0;var bM=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},bN=function(){var a=bM();return function(b,c){g.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},bO=function(){var a=bN();return function(b){return a(b.styles,b.dynamic),null}},bP={left:0,top:0,right:0,gap:0},bQ=function(a){return parseInt(a||"",10)||0},bR=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[bQ(c),bQ(d),bQ(e)]},bS=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return bP;var b=bR(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},bT=bO(),bU="data-scroll-locked",bV=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(bU,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(bC," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bD," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bC," .").concat(bC," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(bD," .").concat(bD," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(bU,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},bW=function(){var a=parseInt(document.body.getAttribute(bU)||"0",10);return isFinite(a)?a:0},bX=function(){g.useEffect(function(){return document.body.setAttribute(bU,(bW()+1).toString()),function(){var a=bW()-1;a<=0?document.body.removeAttribute(bU):document.body.setAttribute(bU,a.toString())}},[])},bY=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;bX();var f=g.useMemo(function(){return bS(e)},[e]);return g.createElement(bT,{styles:bV(f,!b,e,c?"":"!important")})},bZ=!1;if("undefined"!=typeof window)try{var b$=Object.defineProperty({},"passive",{get:function(){return bZ=!0,!0}});window.addEventListener("test",b$,b$),window.removeEventListener("test",b$,b$)}catch(a){bZ=!1}var b_=!!bZ&&{passive:!1},b0=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},b1=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),b2(a,d)){var e=b3(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},b2=function(a,b){return"v"===a?b0(b,"overflowY"):b0(b,"overflowX")},b3=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},b4=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=b3(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&b2(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},b5=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},b6=function(a){return[a.deltaX,a.deltaY]},b7=function(a){return a&&"current"in a?a.current:a},b8=0,b9=[];let ca=(d=function(a){var b=g.useRef([]),c=g.useRef([0,0]),d=g.useRef(),e=g.useState(b8++)[0],f=g.useState(bO)[0],h=g.useRef(a);g.useEffect(function(){h.current=a},[a]),g.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(b7),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=g.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=b5(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=b1(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=b1(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return b4(n,b,a,"h"===n?i:j,!0)},[]),j=g.useCallback(function(a){if(b9.length&&b9[b9.length-1]===f){var c="deltaY"in a?b6(a):b5(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(b7).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=g.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=g.useCallback(function(a){c.current=b5(a),d.current=void 0},[]),m=g.useCallback(function(b){k(b.type,b6(b),b.target,i(b,a.lockRef.current))},[]),n=g.useCallback(function(b){k(b.type,b5(b),b.target,i(b,a.lockRef.current))},[]);g.useEffect(function(){return b9.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,b_),document.addEventListener("touchmove",j,b_),document.addEventListener("touchstart",l,b_),function(){b9=b9.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,b_),document.removeEventListener("touchmove",j,b_),document.removeEventListener("touchstart",l,b_)}},[]);var o=a.removeScrollBar,p=a.inert;return g.createElement(g.Fragment,null,p?g.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?g.createElement(bY,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},bI.useMedium(d),bL);var cb=g.forwardRef(function(a,b){return g.createElement(bK,bA({},a,{ref:b,sideCar:ca}))});cb.classNames=bK.classNames;var cc=[" ","Enter","ArrowUp","ArrowDown"],cd=[" ","Enter"],ce="Select",[cf,cg,ch]=(0,k.N)(ce),[ci,cj]=(0,m.A)(ce,[ch,a8]),ck=a8(),[cl,cm]=ci(ce),[cn,co]=ci(ce),cp=a=>{let{__scopeSelect:b,children:c,open:d,defaultOpen:e,onOpenChange:f,value:h,defaultValue:i,onValueChange:j,dir:k,name:l,autoComplete:m,disabled:o,required:p,form:r}=a,s=ck(b),[t,u]=g.useState(null),[v,w]=g.useState(null),[x,y]=g.useState(!1),z=(0,n.jH)(k),[A,B]=(0,bq.i)({prop:d,defaultProp:e??!1,onChange:f,caller:ce}),[C,D]=(0,bq.i)({prop:h,defaultProp:i,onChange:j,caller:ce}),E=g.useRef(null),F=!t||r||!!t.closest("form"),[G,I]=g.useState(new Set),J=Array.from(G).map(a=>a.props.value).join(";");return(0,q.jsx)(bb,{...s,children:(0,q.jsxs)(cl,{required:p,scope:b,trigger:t,onTriggerChange:u,valueNode:v,onValueNodeChange:w,valueNodeHasChildren:x,onValueNodeHasChildrenChange:y,contentId:(0,H.B)(),value:C,onValueChange:D,open:A,onOpenChange:B,dir:z,triggerPointerDownPosRef:E,disabled:o,children:[(0,q.jsx)(cf.Provider,{scope:b,children:(0,q.jsx)(cn,{scope:a.__scopeSelect,onNativeOptionAdd:g.useCallback(a=>{I(b=>new Set(b).add(a))},[]),onNativeOptionRemove:g.useCallback(a=>{I(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),F?(0,q.jsxs)(c1,{"aria-hidden":!0,required:p,tabIndex:-1,name:l,autoComplete:m,value:C,onChange:a=>D(a.target.value),disabled:o,form:r,children:[void 0===C?(0,q.jsx)("option",{value:""}):null,Array.from(G)]},J):null]})})};cp.displayName=ce;var cq="SelectTrigger",cr=g.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:d=!1,...e}=a,f=ck(c),h=cm(cq,c),i=h.disabled||d,k=(0,l.s)(b,h.onTriggerChange),m=cg(c),n=g.useRef("touch"),[p,r,s]=c3(a=>{let b=m().filter(a=>!a.disabled),c=b.find(a=>a.value===h.value),d=c4(b,a,c);void 0!==d&&h.onValueChange(d.value)}),t=a=>{i||(h.onOpenChange(!0),s()),a&&(h.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,q.jsx)(bd,{asChild:!0,...f,children:(0,q.jsx)(o.sG.button,{type:"button",role:"combobox","aria-controls":h.contentId,"aria-expanded":h.open,"aria-required":h.required,"aria-autocomplete":"none",dir:h.dir,"data-state":h.open?"open":"closed",disabled:i,"data-disabled":i?"":void 0,"data-placeholder":c2(h.value)?"":void 0,...e,ref:k,onClick:(0,j.mK)(e.onClick,a=>{a.currentTarget.focus(),"mouse"!==n.current&&t(a)}),onPointerDown:(0,j.mK)(e.onPointerDown,a=>{n.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(t(a),a.preventDefault())}),onKeyDown:(0,j.mK)(e.onKeyDown,a=>{let b=""!==p.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||r(a.key),(!b||" "!==a.key)&&cc.includes(a.key)&&(t(),a.preventDefault())})})})});cr.displayName=cq;var cs="SelectValue",ct=g.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:g="",...h}=a,i=cm(cs,c),{onValueNodeHasChildrenChange:j}=i,k=void 0!==f,m=(0,l.s)(b,i.onValueNodeChange);return(0,a4.N)(()=>{j(k)},[j,k]),(0,q.jsx)(o.sG.span,{...h,ref:m,style:{pointerEvents:"none"},children:c2(i.value)?(0,q.jsx)(q.Fragment,{children:g}):f})});ct.displayName=cs;var cu=g.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,q.jsx)(o.sG.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});cu.displayName="SelectIcon";var cv=a=>(0,q.jsx)(bo,{asChild:!0,...a});cv.displayName="SelectPortal";var cw="SelectContent",cx=g.forwardRef((a,b)=>{let c=cm(cw,a.__scopeSelect),[d,e]=g.useState();return((0,a4.N)(()=>{e(new DocumentFragment)},[]),c.open)?(0,q.jsx)(cB,{...a,ref:b}):d?h.createPortal((0,q.jsx)(cy,{scope:a.__scopeSelect,children:(0,q.jsx)(cf.Slot,{scope:a.__scopeSelect,children:(0,q.jsx)("div",{children:a.children})})}),d):null});cx.displayName=cw;var[cy,cz]=ci(cw),cA=(0,bp.TL)("SelectContent.RemoveScroll"),cB=g.forwardRef((a,b)=>{let{__scopeSelect:c,position:d="item-aligned",onCloseAutoFocus:e,onEscapeKeyDown:f,onPointerDownOutside:h,side:i,sideOffset:k,align:m,alignOffset:n,arrowPadding:o,collisionBoundary:p,collisionPadding:r,sticky:s,hideWhenDetached:u,avoidCollisions:v,...y}=a,z=cm(cw,c),[A,C]=g.useState(null),[D,E]=g.useState(null),F=(0,l.s)(b,a=>C(a)),[G,H]=g.useState(null),[I,J]=g.useState(null),K=cg(c),[L,M]=g.useState(!1),N=g.useRef(!1);g.useEffect(()=>{if(A)return bz(A)},[A]),g.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??x()),document.body.insertAdjacentElement("beforeend",a[1]??x()),w++,()=>{1===w&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),w--}},[]);let O=g.useCallback(a=>{let[b,...c]=K().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&D&&(D.scrollTop=0),c===d&&D&&(D.scrollTop=D.scrollHeight),c?.focus(),document.activeElement!==e))return},[K,D]),P=g.useCallback(()=>O([G,A]),[O,G,A]);g.useEffect(()=>{L&&P()},[L,P]);let{onOpenChange:Q,triggerPointerDownPosRef:R}=z;g.useEffect(()=>{if(A){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(R.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(R.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():A.contains(c.target)||Q(!1),document.removeEventListener("pointermove",b),R.current=null};return null!==R.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[A,Q,R]),g.useEffect(()=>{let a=()=>Q(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[Q]);let[S,T]=c3(a=>{let b=K().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=c4(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),U=g.useCallback((a,b,c)=>{let d=!N.current&&!c;(void 0!==z.value&&z.value===b||d)&&(H(a),d&&(N.current=!0))},[z.value]),V=g.useCallback(()=>A?.focus(),[A]),W=g.useCallback((a,b,c)=>{let d=!N.current&&!c;(void 0!==z.value&&z.value===b||d)&&J(a)},[z.value]),X="popper"===d?cD:cC,Y=X===cD?{side:i,sideOffset:k,align:m,alignOffset:n,arrowPadding:o,collisionBoundary:p,collisionPadding:r,sticky:s,hideWhenDetached:u,avoidCollisions:v}:{};return(0,q.jsx)(cy,{scope:c,content:A,viewport:D,onViewportChange:E,itemRefCallback:U,selectedItem:G,onItemLeave:V,itemTextRefCallback:W,focusSelectedItem:P,selectedItemText:I,position:d,isPositioned:L,searchRef:S,children:(0,q.jsx)(cb,{as:cA,allowPinchZoom:!0,children:(0,q.jsx)(B,{asChild:!0,trapped:z.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:(0,j.mK)(e,a=>{z.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,q.jsx)(t,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>z.onOpenChange(!1),children:(0,q.jsx)(X,{role:"listbox",id:z.contentId,"data-state":z.open?"open":"closed",dir:z.dir,onContextMenu:a=>a.preventDefault(),...y,...Y,onPlaced:()=>M(!0),ref:F,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:(0,j.mK)(y.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||T(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=K().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>O(b)),a.preventDefault()}})})})})})})});cB.displayName="SelectContentImpl";var cC=g.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:d,...e}=a,f=cm(cw,c),h=cz(cw,c),[j,k]=g.useState(null),[m,n]=g.useState(null),p=(0,l.s)(b,a=>n(a)),r=cg(c),s=g.useRef(!1),t=g.useRef(!0),{viewport:u,selectedItem:v,selectedItemText:w,focusSelectedItem:x}=h,y=g.useCallback(()=>{if(f.trigger&&f.valueNode&&j&&m&&u&&v&&w){let a=f.trigger.getBoundingClientRect(),b=m.getBoundingClientRect(),c=f.valueNode.getBoundingClientRect(),e=w.getBoundingClientRect();if("rtl"!==f.dir){let d=e.left-b.left,f=c.left-d,g=a.left-f,h=a.width+g,k=Math.max(h,b.width),l=window.innerWidth-10,m=(0,i.q)(f,[10,Math.max(10,l-k)]);j.style.minWidth=h+"px",j.style.left=m+"px"}else{let d=b.right-e.right,f=window.innerWidth-c.right-d,g=window.innerWidth-a.right-f,h=a.width+g,k=Math.max(h,b.width),l=window.innerWidth-10,m=(0,i.q)(f,[10,Math.max(10,l-k)]);j.style.minWidth=h+"px",j.style.right=m+"px"}let g=r(),h=window.innerHeight-20,k=u.scrollHeight,l=window.getComputedStyle(m),n=parseInt(l.borderTopWidth,10),o=parseInt(l.paddingTop,10),p=parseInt(l.borderBottomWidth,10),q=n+o+k+parseInt(l.paddingBottom,10)+p,t=Math.min(5*v.offsetHeight,q),x=window.getComputedStyle(u),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=v.offsetHeight/2,C=n+o+(v.offsetTop+B);if(C<=A){let a=g.length>0&&v===g[g.length-1].ref.current;j.style.bottom="0px";let b=Math.max(h-A,B+(a?z:0)+(m.clientHeight-u.offsetTop-u.offsetHeight)+p);j.style.height=C+b+"px"}else{let a=g.length>0&&v===g[0].ref.current;j.style.top="0px";let b=Math.max(A,n+u.offsetTop+(a?y:0)+B);j.style.height=b+(q-C)+"px",u.scrollTop=C-A+u.offsetTop}j.style.margin="10px 0",j.style.minHeight=t+"px",j.style.maxHeight=h+"px",d?.(),requestAnimationFrame(()=>s.current=!0)}},[r,f.trigger,f.valueNode,j,m,u,v,w,f.dir,d]);(0,a4.N)(()=>y(),[y]);let[z,A]=g.useState();(0,a4.N)(()=>{m&&A(window.getComputedStyle(m).zIndex)},[m]);let B=g.useCallback(a=>{a&&!0===t.current&&(y(),x?.(),t.current=!1)},[y,x]);return(0,q.jsx)(cE,{scope:c,contentWrapper:j,shouldExpandOnScrollRef:s,onScrollButtonChange:B,children:(0,q.jsx)("div",{ref:k,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:z},children:(0,q.jsx)(o.sG.div,{...e,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...e.style}})})})});cC.displayName="SelectItemAlignedPosition";var cD=g.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,g=ck(c);return(0,q.jsx)(bh,{...g,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});cD.displayName="SelectPopperPosition";var[cE,cF]=ci(cw,{}),cG="SelectViewport",cH=g.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:d,...e}=a,f=cz(cG,c),h=cF(cG,c),i=(0,l.s)(b,f.onViewportChange),k=g.useRef(0);return(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:d}),(0,q.jsx)(cf.Slot,{scope:c,children:(0,q.jsx)(o.sG.div,{"data-radix-select-viewport":"",role:"presentation",...e,ref:i,style:{position:"relative",flex:1,overflow:"hidden auto",...e.style},onScroll:(0,j.mK)(e.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=h;if(d?.current&&c){let a=Math.abs(k.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}k.current=b.scrollTop})})})]})});cH.displayName=cG;var cI="SelectGroup",[cJ,cK]=ci(cI),cL=g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=(0,H.B)();return(0,q.jsx)(cJ,{scope:c,id:e,children:(0,q.jsx)(o.sG.div,{role:"group","aria-labelledby":e,...d,ref:b})})});cL.displayName=cI;var cM="SelectLabel",cN=g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=cK(cM,c);return(0,q.jsx)(o.sG.div,{id:e.id,...d,ref:b})});cN.displayName=cM;var cO="SelectItem",[cP,cQ]=ci(cO),cR=g.forwardRef((a,b)=>{let{__scopeSelect:c,value:d,disabled:e=!1,textValue:f,...h}=a,i=cm(cO,c),k=cz(cO,c),m=i.value===d,[n,p]=g.useState(f??""),[r,s]=g.useState(!1),t=(0,l.s)(b,a=>k.itemRefCallback?.(a,d,e)),u=(0,H.B)(),v=g.useRef("touch"),w=()=>{e||(i.onValueChange(d),i.onOpenChange(!1))};if(""===d)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,q.jsx)(cP,{scope:c,value:d,disabled:e,textId:u,isSelected:m,onItemTextChange:g.useCallback(a=>{p(b=>b||(a?.textContent??"").trim())},[]),children:(0,q.jsx)(cf.ItemSlot,{scope:c,value:d,disabled:e,textValue:n,children:(0,q.jsx)(o.sG.div,{role:"option","aria-labelledby":u,"data-highlighted":r?"":void 0,"aria-selected":m&&r,"data-state":m?"checked":"unchecked","aria-disabled":e||void 0,"data-disabled":e?"":void 0,tabIndex:e?void 0:-1,...h,ref:t,onFocus:(0,j.mK)(h.onFocus,()=>s(!0)),onBlur:(0,j.mK)(h.onBlur,()=>s(!1)),onClick:(0,j.mK)(h.onClick,()=>{"mouse"!==v.current&&w()}),onPointerUp:(0,j.mK)(h.onPointerUp,()=>{"mouse"===v.current&&w()}),onPointerDown:(0,j.mK)(h.onPointerDown,a=>{v.current=a.pointerType}),onPointerMove:(0,j.mK)(h.onPointerMove,a=>{v.current=a.pointerType,e?k.onItemLeave?.():"mouse"===v.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,j.mK)(h.onPointerLeave,a=>{a.currentTarget===document.activeElement&&k.onItemLeave?.()}),onKeyDown:(0,j.mK)(h.onKeyDown,a=>{(k.searchRef?.current===""||" "!==a.key)&&(cd.includes(a.key)&&w()," "===a.key&&a.preventDefault())})})})})});cR.displayName=cO;var cS="SelectItemText",cT=g.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,...f}=a,i=cm(cS,c),j=cz(cS,c),k=cQ(cS,c),m=co(cS,c),[n,p]=g.useState(null),r=(0,l.s)(b,a=>p(a),k.onItemTextChange,a=>j.itemTextRefCallback?.(a,k.value,k.disabled)),s=n?.textContent,t=g.useMemo(()=>(0,q.jsx)("option",{value:k.value,disabled:k.disabled,children:s},k.value),[k.disabled,k.value,s]),{onNativeOptionAdd:u,onNativeOptionRemove:v}=m;return(0,a4.N)(()=>(u(t),()=>v(t)),[u,v,t]),(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)(o.sG.span,{id:k.textId,...f,ref:r}),k.isSelected&&i.valueNode&&!i.valueNodeHasChildren?h.createPortal(f.children,i.valueNode):null]})});cT.displayName=cS;var cU="SelectItemIndicator",cV=g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return cQ(cU,c).isSelected?(0,q.jsx)(o.sG.span,{"aria-hidden":!0,...d,ref:b}):null});cV.displayName=cU;var cW="SelectScrollUpButton",cX=g.forwardRef((a,b)=>{let c=cz(cW,a.__scopeSelect),d=cF(cW,a.__scopeSelect),[e,f]=g.useState(!1),h=(0,l.s)(b,d.onScrollButtonChange);return(0,a4.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){f(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,q.jsx)(c$,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});cX.displayName=cW;var cY="SelectScrollDownButton",cZ=g.forwardRef((a,b)=>{let c=cz(cY,a.__scopeSelect),d=cF(cY,a.__scopeSelect),[e,f]=g.useState(!1),h=(0,l.s)(b,d.onScrollButtonChange);return(0,a4.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;f(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,q.jsx)(c$,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});cZ.displayName=cY;var c$=g.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:d,...e}=a,f=cz("SelectScrollButton",c),h=g.useRef(null),i=cg(c),k=g.useCallback(()=>{null!==h.current&&(window.clearInterval(h.current),h.current=null)},[]);return g.useEffect(()=>()=>k(),[k]),(0,a4.N)(()=>{let a=i().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[i]),(0,q.jsx)(o.sG.div,{"aria-hidden":!0,...e,ref:b,style:{flexShrink:0,...e.style},onPointerDown:(0,j.mK)(e.onPointerDown,()=>{null===h.current&&(h.current=window.setInterval(d,50))}),onPointerMove:(0,j.mK)(e.onPointerMove,()=>{f.onItemLeave?.(),null===h.current&&(h.current=window.setInterval(d,50))}),onPointerLeave:(0,j.mK)(e.onPointerLeave,()=>{k()})})}),c_=g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,q.jsx)(o.sG.div,{"aria-hidden":!0,...d,ref:b})});c_.displayName="SelectSeparator";var c0="SelectArrow";g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=ck(c),f=cm(c0,c),g=cz(c0,c);return f.open&&"popper"===g.position?(0,q.jsx)(bk,{...e,...d,ref:b}):null}).displayName=c0;var c1=g.forwardRef(({__scopeSelect:a,value:b,...c},d)=>{let e=g.useRef(null),f=(0,l.s)(d,e),h=(0,br.Z)(b);return g.useEffect(()=>{let a=e.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(h!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[h,b]),(0,q.jsx)(o.sG.select,{...c,style:{...bs,...c.style},ref:f,defaultValue:b})});function c2(a){return""===a||void 0===a}function c3(a){let b=(0,p.c)(a),c=g.useRef(""),d=g.useRef(0),e=g.useCallback(a=>{let e=c.current+a;b(e),function a(b){c.current=b,window.clearTimeout(d.current),""!==b&&(d.current=window.setTimeout(()=>a(""),1e3))}(e)},[b]),f=g.useCallback(()=>{c.current="",window.clearTimeout(d.current)},[]);return g.useEffect(()=>()=>window.clearTimeout(d.current),[]),[c,e,f]}function c4(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}c1.displayName="SelectBubbleInput";var c5=cp,c6=cr,c7=ct,c8=cu,c9=cv,da=cx,db=cH,dc=cL,dd=cN,de=cR,df=cT,dg=cV,dh=cX,di=cZ,dj=c_},18853:(a,b,c)=>{c.d(b,{X:()=>f});var d=c(43210),e=c(66156);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},28559:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},65551:(a,b,c)=>{c.d(b,{i:()=>h});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useInsertionEffect ".trim().toString()]||f.N;function h({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[f,h,i]=function({defaultProp:a,onChange:b}){let[c,d]=e.useState(a),f=e.useRef(c),h=e.useRef(b);return g(()=>{h.current=b},[b]),e.useEffect(()=>{f.current!==c&&(h.current?.(c),f.current=c)},[c,f]),[c,d,h]}({defaultProp:b,onChange:c}),j=void 0!==a,k=j?a:f;{let b=e.useRef(void 0!==a);e.useEffect(()=>{let a=b.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=j},[j,d])}return[k,e.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},66156:(a,b,c)=>{c.d(b,{N:()=>e});var d=c(43210),e=globalThis?.document?d.useLayoutEffect:()=>{}},67969:(a,b,c)=>{c.d(b,{q:()=>d});function d(a,[b,c]){return Math.min(c,Math.max(b,a))}},70569:(a,b,c)=>{function d(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}c.d(b,{mK:()=>d}),"undefined"!=typeof window&&window.document&&window.document.createElement},78272:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},83721:(a,b,c)=>{c.d(b,{Z:()=>e});var d=c(43210);function e(a){let b=d.useRef({value:a,previous:a});return d.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}},96963:(a,b,c)=>{c.d(b,{B:()=>i});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useId ".trim().toString()]||(()=>void 0),h=0;function i(a){let[b,c]=e.useState(g());return(0,f.N)(()=>{a||c(a=>a??String(h++))},[a]),a||(b?`radix-${b}`:"")}}};