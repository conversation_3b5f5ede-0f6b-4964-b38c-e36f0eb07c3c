(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[349],{2883:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>y});var l=a(5155),r=a(2115),c=a(2108),t=a(5695),i=a(6695),n=a(285),d=a(6126),x=a(5040),m=a(7580),h=a(7434),o=a(1497),j=a(4616),u=a(5657),p=a(9869),N=a(2657),g=a(3717),v=a(381),f=a(693),b=a(5731);function y(){var e;let{data:s}=(0,c.useSession)(),a=(0,t.useRouter)(),y=(0,f.fR)(),[w,A]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{await f.R9.loadSubjects(),A(!1)})()},[]);let C=()=>{a.push("/teacher/subjects/create")},k=()=>{a.push("/teacher/avatar")},Z=y.filter(e=>{var a;return e.teacherId===(null==s||null==(a=s.user)?void 0:a.id)});return w?(0,l.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Loading your dashboard..."})]})}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-gradient-to-r from-green-600 to-teal-600 rounded-lg p-6 text-white",children:[(0,l.jsxs)("h1",{className:"text-2xl font-bold mb-2",children:["Welcome, ",null==s||null==(e=s.user)?void 0:e.name,"!"]}),(0,l.jsx)("p",{className:"text-green-100",children:"Manage your subjects, upload materials, and customize your AI avatar to enhance student learning."})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,l.jsx)(i.Zp,{children:(0,l.jsx)(i.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,l.jsx)(x.A,{className:"h-5 w-5 text-blue-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"My Subjects"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:Z.length})]})]})})}),(0,l.jsx)(i.Zp,{children:(0,l.jsx)(i.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,l.jsx)(m.A,{className:"h-5 w-5 text-green-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Students"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:"48"})]})]})})}),(0,l.jsx)(i.Zp,{children:(0,l.jsx)(i.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,l.jsx)(h.A,{className:"h-5 w-5 text-purple-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Materials"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:"23"})]})]})})}),(0,l.jsx)(i.Zp,{children:(0,l.jsx)(i.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,l.jsx)(o.A,{className:"h-5 w-5 text-orange-600"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"AI Interactions"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:"156"})]})]})})})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{children:[(0,l.jsx)(i.ZB,{children:"Quick Actions"}),(0,l.jsx)(i.BT,{children:"Common tasks to manage your teaching"})]}),(0,l.jsx)(i.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,l.jsxs)(n.$,{onClick:C,className:"h-20 flex-col space-y-2",children:[(0,l.jsx)(j.A,{className:"h-6 w-6"}),(0,l.jsx)("span",{children:"Create Subject"})]}),(0,l.jsxs)(n.$,{onClick:k,variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,l.jsx)(u.A,{className:"h-6 w-6"}),(0,l.jsx)("span",{children:"Setup AI Avatar"})]}),(0,l.jsxs)(n.$,{onClick:()=>a.push("/teacher/materials/upload"),variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,l.jsx)(p.A,{className:"h-6 w-6"}),(0,l.jsx)("span",{children:"Upload Materials"})]}),(0,l.jsxs)(n.$,{onClick:()=>a.push("/teacher/analytics"),variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,l.jsx)(N.A,{className:"h-6 w-6"}),(0,l.jsx)("span",{children:"View Analytics"})]})]})})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"My Subjects"}),(0,l.jsxs)(n.$,{onClick:C,children:[(0,l.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Create New Subject"]})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:(Z.length>0?Z:b.jy.subjects).map(e=>(0,l.jsxs)(i.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,l.jsxs)(i.aR,{children:[(0,l.jsxs)(i.ZB,{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-lg",children:e.name}),(0,l.jsx)(d.E,{variant:"secondary",children:"Active"})]}),(0,l.jsx)(i.BT,{children:e.description||"No description provided"})]}),(0,l.jsx)(i.Wu,{children:(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Students enrolled:"}),(0,l.jsx)("span",{className:"font-medium",children:"24"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Materials:"}),(0,l.jsx)("span",{className:"font-medium",children:"8"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"AI Chats:"}),(0,l.jsx)("span",{className:"font-medium",children:"67"})]}),(0,l.jsxs)("div",{className:"pt-2 space-y-2",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,l.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{var s;return s=e.id,void a.push("/teacher/subjects/".concat(s,"/edit"))},children:[(0,l.jsx)(g.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,l.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{var s;return s=e.id,void a.push("/teacher/subjects/".concat(s,"/chats"))},children:[(0,l.jsx)(o.A,{className:"h-4 w-4 mr-1"}),"Chats"]})]}),(0,l.jsxs)(n.$,{onClick:()=>{var s;return s=e.id,void a.push("/teacher/subjects/".concat(s,"/materials"))},className:"w-full",size:"sm",children:[(0,l.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Manage Materials"]})]})]})})]},e.id))})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{children:[(0,l.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,l.jsx)(u.A,{className:"h-5 w-5"}),(0,l.jsx)("span",{children:"AI Avatar Configuration"})]}),(0,l.jsx)(i.BT,{children:"Customize your AI teaching assistant's appearance and personality"})]}),(0,l.jsx)(i.Wu,{children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(u.A,{className:"h-6 w-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Default Avatar"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Professional, Friendly tone"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsxs)(n.$,{variant:"outline",onClick:k,children:[(0,l.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Customize"]}),(0,l.jsxs)(n.$,{onClick:()=>a.push("/teacher/avatar/preview"),children:[(0,l.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Preview"]})]})]})})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{children:[(0,l.jsx)(i.ZB,{children:"Recent Activity"}),(0,l.jsx)(i.BT,{children:"Latest interactions and updates"})]}),(0,l.jsx)(i.Wu,{children:(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)(o.A,{className:"h-5 w-5 text-blue-600"}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("p",{className:"font-medium",children:"Student asked about grammar patterns"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Japanese Language - 30 minutes ago"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)(p.A,{className:"h-5 w-5 text-green-600"}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("p",{className:"font-medium",children:"New material uploaded"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Chapter 6 exercises - 2 hours ago"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)(m.A,{className:"h-5 w-5 text-purple-600"}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("p",{className:"font-medium",children:"3 new students enrolled"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Mathematics course - 1 day ago"})]})]})]})})]})]})}},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>i});var l=a(5155);a(2115);var r=a(2085),c=a(9434);let t=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:a,...r}=e;return(0,l.jsx)("div",{className:(0,c.cn)(t({variant:a}),s),...r})}},8760:(e,s,a)=>{Promise.resolve().then(a.bind(a,2883))}},e=>{e.O(0,[817,108,2,747,441,964,358],()=>e(e.s=8760)),_N_E=e.O()}]);