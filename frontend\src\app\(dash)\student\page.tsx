'use client';

// Student Dashboard - View subjects and access AI chat

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BookOpen, 
  MessageSquare, 
  Clock, 
  User,
  Play,
  FileText,
  Headphones
} from 'lucide-react';
import { useSubjects, dataLoaders } from '@/store/useUser';
import { useCurrentSubject, useChatActions } from '@/store/useChat';
import { mockData } from '@/lib/api';
import { Subject } from '@/types';

export default function StudentDashboard() {
  const { data: session } = useSession();
  const router = useRouter();
  const subjects = useSubjects();
  const currentSubject = useCurrentSubject();
  const { setCurrentSubject } = useChatActions();
  const [isLoading, setIsLoading] = useState(true);
  const [hasLoaded, setHasLoaded] = useState(false);

  useEffect(() => {
    if (!hasLoaded) {
      const loadData = async () => {
        await dataLoaders.loadSubjects();
        setIsLoading(false);
        setHasLoaded(true);
      };
      loadData();
    }
  }, [hasLoaded]); // Removed loadSubjects from dependencies - it's now stable

  const handleStartChat = (subject: Subject) => {
    setCurrentSubject(subject);
    router.push('/student/chat');
  };

  const handleViewMaterials = (subjectId: string) => {
    router.push(`/student/materials?subject=${subjectId}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your subjects...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome back, {session?.user?.name}!
        </h1>
        <p className="text-blue-100">
          Ready to continue your learning journey? Choose a subject to start chatting with your AI tutor.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BookOpen className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Enrolled Subjects</p>
                <p className="text-2xl font-bold">{subjects.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <MessageSquare className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Chat Sessions</p>
                <p className="text-2xl font-bold">12</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Study Time</p>
                <p className="text-2xl font-bold">24h</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Subject */}
      {currentSubject && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <span>Continue Learning</span>
            </CardTitle>
            <CardDescription>
              You were last studying {currentSubject.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <User className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium">{currentSubject.name}</p>
                  <p className="text-sm text-gray-600">with {currentSubject.teacherName}</p>
                </div>
              </div>
              <Button onClick={() => handleStartChat(currentSubject)}>
                <MessageSquare className="h-4 w-4 mr-2" />
                Continue Chat
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Available Subjects */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Your Subjects</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {subjects.length > 0 ? subjects.map((subject) => (
            <Card key={subject.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="text-lg">{subject.name}</span>
                  <Badge variant="secondary">Active</Badge>
                </CardTitle>
                <CardDescription>
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>{subject.teacherName}</span>
                  </div>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  {subject.description || 'Enhance your knowledge with AI-powered tutoring.'}
                </p>
                
                <div className="space-y-2">
                  <Button 
                    onClick={() => handleStartChat(subject)}
                    className="w-full"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Start AI Chat
                  </Button>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewMaterials(subject.id)}
                    >
                      <FileText className="h-4 w-4 mr-1" />
                      Materials
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => router.push(`/student/practice?subject=${subject.id}`)}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      Practice
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )) : (
            // Show mock subjects if none loaded
            mockData.subjects.map((subject) => (
              <Card key={subject.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="text-lg">{subject.name}</span>
                    <Badge variant="secondary">Active</Badge>
                  </CardTitle>
                  <CardDescription>
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span>{subject.teacherName}</span>
                    </div>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    {subject.description || 'Enhance your knowledge with AI-powered tutoring.'}
                  </p>
                  
                  <div className="space-y-2">
                    <Button 
                      onClick={() => handleStartChat(subject)}
                      className="w-full"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Start AI Chat
                    </Button>
                    
                    <div className="grid grid-cols-2 gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleViewMaterials(subject.id)}
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Materials
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => router.push(`/student/practice?subject=${subject.id}`)}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        Practice
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Your latest learning sessions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <div className="flex-1">
                <p className="font-medium">Japanese Language Chat</p>
                <p className="text-sm text-gray-600">Discussed grammar patterns - 2 hours ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <Headphones className="h-5 w-5 text-green-600" />
              <div className="flex-1">
                <p className="font-medium">Pronunciation Practice</p>
                <p className="text-sm text-gray-600">Completed voice exercises - 1 day ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <FileText className="h-5 w-5 text-purple-600" />
              <div className="flex-1">
                <p className="font-medium">Study Material Review</p>
                <p className="text-sm text-gray-600">Read Chapter 5 notes - 2 days ago</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}