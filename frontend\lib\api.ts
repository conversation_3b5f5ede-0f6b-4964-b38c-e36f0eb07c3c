// API utilities and mock data for AI Tutor Platform
import { User, Subject, Material, ChatMessage, Analytics } from '@/types';

// API Base Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// API Client with error handling
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Auth API
  async getMe(): Promise<User> {
    // TODO: Replace with actual API call
    // return this.request<User>('/api/me');
    return mockData.user;
  }

  // Subjects API
  async getSubjects(): Promise<Subject[]> {
    // TODO: Replace with actual API call
    // return this.request<Subject[]>('/api/subjects');
    return mockData.subjects;
  }

  // Materials API
  async getMaterials(subjectId?: string): Promise<Material[]> {
    // TODO: Replace with actual API call
    // return this.request<Material[]>(`/api/materials${subjectId ? `?subjectId=${subjectId}` : ''}`);
    return mockData.materials.filter(m => !subjectId || m.subjectId === subjectId);
  }

  // Chat API
  async sendMessage(subjectId: string, message: string): Promise<ReadableStream> {
    // TODO: Replace with actual SSE streaming API call
    // return this.request<ReadableStream>('/api/chat', {
    //   method: 'POST',
    //   body: JSON.stringify({ subjectId, message })
    // });
    
    // Mock streaming response
    return new ReadableStream({
      start(controller) {
        const mockResponse = "This is a mock AI response. In production, this would be a streaming response from the backend.";
        const encoder = new TextEncoder();
        
        let i = 0;
        const interval = setInterval(() => {
          if (i < mockResponse.length) {
            controller.enqueue(encoder.encode(mockResponse[i]));
            i++;
          } else {
            controller.close();
            clearInterval(interval);
          }
        }, 50);
      }
    });
  }

  // File Upload API
  async getSignedUploadUrl(fileName: string, fileType: string): Promise<{ url: string; key: string }> {
    // TODO: Replace with actual API call
    // return this.request<{ url: string; key: string }>('/api/s3/sign', {
    //   method: 'POST',
    //   body: JSON.stringify({ fileName, fileType })
    // });
    
    return {
      url: `https://mock-s3-bucket.s3.amazonaws.com/${fileName}`,
      key: `uploads/${Date.now()}-${fileName}`
    };
  }

  // TTS API
  async getTTS(textId: string): Promise<{ audioUrl: string; visemes: any[] }> {
    // TODO: Replace with actual API call
    // return this.request<{ audioUrl: string; visemes: any[] }>(`/api/tts?id=${textId}`);
    
    return {
      audioUrl: '/mock-audio.mp3',
      visemes: [] // Mock viseme data for lip-sync
    };
  }

  // Avatar API
  async saveAvatarConfig(config: any): Promise<{ success: boolean }> {
    // TODO: Replace with actual API call
    // return this.request<{ success: boolean }>('/api/avatar', {
    //   method: 'POST',
    //   body: JSON.stringify(config)
    // });
    
    return { success: true };
  }

  // Analytics API
  async getAnalytics(): Promise<Analytics> {
    // TODO: Replace with actual API call
    // return this.request<Analytics>('/api/analytics');
    return mockData.analytics;
  }
}

// Export API client instance
export const api = new ApiClient(API_BASE_URL);

// Mock Data for Development
export const mockData = {
  user: {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'student' as const,
    orgId: 'org-1',
    avatar: '/avatars/student.png',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
  },

  subjects: [
    {
      id: '1',
      name: 'Japanese Language',
      description: 'Learn Japanese with AI-powered conversations',
      teacherId: 'teacher-1',
      teacherName: 'Tanaka Sensei',
      orgId: 'org-1',
      isActive: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15'),
    },
    {
      id: '2',
      name: 'Mathematics',
      description: 'Advanced mathematics with step-by-step explanations',
      teacherId: 'teacher-2',
      teacherName: 'Dr. Smith',
      orgId: 'org-1',
      isActive: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15'),
    },
  ],

  materials: [
    {
      id: '1',
      title: 'Introduction to Hiragana',
      description: 'Basic Japanese writing system',
      subjectId: '1',
      teacherId: 'teacher-1',
      fileUrl: '/materials/hiragana.pdf',
      fileType: 'pdf',
      fileSize: 1024000,
      isPublic: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15'),
    },
  ],

  analytics: {
    totalUsers: 150,
    totalStudents: 120,
    totalTeachers: 25,
    totalSubjects: 15,
    totalMaterials: 85,
    totalChats: 1250,
    activeUsersToday: 45,
    activeUsersThisWeek: 98,
    popularSubjects: [
      { subjectId: '1', subjectName: 'Japanese Language', chatCount: 450 },
      { subjectId: '2', subjectName: 'Mathematics', chatCount: 320 },
    ],
    userGrowth: [
      { date: '2024-01-01', count: 100 },
      { date: '2024-02-01', count: 120 },
      { date: '2024-03-01', count: 150 },
    ],
  },
};

// Utility functions
export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

export const getFileIcon = (fileType: string): string => {
  const icons: Record<string, string> = {
    pdf: '📄',
    doc: '📝',
    docx: '📝',
    ppt: '📊',
    pptx: '📊',
    mp3: '🎵',
    mp4: '🎬',
    jpg: '🖼️',
    jpeg: '🖼️',
    png: '🖼️',
    gif: '🖼️',
  };
  return icons[fileType.toLowerCase()] || '📁';
};
