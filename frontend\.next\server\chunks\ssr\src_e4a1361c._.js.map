{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/lib/api.ts"], "sourcesContent": ["// API utility functions for the AI Tutor Platform\n\nimport {\n  User,\n  Subject,\n  StudyMaterial,\n  ChatMessage,\n  TeacherAvatar,\n  Announcement,\n  Analytics,\n  ApiResponse,\n  PaginatedResponse,\n  S3SignedUrl,\n  StreamingResponse,\n  Viseme\n} from '@/types';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1';\n\n// Generic API request function\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<ApiResponse<T>> {\n  try {\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('API request failed:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred',\n    };\n  }\n}\n\n// Authentication APIs\nexport const authApi = {\n  // Get current user profile\n  getMe: (): Promise<ApiResponse<User>> => \n    apiRequest<User>('/me'),\n\n  // Update user profile\n  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> =>\n    apiRequest<User>('/me', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Subject APIs\nexport const subjectApi = {\n  // Get all subjects for current user\n  getSubjects: (): Promise<ApiResponse<Subject[]>> =>\n    apiRequest<Subject[]>('/subjects'),\n\n  // Get subject by ID\n  getSubject: (id: string): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>(`/subjects/${id}`),\n\n  // Create new subject (teacher/admin only)\n  createSubject: (data: Omit<Subject, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>('/subjects', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update subject (teacher/admin only)\n  updateSubject: (id: string, data: Partial<Subject>): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>(`/subjects/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete subject (teacher/admin only)\n  deleteSubject: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/subjects/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Study Material APIs\nexport const materialApi = {\n  // Get materials for a subject\n  getMaterials: (subjectId: string): Promise<ApiResponse<StudyMaterial[]>> =>\n    apiRequest<StudyMaterial[]>(`/materials?subjectId=${subjectId}`),\n\n  // Upload new material\n  uploadMaterial: (data: FormData): Promise<ApiResponse<StudyMaterial>> =>\n    fetch(`${API_BASE_URL}/materials`, {\n      method: 'POST',\n      body: data,\n    }).then(res => res.json()),\n\n  // Delete material\n  deleteMaterial: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/materials/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Chat APIs\nexport const chatApi = {\n  // Send message and get streaming response\n  sendMessage: async function* (\n    message: string,\n    subjectId?: string\n  ): AsyncGenerator<StreamingResponse, void, unknown> {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ message, subjectId }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new Error('No response body');\n    }\n\n    const decoder = new TextDecoder();\n    let buffer = '';\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') {\n              return;\n            }\n            try {\n              const parsed: StreamingResponse = JSON.parse(data);\n              yield parsed;\n            } catch (e) {\n              console.error('Failed to parse SSE data:', e);\n            }\n          }\n        }\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  },\n\n  // Get chat history\n  getChatHistory: (subjectId?: string): Promise<ApiResponse<ChatMessage[]>> =>\n    apiRequest<ChatMessage[]>(`/chat/history${subjectId ? `?subjectId=${subjectId}` : ''}`),\n};\n\n// Text-to-Speech APIs\nexport const ttsApi = {\n  // Get TTS audio and visemes\n  getTTS: (messageId: string): Promise<ApiResponse<{ audioUrl: string; visemes: Viseme[] }>> =>\n    apiRequest<{ audioUrl: string; visemes: Viseme[] }>(`/tts?id=${messageId}`),\n};\n\n// Avatar APIs\nexport const avatarApi = {\n  // Get teacher avatar configuration\n  getAvatar: (teacherId?: string): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>(`/avatar${teacherId ? `?teacherId=${teacherId}` : ''}`),\n\n  // Save teacher avatar configuration\n  saveAvatar: (data: Omit<TeacherAvatar, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>('/avatar', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update avatar configuration\n  updateAvatar: (id: string, data: Partial<TeacherAvatar>): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>(`/avatar/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n};\n\n// S3 Upload APIs\nexport const s3Api = {\n  // Get signed URL for file upload\n  getSignedUrl: (fileName: string, fileType: string): Promise<ApiResponse<S3SignedUrl>> =>\n    apiRequest<S3SignedUrl>('/s3/sign', {\n      method: 'POST',\n      body: JSON.stringify({ fileName, fileType }),\n    }),\n\n  // Upload file to S3 using signed URL\n  uploadFile: async (file: File, signedUrl: S3SignedUrl): Promise<boolean> => {\n    try {\n      const formData = new FormData();\n      Object.entries(signedUrl.fields).forEach(([key, value]) => {\n        formData.append(key, value);\n      });\n      formData.append('file', file);\n\n      const response = await fetch(signedUrl.url, {\n        method: 'POST',\n        body: formData,\n      });\n\n      return response.ok;\n    } catch (error) {\n      console.error('S3 upload failed:', error);\n      return false;\n    }\n  },\n};\n\n// Announcement APIs\nexport const announcementApi = {\n  // Get announcements\n  getAnnouncements: (): Promise<ApiResponse<Announcement[]>> =>\n    apiRequest<Announcement[]>('/announcements'),\n\n  // Create announcement (HOD/Admin only)\n  createAnnouncement: (data: Omit<Announcement, 'id' | 'authorId' | 'authorName' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Announcement>> =>\n    apiRequest<Announcement>('/announcements', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete announcement\n  deleteAnnouncement: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/announcements/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Analytics APIs\nexport const analyticsApi = {\n  // Get analytics data (HOD/Admin only)\n  getAnalytics: (): Promise<ApiResponse<Analytics>> =>\n    apiRequest<Analytics>('/analytics'),\n\n  // Get user analytics\n  getUserAnalytics: (userId?: string): Promise<ApiResponse<Record<string, unknown>>> =>\n    apiRequest<Record<string, unknown>>(`/analytics/users${userId ? `/${userId}` : ''}`),\n};\n\n// User Management APIs (Admin only)\nexport const userApi = {\n  // Get all users\n  getUsers: async (page = 1, limit = 20): Promise<PaginatedResponse<User>> => {\n    const response = await apiRequest<User[]>(`/users?page=${page}&limit=${limit}`);\n    // Transform to paginated response format\n    return {\n      success: response.success,\n      data: response.data || [],\n      pagination: {\n        page,\n        limit,\n        total: response.data?.length || 0,\n        totalPages: Math.ceil((response.data?.length || 0) / limit)\n      }\n    };\n  },\n\n  // Create user\n  createUser: (data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<User>> =>\n    apiRequest<User>('/users', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update user\n  updateUser: (id: string, data: Partial<User>): Promise<ApiResponse<User>> =>\n    apiRequest<User>(`/users/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete user\n  deleteUser: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/users/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Mock data for development (remove when backend is ready)\nexport const mockData = {\n  user: {\n    id: '1',\n    name: 'John Doe',\n    email: '<EMAIL>',\n    role: 'student' as const,\n    orgId: 'org1',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  subjects: [\n    {\n      id: '1',\n      name: 'Japanese Language',\n      description: 'Learn Japanese with AI assistance',\n      teacherId: 'teacher1',\n      teacherName: 'Tanaka Sensei',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n    {\n      id: '2',\n      name: 'Mathematics',\n      description: 'Advanced mathematics concepts',\n      teacherId: 'teacher2',\n      teacherName: 'Smith Sensei',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n  ],\n  messages: [\n    {\n      id: '1',\n      content: 'Hello! How can I help you today?',\n      role: 'assistant' as const,\n      timestamp: new Date(),\n      subjectId: '1',\n    },\n    {\n      id: '2',\n      content: 'Can you explain the difference between は and が?',\n      role: 'user' as const,\n      timestamp: new Date(),\n      subjectId: '1',\n    },\n  ],\n};\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;AAiBlD,MAAM,eAAe,6DAAmC;AAExD,+BAA+B;AAC/B,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,UAAU,EAAE;YACzD,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,UAAU;IACrB,2BAA2B;IAC3B,OAAO,IACL,WAAiB;IAEnB,sBAAsB;IACtB,eAAe,CAAC,OACd,WAAiB,OAAO;YACtB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,aAAa;IACxB,oCAAoC;IACpC,aAAa,IACX,WAAsB;IAExB,oBAAoB;IACpB,YAAY,CAAC,KACX,WAAoB,CAAC,UAAU,EAAE,IAAI;IAEvC,0CAA0C;IAC1C,eAAe,CAAC,OACd,WAAoB,aAAa;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sCAAsC;IACtC,eAAe,CAAC,IAAY,OAC1B,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sCAAsC;IACtC,eAAe,CAAC,KACd,WAAiB,CAAC,UAAU,EAAE,IAAI,EAAE;YAClC,QAAQ;QACV;AACJ;AAGO,MAAM,cAAc;IACzB,8BAA8B;IAC9B,cAAc,CAAC,YACb,WAA4B,CAAC,qBAAqB,EAAE,WAAW;IAEjE,sBAAsB;IACtB,gBAAgB,CAAC,OACf,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACjC,QAAQ;YACR,MAAM;QACR,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;IAEzB,kBAAkB;IAClB,gBAAgB,CAAC,KACf,WAAiB,CAAC,WAAW,EAAE,IAAI,EAAE;YACnC,QAAQ;QACV;AACJ;AAGO,MAAM,UAAU;IACrB,0CAA0C;IAC1C,aAAa,gBACX,OAAe,EACf,SAAkB;QAElB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,KAAK,CAAC,EAAE;YACnD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAU;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAAU,IAAI;QACpB,IAAI,SAAS;QAEb,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,UAAU,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBAC/C,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,SAAS,MAAM,GAAG,MAAM;gBAExB,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI,SAAS,UAAU;4BACrB;wBACF;wBACA,IAAI;4BACF,MAAM,SAA4B,KAAK,KAAK,CAAC;4BAC7C,MAAM;wBACR,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,6BAA6B;wBAC7C;oBACF;gBACF;YACF;QACF,SAAU;YACR,OAAO,WAAW;QACpB;IACF;IAEA,mBAAmB;IACnB,gBAAgB,CAAC,YACf,WAA0B,CAAC,aAAa,EAAE,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI;AAC1F;AAGO,MAAM,SAAS;IACpB,4BAA4B;IAC5B,QAAQ,CAAC,YACP,WAAoD,CAAC,QAAQ,EAAE,WAAW;AAC9E;AAGO,MAAM,YAAY;IACvB,mCAAmC;IACnC,WAAW,CAAC,YACV,WAA0B,CAAC,OAAO,EAAE,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI;IAElF,oCAAoC;IACpC,YAAY,CAAC,OACX,WAA0B,WAAW;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,8BAA8B;IAC9B,cAAc,CAAC,IAAY,OACzB,WAA0B,CAAC,QAAQ,EAAE,IAAI,EAAE;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,QAAQ;IACnB,iCAAiC;IACjC,cAAc,CAAC,UAAkB,WAC/B,WAAwB,YAAY;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAU;YAAS;QAC5C;IAEF,qCAAqC;IACrC,YAAY,OAAO,MAAY;QAC7B,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,OAAO,OAAO,CAAC,UAAU,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBACpD,SAAS,MAAM,CAAC,KAAK;YACvB;YACA,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,UAAU,GAAG,EAAE;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;QACT;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,oBAAoB;IACpB,kBAAkB,IAChB,WAA2B;IAE7B,uCAAuC;IACvC,oBAAoB,CAAC,OACnB,WAAyB,kBAAkB;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sBAAsB;IACtB,oBAAoB,CAAC,KACnB,WAAiB,CAAC,eAAe,EAAE,IAAI,EAAE;YACvC,QAAQ;QACV;AACJ;AAGO,MAAM,eAAe;IAC1B,sCAAsC;IACtC,cAAc,IACZ,WAAsB;IAExB,qBAAqB;IACrB,kBAAkB,CAAC,SACjB,WAAoC,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,IAAI;AACvF;AAGO,MAAM,UAAU;IACrB,gBAAgB;IAChB,UAAU,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE;QACnC,MAAM,WAAW,MAAM,WAAmB,CAAC,YAAY,EAAE,KAAK,OAAO,EAAE,OAAO;QAC9E,yCAAyC;QACzC,OAAO;YACL,SAAS,SAAS,OAAO;YACzB,MAAM,SAAS,IAAI,IAAI,EAAE;YACzB,YAAY;gBACV;gBACA;gBACA,OAAO,SAAS,IAAI,EAAE,UAAU;gBAChC,YAAY,KAAK,IAAI,CAAC,CAAC,SAAS,IAAI,EAAE,UAAU,CAAC,IAAI;YACvD;QACF;IACF;IAEA,cAAc;IACd,YAAY,CAAC,OACX,WAAiB,UAAU;YACzB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,cAAc;IACd,YAAY,CAAC,IAAY,OACvB,WAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,cAAc;IACd,YAAY,CAAC,KACX,WAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;YAC/B,QAAQ;QACV;AACJ;AAGO,MAAM,WAAW;IACtB,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW;YACX,aAAa;YACb,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW;YACX,aAAa;YACb,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;KACD;IACD,UAAU;QACR;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;KACD;AACH", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/store/useUser.ts"], "sourcesContent": ["// Zustand store for User and App state management\n\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { useCallback } from 'react';\nimport { User, Subject, StudyMaterial, Announcement, Analytics, UserStore } from '@/types';\nimport { mockData } from '@/lib/api';\n\nexport const useUser = create<UserStore>()(\n  devtools(\n    persist(\n      (set) => ({\n        // State\n        user: undefined,\n        subjects: [],\n        materials: [],\n        announcements: [],\n        analytics: undefined,\n\n        // Actions\n        setUser: (user: User | undefined) => {\n          set({ user }, false, 'setUser');\n        },\n\n        setSubjects: (subjects: Subject[]) => {\n          set({ subjects }, false, 'setSubjects');\n        },\n\n        setMaterials: (materials: StudyMaterial[]) => {\n          set({ materials }, false, 'setMaterials');\n        },\n\n        setAnnouncements: (announcements: Announcement[]) => {\n          set({ announcements }, false, 'setAnnouncements');\n        },\n\n        setAnalytics: (analytics: Analytics) => {\n          set({ analytics }, false, 'setAnalytics');\n        },\n      }),\n      {\n        name: 'user-store',\n        partialize: (state) => ({\n          user: state.user,\n          subjects: state.subjects,\n          announcements: state.announcements,\n        }),\n      }\n    ),\n    {\n      name: 'user-store',\n    }\n  )\n);\n\n// Selectors for optimized re-renders\nexport const useCurrentUser = () => useUser((state) => state.user);\nexport const useSubjects = () => useUser((state) => state.subjects);\nexport const useMaterials = () => useUser((state) => state.materials);\nexport const useAnnouncements = () => useUser((state) => state.announcements);\nexport const useAnalytics = () => useUser((state) => state.analytics);\n\n// Actions selectors\nexport const useUserActions = () => useUser((state) => ({\n  setUser: state.setUser,\n  setSubjects: state.setSubjects,\n  setMaterials: state.setMaterials,\n  setAnnouncements: state.setAnnouncements,\n  setAnalytics: state.setAnalytics,\n}));\n\n// Authentication helpers\nexport const useAuth = () => {\n  const { user, setUser } = useUser();\n\n  const login = async (email: string, password: string) => {\n    try {\n      // TODO: Implement actual login when NextAuth is configured\n      console.log('Logging in:', email, 'with password length:', password.length);\n\n      // For now, use mock data\n      setUser(mockData.user);\n      return { success: true };\n    } catch (error) {\n      console.error('Login failed:', error);\n      return { success: false, error: 'Login failed' };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      // TODO: Implement actual logout when NextAuth is configured\n      setUser(undefined);\n      // Clear other stores if needed\n      return { success: true };\n    } catch (error) {\n      console.error('Logout failed:', error);\n      return { success: false, error: 'Logout failed' };\n    }\n  };\n\n  const loadUserProfile = async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await authApi.getMe();\n      console.log('Loading user profile');\n      \n      // For now, use mock data\n      setUser(mockData.user);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load user profile:', error);\n      return { success: false, error: 'Failed to load profile' };\n    }\n  };\n\n  return {\n    user,\n    login,\n    logout,\n    loadUserProfile,\n    isAuthenticated: !!user,\n    isStudent: user?.role === 'student',\n    isTeacher: user?.role === 'teacher',\n    isHOD: user?.role === 'hod',\n    isAdmin: user?.role === 'admin',\n  };\n};\n\n// Data loading helpers\nexport const useDataLoaders = () => {\n  const { user } = useAuth();\n\n  const loadSubjects = useCallback(async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await subjectApi.getSubjects();\n      console.log('Loading subjects');\n\n      // For now, use mock data\n      useUser.getState().setSubjects(mockData.subjects);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load subjects:', error);\n      return { success: false, error: 'Failed to load subjects' };\n    }\n  }, []); // Using getState() to avoid dependency issues\n\n  const loadMaterials = useCallback(async (subjectId?: string) => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await materialApi.getMaterials(subjectId);\n      console.log('Loading materials for subject:', subjectId);\n\n      // For now, use empty array\n      useUser.getState().setMaterials([]);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load materials:', error);\n      return { success: false, error: 'Failed to load materials' };\n    }\n  }, []); // Using getState() to avoid dependency issues\n\n  const loadAnnouncements = useCallback(async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await announcementApi.getAnnouncements();\n      console.log('Loading announcements');\n\n      // For now, use empty array\n      useUser.getState().setAnnouncements([]);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load announcements:', error);\n      return { success: false, error: 'Failed to load announcements' };\n    }\n  }, []); // Using getState() to avoid dependency issues\n\n  const loadAnalytics = useCallback(async () => {\n    if (!user || (user.role !== 'hod' && user.role !== 'admin')) {\n      return { success: false, error: 'Unauthorized' };\n    }\n\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await analyticsApi.getAnalytics();\n      console.log('Loading analytics');\n\n      // For now, use mock analytics data\n      const mockAnalytics: Analytics = {\n        totalUsers: 150,\n        totalStudents: 120,\n        totalTeachers: 25,\n        totalSubjects: 15,\n        totalMaterials: 85,\n        totalChats: 1250,\n        activeUsersToday: 45,\n        activeUsersThisWeek: 98,\n        popularSubjects: [\n          { subjectId: '1', subjectName: 'Japanese Language', chatCount: 450 },\n          { subjectId: '2', subjectName: 'Mathematics', chatCount: 320 },\n        ],\n        userGrowth: [\n          { date: '2024-01-01', count: 100 },\n          { date: '2024-02-01', count: 120 },\n          { date: '2024-03-01', count: 150 },\n        ],\n      };\n\n      useUser.getState().setAnalytics(mockAnalytics);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load analytics:', error);\n      return { success: false, error: 'Failed to load analytics' };\n    }\n  }, [user]); // Include user dependency for role check\n\n  const loadAllData = useCallback(async () => {\n    const results = await Promise.allSettled([\n      loadSubjects(),\n      loadAnnouncements(),\n      ...(user?.role === 'hod' || user?.role === 'admin' ? [loadAnalytics()] : []),\n    ]);\n\n    const failures = results.filter(result => result.status === 'rejected');\n    if (failures.length > 0) {\n      console.error('Some data failed to load:', failures);\n    }\n\n    return { success: failures.length === 0 };\n  }, [loadSubjects, loadAnnouncements, loadAnalytics, user?.role]);\n\n  return {\n    loadSubjects,\n    loadMaterials,\n    loadAnnouncements,\n    loadAnalytics,\n    loadAllData,\n  };\n};\n\n// Subject helpers\nexport const useSubjectHelpers = () => {\n  const subjects = useSubjects();\n  const { user } = useAuth();\n\n  const getSubjectById = (id: string) => {\n    return subjects.find(subject => subject.id === id);\n  };\n\n  const getSubjectsByTeacher = (teacherId: string) => {\n    return subjects.filter(subject => subject.teacherId === teacherId);\n  };\n\n  const getUserSubjects = () => {\n    if (!user) return [];\n    \n    if (user.role === 'student') {\n      // Students see all subjects in their org\n      return subjects.filter(subject => subject.orgId === user.orgId);\n    } else if (user.role === 'teacher') {\n      // Teachers see only their subjects\n      return subjects.filter(subject => subject.teacherId === user.id);\n    } else {\n      // HOD and Admin see all subjects in their org\n      return subjects.filter(subject => subject.orgId === user.orgId);\n    }\n  };\n\n  return {\n    getSubjectById,\n    getSubjectsByTeacher,\n    getUserSubjects,\n  };\n};\n\n// Role-based permissions\nexport const usePermissions = () => {\n  const { user } = useAuth();\n\n  const canCreateSubject = () => {\n    return user?.role === 'teacher' || user?.role === 'admin';\n  };\n\n  const canEditSubject = (subjectId: string) => {\n    if (!user) return false;\n    if (user.role === 'admin') return true;\n    \n    const subject = useUser.getState().subjects.find(s => s.id === subjectId);\n    return user.role === 'teacher' && subject?.teacherId === user.id;\n  };\n\n  const canUploadMaterial = (subjectId: string) => {\n    return canEditSubject(subjectId);\n  };\n\n  const canCreateAnnouncement = () => {\n    return user?.role === 'hod' || user?.role === 'admin';\n  };\n\n  const canViewAnalytics = () => {\n    return user?.role === 'hod' || user?.role === 'admin';\n  };\n\n  const canManageUsers = () => {\n    return user?.role === 'admin';\n  };\n\n  return {\n    canCreateSubject,\n    canEditSubject,\n    canUploadMaterial,\n    canCreateAnnouncement,\n    canViewAnalytics,\n    canManageUsers,\n  };\n};"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;AAElD;AACA;AACA;AAEA;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,QAAQ;QACR,MAAM;QACN,UAAU,EAAE;QACZ,WAAW,EAAE;QACb,eAAe,EAAE;QACjB,WAAW;QAEX,UAAU;QACV,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK,GAAG,OAAO;QACvB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS,GAAG,OAAO;QAC3B;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;YAAc,GAAG,OAAO;QAChC;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,UAAU,MAAM,QAAQ;YACxB,eAAe,MAAM,aAAa;QACpC,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,iBAAiB,IAAM,QAAQ,CAAC,QAAU,MAAM,IAAI;AAC1D,MAAM,cAAc,IAAM,QAAQ,CAAC,QAAU,MAAM,QAAQ;AAC3D,MAAM,eAAe,IAAM,QAAQ,CAAC,QAAU,MAAM,SAAS;AAC7D,MAAM,mBAAmB,IAAM,QAAQ,CAAC,QAAU,MAAM,aAAa;AACrE,MAAM,eAAe,IAAM,QAAQ,CAAC,QAAU,MAAM,SAAS;AAG7D,MAAM,iBAAiB,IAAM,QAAQ,CAAC,QAAU,CAAC;YACtD,SAAS,MAAM,OAAO;YACtB,aAAa,MAAM,WAAW;YAC9B,cAAc,MAAM,YAAY;YAChC,kBAAkB,MAAM,gBAAgB;YACxC,cAAc,MAAM,YAAY;QAClC,CAAC;AAGM,MAAM,UAAU;IACrB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAE1B,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,2DAA2D;YAC3D,QAAQ,GAAG,CAAC,eAAe,OAAO,yBAAyB,SAAS,MAAM;YAE1E,yBAAyB;YACzB,QAAQ,iHAAA,CAAA,WAAQ,CAAC,IAAI;YACrB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,4DAA4D;YAC5D,QAAQ;YACR,+BAA+B;YAC/B,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgB;QAClD;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,wDAAwD;YACxD,0CAA0C;YAC1C,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,QAAQ,iHAAA,CAAA,WAAQ,CAAC,IAAI;YACrB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB,WAAW,MAAM,SAAS;QAC1B,WAAW,MAAM,SAAS;QAC1B,OAAO,MAAM,SAAS;QACtB,SAAS,MAAM,SAAS;IAC1B;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI;YACF,wDAAwD;YACxD,mDAAmD;YACnD,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,QAAQ,QAAQ,GAAG,WAAW,CAAC,iHAAA,CAAA,WAAQ,CAAC,QAAQ;YAChD,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;IACF,GAAG,EAAE,GAAG,8CAA8C;IAEtD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,IAAI;YACF,wDAAwD;YACxD,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,2BAA2B;YAC3B,QAAQ,QAAQ,GAAG,YAAY,CAAC,EAAE;YAClC,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA2B;QAC7D;IACF,GAAG,EAAE,GAAG,8CAA8C;IAEtD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI;YACF,wDAAwD;YACxD,6DAA6D;YAC7D,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B,QAAQ,QAAQ,GAAG,gBAAgB,CAAC,EAAE;YACtC,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA+B;QACjE;IACF,GAAG,EAAE,GAAG,8CAA8C;IAEtD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,QAAS,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAU;YAC3D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;QAEA,IAAI;YACF,wDAAwD;YACxD,sDAAsD;YACtD,QAAQ,GAAG,CAAC;YAEZ,mCAAmC;YACnC,MAAM,gBAA2B;gBAC/B,YAAY;gBACZ,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,gBAAgB;gBAChB,YAAY;gBACZ,kBAAkB;gBAClB,qBAAqB;gBACrB,iBAAiB;oBACf;wBAAE,WAAW;wBAAK,aAAa;wBAAqB,WAAW;oBAAI;oBACnE;wBAAE,WAAW;wBAAK,aAAa;wBAAe,WAAW;oBAAI;iBAC9D;gBACD,YAAY;oBACV;wBAAE,MAAM;wBAAc,OAAO;oBAAI;oBACjC;wBAAE,MAAM;wBAAc,OAAO;oBAAI;oBACjC;wBAAE,MAAM;wBAAc,OAAO;oBAAI;iBAClC;YACH;YAEA,QAAQ,QAAQ,GAAG,YAAY,CAAC;YAChC,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA2B;QAC7D;IACF,GAAG;QAAC;KAAK,GAAG,yCAAyC;IAErD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,MAAM,UAAU,MAAM,QAAQ,UAAU,CAAC;YACvC;YACA;eACI,MAAM,SAAS,SAAS,MAAM,SAAS,UAAU;gBAAC;aAAgB,GAAG,EAAE;SAC5E;QAED,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;QAC5D,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;QAEA,OAAO;YAAE,SAAS,SAAS,MAAM,KAAK;QAAE;IAC1C,GAAG;QAAC;QAAc;QAAmB;QAAe,MAAM;KAAK;IAE/D,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,WAAW;IACjB,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK;IAC1D;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,yCAAyC;YACzC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;QAChE,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;YAClC,mCAAmC;YACnC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE;QACjE,OAAO;YACL,8CAA8C;YAC9C,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;QAChE;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,mBAAmB;QACvB,OAAO,MAAM,SAAS,aAAa,MAAM,SAAS;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,KAAK,IAAI,KAAK,SAAS,OAAO;QAElC,MAAM,UAAU,QAAQ,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/D,OAAO,KAAK,IAAI,KAAK,aAAa,SAAS,cAAc,KAAK,EAAE;IAClE;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,eAAe;IACxB;IAEA,MAAM,wBAAwB;QAC5B,OAAO,MAAM,SAAS,SAAS,MAAM,SAAS;IAChD;IAEA,MAAM,mBAAmB;QACvB,OAAO,MAAM,SAAS,SAAS,MAAM,SAAS;IAChD;IAEA,MAAM,iBAAiB;QACrB,OAAO,MAAM,SAAS;IACxB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/%28dash%29/teacher/page.tsx"], "sourcesContent": ["'use client';\n\n// Teacher Dashboard - Manage subjects, materials, and AI avatar\n\nimport { useEffect, useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  BookOpen, \n  Users, \n  Upload, \n  Settings,\n  MessageSquare,\n  FileText,\n  Bot,\n  Plus,\n  Eye,\n  Edit\n} from 'lucide-react';\nimport { useSubjects, useDataLoaders } from '@/store/useUser';\nimport { mockData } from '@/lib/api';\n\nexport default function TeacherDashboard() {\n  const { data: session } = useSession();\n  const router = useRouter();\n  const subjects = useSubjects();\n  const { loadSubjects } = useDataLoaders();\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const loadData = async () => {\n      await loadSubjects();\n      setIsLoading(false);\n    };\n    loadData();\n  }, [loadSubjects]);\n\n  const handleCreateSubject = () => {\n    router.push('/teacher/subjects/create');\n  };\n\n  const handleEditSubject = (subjectId: string) => {\n    router.push(`/teacher/subjects/${subjectId}/edit`);\n  };\n\n  const handleManageMaterials = (subjectId: string) => {\n    router.push(`/teacher/subjects/${subjectId}/materials`);\n  };\n\n  const handleSetupAvatar = () => {\n    router.push('/teacher/avatar');\n  };\n\n  const handleViewChats = (subjectId: string) => {\n    router.push(`/teacher/subjects/${subjectId}/chats`);\n  };\n\n  // Get teacher's subjects\n  const teacherSubjects = subjects.filter(subject => \n    subject.teacherId === session?.user?.id\n  );\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading your dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"bg-gradient-to-r from-green-600 to-teal-600 rounded-lg p-6 text-white\">\n        <h1 className=\"text-2xl font-bold mb-2\">\n          Welcome, {session?.user?.name}!\n        </h1>\n        <p className=\"text-green-100\">\n          Manage your subjects, upload materials, and customize your AI avatar to enhance student learning.\n        </p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <BookOpen className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">My Subjects</p>\n                <p className=\"text-2xl font-bold\">{teacherSubjects.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <Users className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Students</p>\n                <p className=\"text-2xl font-bold\">48</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <FileText className=\"h-5 w-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Materials</p>\n                <p className=\"text-2xl font-bold\">23</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-orange-100 rounded-lg\">\n                <MessageSquare className=\"h-5 w-5 text-orange-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">AI Interactions</p>\n                <p className=\"text-2xl font-bold\">156</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Quick Actions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Quick Actions</CardTitle>\n          <CardDescription>Common tasks to manage your teaching</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <Button onClick={handleCreateSubject} className=\"h-20 flex-col space-y-2\">\n              <Plus className=\"h-6 w-6\" />\n              <span>Create Subject</span>\n            </Button>\n            <Button onClick={handleSetupAvatar} variant=\"outline\" className=\"h-20 flex-col space-y-2\">\n              <Bot className=\"h-6 w-6\" />\n              <span>Setup AI Avatar</span>\n            </Button>\n            <Button onClick={() => router.push('/teacher/materials/upload')} variant=\"outline\" className=\"h-20 flex-col space-y-2\">\n              <Upload className=\"h-6 w-6\" />\n              <span>Upload Materials</span>\n            </Button>\n            <Button onClick={() => router.push('/teacher/analytics')} variant=\"outline\" className=\"h-20 flex-col space-y-2\">\n              <Eye className=\"h-6 w-6\" />\n              <span>View Analytics</span>\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* My Subjects */}\n      <div>\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-semibold\">My Subjects</h2>\n          <Button onClick={handleCreateSubject}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Create New Subject\n          </Button>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {(teacherSubjects.length > 0 ? teacherSubjects : mockData.subjects).map((subject) => (\n            <Card key={subject.id} className=\"hover:shadow-md transition-shadow\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <span className=\"text-lg\">{subject.name}</span>\n                  <Badge variant=\"secondary\">Active</Badge>\n                </CardTitle>\n                <CardDescription>\n                  {subject.description || 'No description provided'}\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">Students enrolled:</span>\n                    <span className=\"font-medium\">24</span>\n                  </div>\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">Materials:</span>\n                    <span className=\"font-medium\">8</span>\n                  </div>\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span className=\"text-gray-600\">AI Chats:</span>\n                    <span className=\"font-medium\">67</span>\n                  </div>\n                  \n                  <div className=\"pt-2 space-y-2\">\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <Button \n                        variant=\"outline\" \n                        size=\"sm\"\n                        onClick={() => handleEditSubject(subject.id)}\n                      >\n                        <Edit className=\"h-4 w-4 mr-1\" />\n                        Edit\n                      </Button>\n                      <Button \n                        variant=\"outline\" \n                        size=\"sm\"\n                        onClick={() => handleViewChats(subject.id)}\n                      >\n                        <MessageSquare className=\"h-4 w-4 mr-1\" />\n                        Chats\n                      </Button>\n                    </div>\n                    <Button \n                      onClick={() => handleManageMaterials(subject.id)}\n                      className=\"w-full\"\n                      size=\"sm\"\n                    >\n                      <FileText className=\"h-4 w-4 mr-2\" />\n                      Manage Materials\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n\n      {/* AI Avatar Status */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Bot className=\"h-5 w-5\" />\n            <span>AI Avatar Configuration</span>\n          </CardTitle>\n          <CardDescription>\n            Customize your AI teaching assistant&apos;s appearance and personality\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                <Bot className=\"h-6 w-6 text-white\" />\n              </div>\n              <div>\n                <p className=\"font-medium\">Default Avatar</p>\n                <p className=\"text-sm text-gray-600\">Professional, Friendly tone</p>\n              </div>\n            </div>\n            <div className=\"flex space-x-2\">\n              <Button variant=\"outline\" onClick={handleSetupAvatar}>\n                <Settings className=\"h-4 w-4 mr-2\" />\n                Customize\n              </Button>\n              <Button onClick={() => router.push('/teacher/avatar/preview')}>\n                <Eye className=\"h-4 w-4 mr-2\" />\n                Preview\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Recent Activity */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Activity</CardTitle>\n          <CardDescription>Latest interactions and updates</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <MessageSquare className=\"h-5 w-5 text-blue-600\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">Student asked about grammar patterns</p>\n                <p className=\"text-sm text-gray-600\">Japanese Language - 30 minutes ago</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <Upload className=\"h-5 w-5 text-green-600\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">New material uploaded</p>\n                <p className=\"text-sm text-gray-600\">Chapter 6 exercises - 2 hours ago</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <Users className=\"h-5 w-5 text-purple-600\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">3 new students enrolled</p>\n                <p className=\"text-sm text-gray-600\">Mathematics course - 1 day ago</p>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,gEAAgE;AAEhE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAvBA;;;;;;;;;;;AAyBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,MAAM;YACN,aAAa;QACf;QACA;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,IAAI,CAAC,CAAC,kBAAkB,EAAE,UAAU,KAAK,CAAC;IACnD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,IAAI,CAAC,CAAC,kBAAkB,EAAE,UAAU,UAAU,CAAC;IACxD;IAEA,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,CAAC,CAAC,kBAAkB,EAAE,UAAU,MAAM,CAAC;IACpD;IAEA,yBAAyB;IACzB,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,UACtC,QAAQ,SAAS,KAAK,SAAS,MAAM;IAGvC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAA0B;4BAC5B,SAAS,MAAM;4BAAK;;;;;;;kCAEhC,8OAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAsB,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMjE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAqB,WAAU;;sDAC9C,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAmB,SAAQ;oCAAU,WAAU;;sDAC9D,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAA8B,SAAQ;oCAAU,WAAU;;sDAC3F,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAAuB,SAAQ;oCAAU,WAAU;;sDACpF,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAKrC,8OAAC;wBAAI,WAAU;kCACZ,CAAC,gBAAgB,MAAM,GAAG,IAAI,kBAAkB,iHAAA,CAAA,WAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,wBACvE,8OAAC,gIAAA,CAAA,OAAI;gCAAkB,WAAU;;kDAC/B,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAK,WAAU;kEAAW,QAAQ,IAAI;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;;;;;;;0DAE7B,8OAAC,gIAAA,CAAA,kBAAe;0DACb,QAAQ,WAAW,IAAI;;;;;;;;;;;;kDAG5B,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAGhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,kBAAkB,QAAQ,EAAE;;sFAE3C,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGnC,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,gBAAgB,QAAQ,EAAE;;sFAEzC,8OAAC,wNAAA,CAAA,gBAAa;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;sEAI9C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,IAAM,sBAAsB,QAAQ,EAAE;4DAC/C,WAAU;4DACV,MAAK;;8EAEL,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;+BAjDpC,QAAQ,EAAE;;;;;;;;;;;;;;;;0BA6D3B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;8DACjC,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}]}