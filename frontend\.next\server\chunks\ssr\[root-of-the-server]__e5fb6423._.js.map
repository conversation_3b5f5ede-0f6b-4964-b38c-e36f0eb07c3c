{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/slider.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,qMAAA,CAAA,aAAgB,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,qMAAA,CAAA,aAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/store/useAITeacher.ts"], "sourcesContent": ["// Zustand store for AI Teacher Avatar state management\n\nimport { create } from 'zustand';\nimport { devtools } from 'zustand/middleware';\nimport { TeacherAvatar, Viseme, AITeacherStore } from '@/types';\n\nexport const useAITeacher = create<AITeacherStore>()(\n  devtools(\n    (set) => ({\n      // State\n      avatar: undefined,\n      isModelLoaded: false,\n      isSpeaking: false,\n      currentVisemes: [],\n      blackboardText: '',\n\n      // Actions\n      setAvatar: (avatar: TeacherAvatar) => {\n        set({ avatar }, false, 'setAvatar');\n      },\n\n      setModelLoaded: (loaded: boolean) => {\n        set({ isModelLoaded: loaded }, false, 'setModelLoaded');\n      },\n\n      setSpeaking: (speaking: boolean) => {\n        set({ isSpeaking: speaking }, false, 'setSpeaking');\n        \n        // Clear visemes when not speaking\n        if (!speaking) {\n          set({ currentVisemes: [] }, false, 'clearVisemes');\n        }\n      },\n\n      setCurrentVisemes: (visemes: Viseme[]) => {\n        set({ currentVisemes: visemes }, false, 'setCurrentVisemes');\n      },\n\n      setBlackboardText: (text: string) => {\n        set({ blackboardText: text }, false, 'setBlackboardText');\n      },\n    }),\n    {\n      name: 'ai-teacher-store',\n    }\n  )\n);\n\n// Selectors for optimized re-renders\nexport const useAvatarConfig = () => useAITeacher((state) => state.avatar);\nexport const useModelLoaded = () => useAITeacher((state) => state.isModelLoaded);\nexport const useIsSpeaking = () => useAITeacher((state) => state.isSpeaking);\nexport const useCurrentVisemes = () => useAITeacher((state) => state.currentVisemes);\nexport const useBlackboardText = () => useAITeacher((state) => state.blackboardText);\n\n// Actions selectors\nexport const useAITeacherActions = () => useAITeacher((state) => ({\n  setAvatar: state.setAvatar,\n  setModelLoaded: state.setModelLoaded,\n  setSpeaking: state.setSpeaking,\n  setCurrentVisemes: state.setCurrentVisemes,\n  setBlackboardText: state.setBlackboardText,\n}));\n\n// Helper functions for avatar management\nexport const useAvatarHelpers = () => {\n  const { avatar, setAvatar } = useAITeacher();\n\n  const updateAvatarAppearance = (appearance: Partial<TeacherAvatar['appearance']>) => {\n    if (avatar) {\n      setAvatar({\n        ...avatar,\n        appearance: { ...avatar.appearance, ...appearance },\n        updatedAt: new Date(),\n      });\n    }\n  };\n\n  const updateVoiceSettings = (voiceSettings: Partial<TeacherAvatar['voiceSettings']>) => {\n    if (avatar) {\n      setAvatar({\n        ...avatar,\n        voiceSettings: { ...avatar.voiceSettings, ...voiceSettings },\n        updatedAt: new Date(),\n      });\n    }\n  };\n\n  const updatePersonality = (personality: Partial<TeacherAvatar['personality']>) => {\n    if (avatar) {\n      setAvatar({\n        ...avatar,\n        personality: { ...avatar.personality, ...personality },\n        updatedAt: new Date(),\n      });\n    }\n  };\n\n  return {\n    updateAvatarAppearance,\n    updateVoiceSettings,\n    updatePersonality,\n  };\n};\n\n// Animation helpers for lip-sync\nexport const useVisemeAnimation = () => {\n  const { currentVisemes, setCurrentVisemes, setSpeaking } = useAITeacher();\n\n  const playVisemeSequence = (visemes: Viseme[], audioElement?: HTMLAudioElement) => {\n    if (visemes.length === 0) return;\n\n    setSpeaking(true);\n    setCurrentVisemes(visemes);\n\n    // If audio element is provided, sync with audio playback\n    if (audioElement) {\n      const handleAudioEnd = () => {\n        setSpeaking(false);\n        setCurrentVisemes([]);\n        audioElement.removeEventListener('ended', handleAudioEnd);\n      };\n\n      audioElement.addEventListener('ended', handleAudioEnd);\n      audioElement.play().catch(console.error);\n    } else {\n      // Fallback: estimate duration based on viseme timestamps\n      const duration = visemes[visemes.length - 1]?.time || 3000;\n      setTimeout(() => {\n        setSpeaking(false);\n        setCurrentVisemes([]);\n      }, duration);\n    }\n  };\n\n  const stopVisemeSequence = () => {\n    setSpeaking(false);\n    setCurrentVisemes([]);\n  };\n\n  return {\n    playVisemeSequence,\n    stopVisemeSequence,\n    currentVisemes,\n  };\n};\n\n// Default avatar configuration\nexport const defaultAvatarConfig: Omit<TeacherAvatar, 'id' | 'teacherId' | 'createdAt' | 'updatedAt'> = {\n  modelUrl: '/models/teacher-default.glb',\n  voiceId: 'default-voice',\n  voiceSettings: {\n    pitch: 1.0,\n    speed: 1.0,\n    volume: 0.8,\n  },\n  appearance: {\n    skinTone: 'medium',\n    hairColor: 'brown',\n    eyeColor: 'brown',\n    clothing: 'professional',\n  },\n  personality: {\n    tone: 'friendly',\n    enthusiasm: 7,\n    patience: 8,\n  },\n};\n\n// Persistence helpers (for saving to backend)\nexport const useAvatarPersistence = () => {\n  const { avatar } = useAITeacher();\n\n  const saveAvatarToBackend = async () => {\n    if (!avatar) return false;\n\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await avatarApi.saveAvatar(avatar);\n      console.log('Saving avatar configuration:', avatar);\n      return true;\n    } catch (error) {\n      console.error('Failed to save avatar:', error);\n      return false;\n    }\n  };\n\n  const loadAvatarFromBackend = async (teacherId?: string) => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await avatarApi.getAvatar(teacherId);\n      console.log('Loading avatar configuration for teacher:', teacherId);\n      \n      // For now, return default config\n      return defaultAvatarConfig;\n    } catch (error) {\n      console.error('Failed to load avatar:', error);\n      return null;\n    }\n  };\n\n  return {\n    saveAvatarToBackend,\n    loadAvatarFromBackend,\n  };\n};\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;;;;;;;;;;AAEvD;AACA;;;AAGO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAC,MAAQ,CAAC;QACR,QAAQ;QACR,QAAQ;QACR,eAAe;QACf,YAAY;QACZ,gBAAgB,EAAE;QAClB,gBAAgB;QAEhB,UAAU;QACV,WAAW,CAAC;YACV,IAAI;gBAAE;YAAO,GAAG,OAAO;QACzB;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE,eAAe;YAAO,GAAG,OAAO;QACxC;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE,YAAY;YAAS,GAAG,OAAO;YAErC,kCAAkC;YAClC,IAAI,CAAC,UAAU;gBACb,IAAI;oBAAE,gBAAgB,EAAE;gBAAC,GAAG,OAAO;YACrC;QACF;QAEA,mBAAmB,CAAC;YAClB,IAAI;gBAAE,gBAAgB;YAAQ,GAAG,OAAO;QAC1C;QAEA,mBAAmB,CAAC;YAClB,IAAI;gBAAE,gBAAgB;YAAK,GAAG,OAAO;QACvC;IACF,CAAC,GACD;IACE,MAAM;AACR;AAKG,MAAM,kBAAkB,IAAM,aAAa,CAAC,QAAU,MAAM,MAAM;AAClE,MAAM,iBAAiB,IAAM,aAAa,CAAC,QAAU,MAAM,aAAa;AACxE,MAAM,gBAAgB,IAAM,aAAa,CAAC,QAAU,MAAM,UAAU;AACpE,MAAM,oBAAoB,IAAM,aAAa,CAAC,QAAU,MAAM,cAAc;AAC5E,MAAM,oBAAoB,IAAM,aAAa,CAAC,QAAU,MAAM,cAAc;AAG5E,MAAM,sBAAsB,IAAM,aAAa,CAAC,QAAU,CAAC;YAChE,WAAW,MAAM,SAAS;YAC1B,gBAAgB,MAAM,cAAc;YACpC,aAAa,MAAM,WAAW;YAC9B,mBAAmB,MAAM,iBAAiB;YAC1C,mBAAmB,MAAM,iBAAiB;QAC5C,CAAC;AAGM,MAAM,mBAAmB;IAC9B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IAE9B,MAAM,yBAAyB,CAAC;QAC9B,IAAI,QAAQ;YACV,UAAU;gBACR,GAAG,MAAM;gBACT,YAAY;oBAAE,GAAG,OAAO,UAAU;oBAAE,GAAG,UAAU;gBAAC;gBAClD,WAAW,IAAI;YACjB;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,QAAQ;YACV,UAAU;gBACR,GAAG,MAAM;gBACT,eAAe;oBAAE,GAAG,OAAO,aAAa;oBAAE,GAAG,aAAa;gBAAC;gBAC3D,WAAW,IAAI;YACjB;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,QAAQ;YACV,UAAU;gBACR,GAAG,MAAM;gBACT,aAAa;oBAAE,GAAG,OAAO,WAAW;oBAAE,GAAG,WAAW;gBAAC;gBACrD,WAAW,IAAI;YACjB;QACF;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAGO,MAAM,qBAAqB;IAChC,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG;IAE3D,MAAM,qBAAqB,CAAC,SAAmB;QAC7C,IAAI,QAAQ,MAAM,KAAK,GAAG;QAE1B,YAAY;QACZ,kBAAkB;QAElB,yDAAyD;QACzD,IAAI,cAAc;YAChB,MAAM,iBAAiB;gBACrB,YAAY;gBACZ,kBAAkB,EAAE;gBACpB,aAAa,mBAAmB,CAAC,SAAS;YAC5C;YAEA,aAAa,gBAAgB,CAAC,SAAS;YACvC,aAAa,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;QACzC,OAAO;YACL,yDAAyD;YACzD,MAAM,WAAW,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,EAAE,QAAQ;YACtD,WAAW;gBACT,YAAY;gBACZ,kBAAkB,EAAE;YACtB,GAAG;QACL;IACF;IAEA,MAAM,qBAAqB;QACzB,YAAY;QACZ,kBAAkB,EAAE;IACtB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAGO,MAAM,sBAA2F;IACtG,UAAU;IACV,SAAS;IACT,eAAe;QACb,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA,YAAY;QACV,UAAU;QACV,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA,aAAa;QACX,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;AACF;AAGO,MAAM,uBAAuB;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,QAAQ,OAAO;QAEpB,IAAI;YACF,wDAAwD;YACxD,uDAAuD;YACvD,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,wDAAwD;YACxD,yDAAyD;YACzD,QAAQ,GAAG,CAAC,6CAA6C;YAEzD,iCAAiC;YACjC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/3d/TeacherModel.tsx"], "sourcesContent": ["'use client';\n\n// TeacherModel component with lip-sync animation and customizable appearance\n\nimport { useRef, useEffect, useMemo } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { useGLTF, useAnimations } from '@react-three/drei';\nimport * as THREE from 'three';\nimport { \n  useAvatarConfig, \n  useIsSpeaking, \n  useCurrentVisemes,\n  useAITeacherActions \n} from '@/store/useAITeacher';\n\ninterface TeacherModelProps {\n  position?: [number, number, number];\n  rotation?: [number, number, number];\n  scale?: number;\n}\n\n// Viseme to blend shape mapping for lip-sync\nconst VISEME_MAPPING: Record<string, string> = {\n  'sil': 'viseme_sil',     // Silence\n  'PP': 'viseme_PP',       // P, B, M\n  'FF': 'viseme_FF',       // F, V\n  'TH': 'viseme_TH',       // TH\n  'DD': 'viseme_DD',       // T, D\n  'kk': 'viseme_kk',       // K, G\n  'CH': 'viseme_CH',       // CH, J, SH\n  'SS': 'viseme_SS',       // S, Z\n  'nn': 'viseme_nn',       // N, L\n  'RR': 'viseme_RR',       // R\n  'aa': 'viseme_aa',       // AA (father)\n  'E': 'viseme_E',         // E (bed)\n  'I': 'viseme_I',         // I (bit)\n  'O': 'viseme_O',         // O (boat)\n  'U': 'viseme_U',         // U (book)\n};\n\n// Default teacher model (placeholder - replace with actual GLB model)\nfunction DefaultTeacherGeometry() {\n  return (\n    <group>\n      {/* Head */}\n      <mesh position={[0, 1.6, 0]} castShadow>\n        <sphereGeometry args={[0.15, 32, 32]} />\n        <meshStandardMaterial color=\"#FDBCB4\" />\n      </mesh>\n      \n      {/* Body */}\n      <mesh position={[0, 1, 0]} castShadow>\n        <cylinderGeometry args={[0.2, 0.25, 0.8]} />\n        <meshStandardMaterial color=\"#4A90E2\" />\n      </mesh>\n      \n      {/* Arms */}\n      <mesh position={[-0.3, 1.2, 0]} rotation={[0, 0, 0.3]} castShadow>\n        <cylinderGeometry args={[0.05, 0.05, 0.6]} />\n        <meshStandardMaterial color=\"#FDBCB4\" />\n      </mesh>\n      <mesh position={[0.3, 1.2, 0]} rotation={[0, 0, -0.3]} castShadow>\n        <cylinderGeometry args={[0.05, 0.05, 0.6]} />\n        <meshStandardMaterial color=\"#FDBCB4\" />\n      </mesh>\n      \n      {/* Legs */}\n      <mesh position={[-0.1, 0.2, 0]} castShadow>\n        <cylinderGeometry args={[0.08, 0.08, 0.8]} />\n        <meshStandardMaterial color=\"#2C3E50\" />\n      </mesh>\n      <mesh position={[0.1, 0.2, 0]} castShadow>\n        <cylinderGeometry args={[0.08, 0.08, 0.8]} />\n        <meshStandardMaterial color=\"#2C3E50\" />\n      </mesh>\n      \n      {/* Eyes */}\n      <mesh position={[-0.05, 1.65, 0.12]} castShadow>\n        <sphereGeometry args={[0.02, 16, 16]} />\n        <meshStandardMaterial color=\"#000000\" />\n      </mesh>\n      <mesh position={[0.05, 1.65, 0.12]} castShadow>\n        <sphereGeometry args={[0.02, 16, 16]} />\n        <meshStandardMaterial color=\"#000000\" />\n      </mesh>\n      \n      {/* Mouth (for lip-sync) */}\n      <mesh position={[0, 1.55, 0.12]} castShadow>\n        <sphereGeometry args={[0.015, 16, 16]} />\n        <meshStandardMaterial color=\"#8B0000\" />\n      </mesh>\n    </group>\n  );\n}\n\n// GLB Model component (when actual model is available)\nfunction GLBTeacherModel({ url, ...props }: { url: string } & TeacherModelProps) {\n  const group = useRef<THREE.Group>(null);\n  const { scene, animations } = useGLTF(url);\n  const { actions, mixer } = useAnimations(animations, group);\n  \n  const isSpeaking = useIsSpeaking();\n  const currentVisemes = useCurrentVisemes();\n  const avatarConfig = useAvatarConfig();\n  \n  // Clone the scene to avoid sharing between instances\n  const clonedScene = useMemo(() => scene.clone(), [scene]);\n  \n  // Apply avatar customizations\n  useEffect(() => {\n    if (avatarConfig && clonedScene) {\n      clonedScene.traverse((child) => {\n        if (child instanceof THREE.Mesh) {\n          // Apply appearance customizations\n          if (child.name.includes('hair')) {\n            child.material = new THREE.MeshStandardMaterial({\n              color: avatarConfig.appearance.hairColor\n            });\n          } else if (child.name.includes('skin')) {\n            child.material = new THREE.MeshStandardMaterial({\n              color: avatarConfig.appearance.skinTone\n            });\n          } else if (child.name.includes('clothing')) {\n            child.material = new THREE.MeshStandardMaterial({\n              color: avatarConfig.appearance.clothing\n            });\n          }\n        }\n      });\n    }\n  }, [avatarConfig, clonedScene]);\n  \n  // Handle lip-sync animation\n  useFrame((state, delta) => {\n    if (mixer) mixer.update(delta);\n    \n    if (isSpeaking && currentVisemes.length > 0 && clonedScene) {\n      const currentTime = state.clock.getElapsedTime() * 1000; // Convert to milliseconds\n      \n      // Find the current viseme based on time\n      const currentViseme = currentVisemes.find((viseme, index) => {\n        const nextViseme = currentVisemes[index + 1];\n        return currentTime >= viseme.time && \n               (!nextViseme || currentTime < nextViseme.time);\n      });\n      \n      if (currentViseme) {\n        // Apply viseme to blend shapes\n        clonedScene.traverse((child) => {\n          if (child instanceof THREE.SkinnedMesh && child.morphTargetInfluences) {\n            // Reset all viseme blend shapes\n            Object.values(VISEME_MAPPING).forEach((blendShapeName) => {\n              const index = child.morphTargetDictionary?.[blendShapeName];\n              if (index !== undefined) {\n                child.morphTargetInfluences[index] = 0;\n              }\n            });\n            \n            // Apply current viseme\n            const blendShapeName = VISEME_MAPPING[currentViseme.viseme];\n            if (blendShapeName) {\n              const index = child.morphTargetDictionary?.[blendShapeName];\n              if (index !== undefined) {\n                child.morphTargetInfluences[index] = currentViseme.value;\n              }\n            }\n          }\n        });\n      }\n    }\n  });\n  \n  // Idle animation\n  useEffect(() => {\n    if (actions.idle) {\n      actions.idle.play();\n    }\n    \n    return () => {\n      if (actions.idle) {\n        actions.idle.stop();\n      }\n    };\n  }, [actions]);\n  \n  return (\n    <group ref={group} {...props}>\n      <primitive object={clonedScene} />\n    </group>\n  );\n}\n\n// Main TeacherModel component\nexport default function TeacherModel(props: TeacherModelProps) {\n  const group = useRef<THREE.Group>(null);\n  const avatarConfig = useAvatarConfig();\n  const isSpeaking = useIsSpeaking();\n  const { setModelLoaded } = useAITeacherActions();\n  \n  // Breathing animation for idle state\n  useFrame((state) => {\n    if (group.current && !isSpeaking) {\n      const breathingScale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.02;\n      group.current.scale.y = breathingScale;\n    }\n  });\n  \n  // Subtle swaying animation\n  useFrame((state) => {\n    if (group.current) {\n      group.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.5) * 0.05;\n    }\n  });\n  \n  useEffect(() => {\n    // Mark model as loaded after component mounts\n    const timer = setTimeout(() => {\n      setModelLoaded(true);\n    }, 1000);\n    \n    return () => clearTimeout(timer);\n  }, [setModelLoaded]);\n  \n  // Use GLB model if available, otherwise use default geometry\n  const modelUrl = avatarConfig?.modelUrl;\n  \n  return (\n    <group ref={group} {...props}>\n      {modelUrl ? (\n        <GLBTeacherModel url={modelUrl} />\n      ) : (\n        <DefaultTeacherGeometry />\n      )}\n    </group>\n  );\n}\n\n// Preload the default model\n// useGLTF.preload('/models/teacher-default.glb');\n"], "names": [], "mappings": ";;;;AAEA,6EAA6E;AAE7E;AACA;AACA;AAAA;AACA;AACA;AARA;;;;;;;AAqBA,6CAA6C;AAC7C,MAAM,iBAAyC;IAC7C,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AAEA,sEAAsE;AACtE,SAAS;IACP,qBACE,8OAAC;;0BAEC,8OAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,UAAU;;kCACrC,8OAAC;wBAAe,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;;;;;;kCACpC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAI9B,8OAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,UAAU;;kCACnC,8OAAC;wBAAiB,MAAM;4BAAC;4BAAK;4BAAM;yBAAI;;;;;;kCACxC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAI9B,8OAAC;gBAAK,UAAU;oBAAC,CAAC;oBAAK;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;;kCAC/D,8OAAC;wBAAiB,MAAM;4BAAC;4BAAM;4BAAM;yBAAI;;;;;;kCACzC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAE9B,8OAAC;gBAAK,UAAU;oBAAC;oBAAK;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,UAAU;;kCAC/D,8OAAC;wBAAiB,MAAM;4BAAC;4BAAM;4BAAM;yBAAI;;;;;;kCACzC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAI9B,8OAAC;gBAAK,UAAU;oBAAC,CAAC;oBAAK;oBAAK;iBAAE;gBAAE,UAAU;;kCACxC,8OAAC;wBAAiB,MAAM;4BAAC;4BAAM;4BAAM;yBAAI;;;;;;kCACzC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAE9B,8OAAC;gBAAK,UAAU;oBAAC;oBAAK;oBAAK;iBAAE;gBAAE,UAAU;;kCACvC,8OAAC;wBAAiB,MAAM;4BAAC;4BAAM;4BAAM;yBAAI;;;;;;kCACzC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAI9B,8OAAC;gBAAK,UAAU;oBAAC,CAAC;oBAAM;oBAAM;iBAAK;gBAAE,UAAU;;kCAC7C,8OAAC;wBAAe,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;;;;;;kCACpC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAE9B,8OAAC;gBAAK,UAAU;oBAAC;oBAAM;oBAAM;iBAAK;gBAAE,UAAU;;kCAC5C,8OAAC;wBAAe,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;;;;;;kCACpC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAI9B,8OAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAM;iBAAK;gBAAE,UAAU;;kCACzC,8OAAC;wBAAe,MAAM;4BAAC;4BAAO;4BAAI;yBAAG;;;;;;kCACrC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;;;;;;;AAIpC;AAEA,uDAAuD;AACvD,SAAS,gBAAgB,EAAE,GAAG,EAAE,GAAG,OAA4C;IAC7E,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAClC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE;IACtC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,YAAY;IAErD,MAAM,aAAa,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,iBAAiB,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAEnC,qDAAqD;IACrD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,MAAM,KAAK,IAAI;QAAC;KAAM;IAExD,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,aAAa;YAC/B,YAAY,QAAQ,CAAC,CAAC;gBACpB,IAAI,iBAAiB,+IAAA,CAAA,OAAU,EAAE;oBAC/B,kCAAkC;oBAClC,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS;wBAC/B,MAAM,QAAQ,GAAG,IAAI,+IAAA,CAAA,uBAA0B,CAAC;4BAC9C,OAAO,aAAa,UAAU,CAAC,SAAS;wBAC1C;oBACF,OAAO,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS;wBACtC,MAAM,QAAQ,GAAG,IAAI,+IAAA,CAAA,uBAA0B,CAAC;4BAC9C,OAAO,aAAa,UAAU,CAAC,QAAQ;wBACzC;oBACF,OAAO,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa;wBAC1C,MAAM,QAAQ,GAAG,IAAI,+IAAA,CAAA,uBAA0B,CAAC;4BAC9C,OAAO,aAAa,UAAU,CAAC,QAAQ;wBACzC;oBACF;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAc;KAAY;IAE9B,4BAA4B;IAC5B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO;QACf,IAAI,OAAO,MAAM,MAAM,CAAC;QAExB,IAAI,cAAc,eAAe,MAAM,GAAG,KAAK,aAAa;YAC1D,MAAM,cAAc,MAAM,KAAK,CAAC,cAAc,KAAK,MAAM,0BAA0B;YAEnF,wCAAwC;YACxC,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAC,QAAQ;gBACjD,MAAM,aAAa,cAAc,CAAC,QAAQ,EAAE;gBAC5C,OAAO,eAAe,OAAO,IAAI,IAC1B,CAAC,CAAC,cAAc,cAAc,WAAW,IAAI;YACtD;YAEA,IAAI,eAAe;gBACjB,+BAA+B;gBAC/B,YAAY,QAAQ,CAAC,CAAC;oBACpB,IAAI,iBAAiB,+IAAA,CAAA,cAAiB,IAAI,MAAM,qBAAqB,EAAE;wBACrE,gCAAgC;wBAChC,OAAO,MAAM,CAAC,gBAAgB,OAAO,CAAC,CAAC;4BACrC,MAAM,QAAQ,MAAM,qBAAqB,EAAE,CAAC,eAAe;4BAC3D,IAAI,UAAU,WAAW;gCACvB,MAAM,qBAAqB,CAAC,MAAM,GAAG;4BACvC;wBACF;wBAEA,uBAAuB;wBACvB,MAAM,iBAAiB,cAAc,CAAC,cAAc,MAAM,CAAC;wBAC3D,IAAI,gBAAgB;4BAClB,MAAM,QAAQ,MAAM,qBAAqB,EAAE,CAAC,eAAe;4BAC3D,IAAI,UAAU,WAAW;gCACvB,MAAM,qBAAqB,CAAC,MAAM,GAAG,cAAc,KAAK;4BAC1D;wBACF;oBACF;gBACF;YACF;QACF;IACF;IAEA,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,IAAI,EAAE;YAChB,QAAQ,IAAI,CAAC,IAAI;QACnB;QAEA,OAAO;YACL,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,IAAI;YACnB;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,qBACE,8OAAC;QAAM,KAAK;QAAQ,GAAG,KAAK;kBAC1B,cAAA,8OAAC;YAAU,QAAQ;;;;;;;;;;;AAGzB;AAGe,SAAS,aAAa,KAAwB;IAC3D,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAClC,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,aAAa,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD;IAE7C,qCAAqC;IACrC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,MAAM,OAAO,IAAI,CAAC,YAAY;YAChC,MAAM,iBAAiB,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YACnE,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG;QAC1B;IACF;IAEA,2BAA2B;IAC3B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QACvE;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,MAAM,QAAQ,WAAW;YACvB,eAAe;QACjB,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAe;IAEnB,6DAA6D;IAC7D,MAAM,WAAW,cAAc;IAE/B,qBACE,8OAAC;QAAM,KAAK;QAAQ,GAAG,KAAK;kBACzB,yBACC,8OAAC;YAAgB,KAAK;;;;;iCAEtB,8OAAC;;;;;;;;;;AAIT,EAEA,4BAA4B;CAC5B,kDAAkD", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/3d/Blackboard.tsx"], "sourcesContent": ["'use client';\n\n// Blackboard component for displaying text translations and explanations\n\nimport { useRef, useEffect, useMemo } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Text, Html } from '@react-three/drei';\nimport * as THREE from 'three';\n\ninterface BlackboardProps {\n  position?: [number, number, number];\n  rotation?: [number, number, number];\n  scale?: number;\n  text?: string;\n  width?: number;\n  height?: number;\n}\n\n// HTML overlay for rich text content\nfunction BlackboardOverlay({ \n  text, \n  position, \n  width = 3, \n  height = 2 \n}: { \n  text: string;\n  position: [number, number, number];\n  width?: number;\n  height?: number;\n}) {\n  const overlayRef = useRef<HTMLDivElement>(null);\n  \n  // Parse text for different content types\n  const parseContent = (content: string) => {\n    // Check if content contains furigana markup\n    if (content.includes('[') && content.includes(']')) {\n      return content.split(/(\\[[^\\]]+\\])/).map((part, index) => {\n        if (part.startsWith('[') && part.endsWith(']')) {\n          const furiganaText = part.slice(1, -1);\n          const [kanji, reading] = furiganaText.split('|');\n          return (\n            <span key={index} className=\"inline-block mx-1\">\n              <span className=\"block text-xs text-center text-blue-300\">{reading}</span>\n              <span className=\"block text-lg font-bold\">{kanji}</span>\n            </span>\n          );\n        }\n        return <span key={index}>{part}</span>;\n      });\n    }\n    \n    // Regular text\n    return content;\n  };\n  \n  if (!text) return null;\n  \n  return (\n    <Html\n      position={position}\n      transform\n      occlude\n      style={{\n        width: `${width * 100}px`,\n        height: `${height * 100}px`,\n        pointerEvents: 'none',\n      }}\n    >\n      <div\n        ref={overlayRef}\n        className=\"w-full h-full p-4 text-white font-mono text-sm leading-relaxed overflow-hidden\"\n        style={{\n          background: 'rgba(0, 0, 0, 0.1)',\n          backdropFilter: 'blur(1px)',\n        }}\n      >\n        <div className=\"space-y-2\">\n          {text.split('\\n').map((line, index) => (\n            <div key={index} className=\"flex flex-wrap\">\n              {parseContent(line)}\n            </div>\n          ))}\n        </div>\n      </div>\n    </Html>\n  );\n}\n\n// Chalk dust particle effect\nfunction ChalkDust({ position }: { position: [number, number, number] }) {\n  const particlesRef = useRef<THREE.Points>(null);\n  \n  const particleCount = 50;\n  const positions = useMemo(() => {\n    const pos = new Float32Array(particleCount * 3);\n    for (let i = 0; i < particleCount; i++) {\n      pos[i * 3] = (Math.random() - 0.5) * 2;\n      pos[i * 3 + 1] = (Math.random() - 0.5) * 2;\n      pos[i * 3 + 2] = (Math.random() - 0.5) * 0.1;\n    }\n    return pos;\n  }, []);\n  \n  useFrame((state) => {\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n      \n      // Animate particles\n      const positions = particlesRef.current.geometry.attributes.position.array as Float32Array;\n      for (let i = 0; i < particleCount; i++) {\n        positions[i * 3 + 1] += Math.sin(state.clock.elapsedTime + i) * 0.001;\n      }\n      particlesRef.current.geometry.attributes.position.needsUpdate = true;\n    }\n  });\n  \n  return (\n    <points ref={particlesRef} position={position}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        size={0.02}\n        color=\"#ffffff\"\n        transparent\n        opacity={0.6}\n        sizeAttenuation\n      />\n    </points>\n  );\n}\n\n// Main Blackboard component\nexport default function Blackboard({\n  position = [0, 0, 0],\n  rotation = [0, 0, 0],\n  scale = 1,\n  text = '',\n  width = 3,\n  height = 2\n}: BlackboardProps) {\n  const boardRef = useRef<THREE.Mesh>(null);\n  const frameRef = useRef<THREE.Group>(null);\n  \n  // Subtle animation for the blackboard\n  useFrame((state) => {\n    if (frameRef.current) {\n      frameRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.5) * 0.01;\n    }\n  });\n  \n  return (\n    <group ref={frameRef} position={position} rotation={rotation} scale={scale}>\n      {/* Blackboard frame */}\n      <mesh position={[0, 0, -0.05]} castShadow>\n        <boxGeometry args={[width + 0.2, height + 0.2, 0.1]} />\n        <meshStandardMaterial color=\"#8B4513\" />\n      </mesh>\n      \n      {/* Blackboard surface */}\n      <mesh ref={boardRef} castShadow receiveShadow>\n        <planeGeometry args={[width, height]} />\n        <meshStandardMaterial \n          color=\"#1a1a1a\" \n          roughness={0.8}\n          metalness={0.1}\n        />\n      </mesh>\n      \n      {/* Chalk tray */}\n      <mesh position={[0, -height/2 - 0.05, 0.05]} castShadow>\n        <boxGeometry args={[width * 0.8, 0.05, 0.1]} />\n        <meshStandardMaterial color=\"#8B4513\" />\n      </mesh>\n      \n      {/* Chalk pieces */}\n      {[0, 0.3, -0.3].map((offset, index) => (\n        <mesh \n          key={index}\n          position={[offset, -height/2 - 0.02, 0.1]} \n          rotation={[0, 0, Math.random() * Math.PI]}\n          castShadow\n        >\n          <cylinderGeometry args={[0.01, 0.01, 0.1]} />\n          <meshStandardMaterial color=\"#ffffff\" />\n        </mesh>\n      ))}\n      \n      {/* Text content using 3D text */}\n      {text && (\n        <Text\n          position={[0, 0, 0.01]}\n          fontSize={0.15}\n          color=\"#ffffff\"\n          anchorX=\"center\"\n          anchorY=\"middle\"\n          maxWidth={width * 0.9}\n          textAlign=\"center\"\n          font=\"/fonts/chalk-font.woff\"\n          outlineWidth={0.002}\n          outlineColor=\"#cccccc\"\n        >\n          {text}\n        </Text>\n      )}\n      \n      {/* HTML overlay for rich content */}\n      {text && (\n        <BlackboardOverlay \n          text={text} \n          position={[0, 0, 0.02]} \n          width={width} \n          height={height} \n        />\n      )}\n      \n      {/* Chalk dust effect */}\n      <ChalkDust position={[0, 0, 0.1]} />\n      \n      {/* Eraser */}\n      <mesh position={[width/2 - 0.2, height/2 - 0.1, 0.05]} castShadow>\n        <boxGeometry args={[0.15, 0.05, 0.03]} />\n        <meshStandardMaterial color=\"#654321\" />\n      </mesh>\n      \n      {/* Felt part of eraser */}\n      <mesh position={[width/2 - 0.2, height/2 - 0.1, 0.065]} castShadow>\n        <boxGeometry args={[0.14, 0.04, 0.01]} />\n        <meshStandardMaterial color=\"#333333\" />\n      </mesh>\n    </group>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,yEAAyE;AAEzE;AACA;AACA;AAAA;AANA;;;;;AAkBA,qCAAqC;AACrC,SAAS,kBAAkB,EACzB,IAAI,EACJ,QAAQ,EACR,QAAQ,CAAC,EACT,SAAS,CAAC,EAMX;IACC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,yCAAyC;IACzC,MAAM,eAAe,CAAC;QACpB,4CAA4C;QAC5C,IAAI,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,MAAM;YAClD,OAAO,QAAQ,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC,MAAM;gBAC9C,IAAI,KAAK,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM;oBAC9C,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC;oBACpC,MAAM,CAAC,OAAO,QAAQ,GAAG,aAAa,KAAK,CAAC;oBAC5C,qBACE,8OAAC;wBAAiB,WAAU;;0CAC1B,8OAAC;gCAAK,WAAU;0CAA2C;;;;;;0CAC3D,8OAAC;gCAAK,WAAU;0CAA2B;;;;;;;uBAFlC;;;;;gBAKf;gBACA,qBAAO,8OAAC;8BAAkB;mBAAR;;;;;YACpB;QACF;QAEA,eAAe;QACf,OAAO;IACT;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,uJAAA,CAAA,OAAI;QACH,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;YACL,OAAO,GAAG,QAAQ,IAAI,EAAE,CAAC;YACzB,QAAQ,GAAG,SAAS,IAAI,EAAE,CAAC;YAC3B,eAAe;QACjB;kBAEA,cAAA,8OAAC;YACC,KAAK;YACL,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,gBAAgB;YAClB;sBAEA,cAAA,8OAAC;gBAAI,WAAU;0BACZ,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC;wBAAgB,WAAU;kCACxB,aAAa;uBADN;;;;;;;;;;;;;;;;;;;;AAQtB;AAEA,6BAA6B;AAC7B,SAAS,UAAU,EAAE,QAAQ,EAA0C;IACrE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IAE1C,MAAM,gBAAgB;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,MAAM,IAAI,aAAa,gBAAgB;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;YACtC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACrC,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACzC,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC3C;QACA,OAAO;IACT,GAAG,EAAE;IAEL,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YAE5D,oBAAoB;YACpB,MAAM,YAAY,aAAa,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK;YACzE,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YAClE;YACA,aAAa,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,GAAG;QAClE;IACF;IAEA,qBACE,8OAAC;QAAO,KAAK;QAAc,UAAU;;0BACnC,8OAAC;0BACC,cAAA,8OAAC;oBACC,QAAO;oBACP,OAAO;oBACP,OAAO;oBACP,UAAU;;;;;;;;;;;0BAGd,8OAAC;gBACC,MAAM;gBACN,OAAM;gBACN,WAAW;gBACX,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;AAGe,SAAS,WAAW,EACjC,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,OAAO,EAAE,EACT,QAAQ,CAAC,EACT,SAAS,CAAC,EACM;IAChB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACpC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAErC,sCAAsC;IACtC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QACxF;IACF;IAEA,qBACE,8OAAC;QAAM,KAAK;QAAU,UAAU;QAAU,UAAU;QAAU,OAAO;;0BAEnE,8OAAC;gBAAK,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,UAAU;;kCACvC,8OAAC;wBAAY,MAAM;4BAAC,QAAQ;4BAAK,SAAS;4BAAK;yBAAI;;;;;;kCACnD,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAI9B,8OAAC;gBAAK,KAAK;gBAAU,UAAU;gBAAC,aAAa;;kCAC3C,8OAAC;wBAAc,MAAM;4BAAC;4BAAO;yBAAO;;;;;;kCACpC,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,WAAW;;;;;;;;;;;;0BAKf,8OAAC;gBAAK,UAAU;oBAAC;oBAAG,CAAC,SAAO,IAAI;oBAAM;iBAAK;gBAAE,UAAU;;kCACrD,8OAAC;wBAAY,MAAM;4BAAC,QAAQ;4BAAK;4BAAM;yBAAI;;;;;;kCAC3C,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;YAI7B;gBAAC;gBAAG;gBAAK,CAAC;aAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC;oBAEC,UAAU;wBAAC;wBAAQ,CAAC,SAAO,IAAI;wBAAM;qBAAI;oBACzC,UAAU;wBAAC;wBAAG;wBAAG,KAAK,MAAM,KAAK,KAAK,EAAE;qBAAC;oBACzC,UAAU;;sCAEV,8OAAC;4BAAiB,MAAM;gCAAC;gCAAM;gCAAM;6BAAI;;;;;;sCACzC,8OAAC;4BAAqB,OAAM;;;;;;;mBANvB;;;;;YAWR,sBACC,8OAAC,wJAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBACtB,UAAU;gBACV,OAAM;gBACN,SAAQ;gBACR,SAAQ;gBACR,UAAU,QAAQ;gBAClB,WAAU;gBACV,MAAK;gBACL,cAAc;gBACd,cAAa;0BAEZ;;;;;;YAKJ,sBACC,8OAAC;gBACC,MAAM;gBACN,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBACtB,OAAO;gBACP,QAAQ;;;;;;0BAKZ,8OAAC;gBAAU,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;;;;;;0BAGhC,8OAAC;gBAAK,UAAU;oBAAC,QAAM,IAAI;oBAAK,SAAO,IAAI;oBAAK;iBAAK;gBAAE,UAAU;;kCAC/D,8OAAC;wBAAY,MAAM;4BAAC;4BAAM;4BAAM;yBAAK;;;;;;kCACrC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAI9B,8OAAC;gBAAK,UAAU;oBAAC,QAAM,IAAI;oBAAK,SAAO,IAAI;oBAAK;iBAAM;gBAAE,UAAU;;kCAChE,8OAAC;wBAAY,MAAM;4BAAC;4BAAM;4BAAM;yBAAK;;;;;;kCACrC,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;;;;;;;AAIpC", "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/3d/Experience.tsx"], "sourcesContent": ["'use client';\n\n// 3D Experience component with Canvas, lighting, and camera controls\n\nimport { Suspense } from 'react';\nimport { Canvas } from '@react-three/fiber';\nimport { \n  Environment, \n  CameraControls, \n  Html, \n  useProgress,\n  OrbitControls,\n  PerspectiveCamera,\n  ContactShadows\n} from '@react-three/drei';\nimport TeacherModel from './TeacherModel';\nimport Blackboard from './Blackboard';\nimport { useBlackboardText } from '@/store/useAITeacher';\n\n// Loading component\nfunction Loader() {\n  const { progress } = useProgress();\n  \n  return (\n    <Html center>\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin\"></div>\n        <div className=\"text-center\">\n          <p className=\"text-lg font-medium text-gray-900\">Loading AI Teacher</p>\n          <p className=\"text-sm text-gray-600\">{Math.round(progress)}% complete</p>\n        </div>\n      </div>\n    </Html>\n  );\n}\n\n// Classroom environment setup\nfunction ClassroomEnvironment() {\n  return (\n    <>\n      {/* Lighting setup */}\n      <ambientLight intensity={0.4} />\n      <directionalLight\n        position={[5, 5, 5]}\n        intensity={1}\n        castShadow\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n        shadow-camera-far={50}\n        shadow-camera-left={-10}\n        shadow-camera-right={10}\n        shadow-camera-top={10}\n        shadow-camera-bottom={-10}\n      />\n      <pointLight position={[-5, 5, 5]} intensity={0.5} />\n      \n      {/* Environment map for reflections */}\n      <Environment preset=\"studio\" />\n      \n      {/* Ground/Floor */}\n      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]} receiveShadow>\n        <planeGeometry args={[20, 20]} />\n        <meshStandardMaterial color=\"#f0f0f0\" />\n      </mesh>\n      \n      {/* Contact shadows for better grounding */}\n      <ContactShadows\n        position={[0, -1.99, 0]}\n        opacity={0.4}\n        scale={10}\n        blur={2}\n        far={4}\n      />\n    </>\n  );\n}\n\n// Camera setup component\nfunction CameraSetup() {\n  return (\n    <PerspectiveCamera\n      makeDefault\n      position={[0, 0, 5]}\n      fov={50}\n      near={0.1}\n      far={1000}\n    />\n  );\n}\n\n// Main Experience component\ninterface ExperienceProps {\n  enableControls?: boolean;\n  showBlackboard?: boolean;\n  className?: string;\n}\n\nexport default function Experience({ \n  enableControls = true, \n  showBlackboard = true,\n  className = \"w-full h-full\"\n}: ExperienceProps) {\n  const blackboardText = useBlackboardText();\n\n  return (\n    <div className={className}>\n      <Canvas\n        shadows\n        camera={{ position: [0, 0, 5], fov: 50 }}\n        gl={{ \n          antialias: true, \n          alpha: true,\n          powerPreference: \"high-performance\"\n        }}\n      >\n        <Suspense fallback={<Loader />}>\n          {/* Camera setup */}\n          <CameraSetup />\n          \n          {/* Camera controls */}\n          {enableControls && (\n            <OrbitControls\n              enablePan={true}\n              enableZoom={true}\n              enableRotate={true}\n              minDistance={2}\n              maxDistance={10}\n              minPolarAngle={Math.PI / 6}\n              maxPolarAngle={Math.PI / 2}\n              target={[0, 0, 0]}\n            />\n          )}\n          \n          {/* Environment and lighting */}\n          <ClassroomEnvironment />\n          \n          {/* Teacher model */}\n          <TeacherModel position={[0, -2, 0]} />\n          \n          {/* Blackboard */}\n          {showBlackboard && (\n            <Blackboard \n              position={[0, 1, -3]} \n              text={blackboardText}\n            />\n          )}\n          \n          {/* Additional classroom elements */}\n          <group position={[3, -1, -2]}>\n            {/* Desk */}\n            <mesh castShadow>\n              <boxGeometry args={[2, 0.1, 1]} />\n              <meshStandardMaterial color=\"#8B4513\" />\n            </mesh>\n            {/* Desk legs */}\n            {[[-0.8, -0.5, -0.4], [0.8, -0.5, -0.4], [-0.8, -0.5, 0.4], [0.8, -0.5, 0.4]].map((pos, i) => (\n              <mesh key={i} position={pos} castShadow>\n                <cylinderGeometry args={[0.05, 0.05, 1]} />\n                <meshStandardMaterial color=\"#654321\" />\n              </mesh>\n            ))}\n          </group>\n          \n          {/* Bookshelf */}\n          <group position={[-3, 0, -2]}>\n            <mesh castShadow>\n              <boxGeometry args={[1, 3, 0.3]} />\n              <meshStandardMaterial color=\"#8B4513\" />\n            </mesh>\n            {/* Books */}\n            {Array.from({ length: 8 }, (_, i) => (\n              <mesh \n                key={i} \n                position={[\n                  -0.3 + (i % 4) * 0.2, \n                  -1 + Math.floor(i / 4) * 0.5, \n                  0.2\n                ]} \n                castShadow\n              >\n                <boxGeometry args={[0.15, 0.4, 0.05]} />\n                <meshStandardMaterial \n                  color={`hsl(${i * 45}, 70%, 50%)`} \n                />\n              </mesh>\n            ))}\n          </group>\n        </Suspense>\n      </Canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,qEAAqE;AAErE;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AAjBA;;;;;;;;AAmBA,oBAAoB;AACpB,SAAS;IACP,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,8OAAC,uJAAA,CAAA,OAAI;QAAC,MAAM;kBACV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAoC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;;gCAAyB,KAAK,KAAK,CAAC;gCAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKrE;AAEA,8BAA8B;AAC9B,SAAS;IACP,qBACE;;0BAEE,8OAAC;gBAAa,WAAW;;;;;;0BACzB,8OAAC;gBACC,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBACnB,WAAW;gBACX,UAAU;gBACV,wBAAsB;gBACtB,yBAAuB;gBACvB,qBAAmB;gBACnB,sBAAoB,CAAC;gBACrB,uBAAqB;gBACrB,qBAAmB;gBACnB,wBAAsB,CAAC;;;;;;0BAEzB,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG;oBAAG;iBAAE;gBAAE,WAAW;;;;;;0BAG7C,8OAAC,+JAAA,CAAA,cAAW;gBAAC,QAAO;;;;;;0BAGpB,8OAAC;gBAAK,UAAU;oBAAC,CAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;gBAAE,aAAa;;kCACvE,8OAAC;wBAAc,MAAM;4BAAC;4BAAI;yBAAG;;;;;;kCAC7B,8OAAC;wBAAqB,OAAM;;;;;;;;;;;;0BAI9B,8OAAC,kKAAA,CAAA,iBAAc;gBACb,UAAU;oBAAC;oBAAG,CAAC;oBAAM;iBAAE;gBACvB,SAAS;gBACT,OAAO;gBACP,MAAM;gBACN,KAAK;;;;;;;;AAIb;AAEA,yBAAyB;AACzB,SAAS;IACP,qBACE,8OAAC,qKAAA,CAAA,oBAAiB;QAChB,WAAW;QACX,UAAU;YAAC;YAAG;YAAG;SAAE;QACnB,KAAK;QACL,MAAM;QACN,KAAK;;;;;;AAGX;AASe,SAAS,WAAW,EACjC,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,YAAY,eAAe,EACX;IAChB,MAAM,iBAAiB,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD;IAEvC,qBACE,8OAAC;QAAI,WAAW;kBACd,cAAA,8OAAC,mMAAA,CAAA,SAAM;YACL,OAAO;YACP,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,iBAAiB;YACnB;sBAEA,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;;;;;;kCAEnB,8OAAC;;;;;oBAGA,gCACC,8OAAC,iKAAA,CAAA,gBAAa;wBACZ,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,aAAa;wBACb,aAAa;wBACb,eAAe,KAAK,EAAE,GAAG;wBACzB,eAAe,KAAK,EAAE,GAAG;wBACzB,QAAQ;4BAAC;4BAAG;4BAAG;yBAAE;;;;;;kCAKrB,8OAAC;;;;;kCAGD,8OAAC,wIAAA,CAAA,UAAY;wBAAC,UAAU;4BAAC;4BAAG,CAAC;4BAAG;yBAAE;;;;;;oBAGjC,gCACC,8OAAC,sIAAA,CAAA,UAAU;wBACT,UAAU;4BAAC;4BAAG;4BAAG,CAAC;yBAAE;wBACpB,MAAM;;;;;;kCAKV,8OAAC;wBAAM,UAAU;4BAAC;4BAAG,CAAC;4BAAG,CAAC;yBAAE;;0CAE1B,8OAAC;gCAAK,UAAU;;kDACd,8OAAC;wCAAY,MAAM;4CAAC;4CAAG;4CAAK;yCAAE;;;;;;kDAC9B,8OAAC;wCAAqB,OAAM;;;;;;;;;;;;4BAG7B;gCAAC;oCAAC,CAAC;oCAAK,CAAC;oCAAK,CAAC;iCAAI;gCAAE;oCAAC;oCAAK,CAAC;oCAAK,CAAC;iCAAI;gCAAE;oCAAC,CAAC;oCAAK,CAAC;oCAAK;iCAAI;gCAAE;oCAAC;oCAAK,CAAC;oCAAK;iCAAI;6BAAC,CAAC,GAAG,CAAC,CAAC,KAAK,kBACtF,8OAAC;oCAAa,UAAU;oCAAK,UAAU;;sDACrC,8OAAC;4CAAiB,MAAM;gDAAC;gDAAM;gDAAM;6CAAE;;;;;;sDACvC,8OAAC;4CAAqB,OAAM;;;;;;;mCAFnB;;;;;;;;;;;kCAQf,8OAAC;wBAAM,UAAU;4BAAC,CAAC;4BAAG;4BAAG,CAAC;yBAAE;;0CAC1B,8OAAC;gCAAK,UAAU;;kDACd,8OAAC;wCAAY,MAAM;4CAAC;4CAAG;4CAAG;yCAAI;;;;;;kDAC9B,8OAAC;wCAAqB,OAAM;;;;;;;;;;;;4BAG7B,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;oCAEC,UAAU;wCACR,CAAC,MAAM,AAAC,IAAI,IAAK;wCACjB,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK;wCACzB;qCACD;oCACD,UAAU;;sDAEV,8OAAC;4CAAY,MAAM;gDAAC;gDAAM;gDAAK;6CAAK;;;;;;sDACpC,8OAAC;4CACC,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,WAAW,CAAC;;;;;;;mCAV9B;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBrB", "debugId": null}}, {"offset": {"line": 2063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/%28dash%29/teacher/avatar/page.tsx"], "sourcesContent": ["'use client';\n\n// Teacher Avatar Setup Page - Customize AI teacher appearance and personality\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Label } from '@/components/ui/label';\nimport { Slider } from '@/components/ui/slider';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  Bot, \n  ArrowLeft, \n  Save, \n  Play, \n  Palette,\n  Volume2,\n  User,\n  Eye\n} from 'lucide-react';\nimport Experience from '@/components/3d/Experience';\nimport { \n  useAvatarConfig, \n  useAvatarHelpers,\n  useAITeacherActions,\n  defaultAvatarConfig \n} from '@/store/useAITeacher';\n\nexport default function TeacherAvatarPage() {\n  const router = useRouter();\n  const avatarConfig = useAvatarConfig();\n  const { updateAvatarAppearance, updateVoiceSettings, updatePersonality } = useAvatarHelpers();\n  const { setAvatar, setBlackboardText } = useAITeacherActions();\n  const [isSaving, setIsSaving] = useState(false);\n  const [previewText, setPreviewText] = useState('Hello! I am your AI teacher. How can I help you today?');\n\n  // Initialize avatar config if not set\n  const currentConfig = avatarConfig || {\n    ...defaultAvatarConfig,\n    id: 'temp',\n    teacherId: 'current-teacher',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  };\n\n  const handleSaveAvatar = async () => {\n    setIsSaving(true);\n    try {\n      // TODO: Save to backend when API is ready\n      console.log('Saving avatar configuration:', currentConfig);\n      \n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      alert('Avatar configuration saved successfully!');\n    } catch (error) {\n      console.error('Failed to save avatar:', error);\n      alert('Failed to save avatar configuration. Please try again.');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePreviewVoice = () => {\n    // TODO: Implement voice preview when TTS API is ready\n    console.log('Playing voice preview with settings:', currentConfig.voiceSettings);\n    setBlackboardText(previewText);\n  };\n\n  const handleResetToDefault = () => {\n    setAvatar({\n      ...defaultAvatarConfig,\n      id: currentConfig.id,\n      teacherId: currentConfig.teacherId,\n      createdAt: currentConfig.createdAt,\n      updatedAt: new Date(),\n    });\n  };\n\n  return (\n    <div className=\"h-[calc(100vh-4rem)] flex\">\n      {/* Left Panel - 3D Preview */}\n      <div className=\"w-1/2 bg-gray-100 relative\">\n        <div className=\"absolute top-4 left-4 z-10\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => router.push('/teacher')}\n            className=\"bg-white/90 backdrop-blur-sm\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to Dashboard\n          </Button>\n        </div>\n        \n        <div className=\"absolute top-4 right-4 z-10\">\n          <Badge variant=\"secondary\" className=\"bg-white/90 backdrop-blur-sm\">\n            3D Preview\n          </Badge>\n        </div>\n        \n        <Experience \n          enableControls={true}\n          showBlackboard={true}\n          className=\"w-full h-full\"\n        />\n        \n        <div className=\"absolute bottom-4 left-4 right-4 z-10\">\n          <Card className=\"bg-white/90 backdrop-blur-sm\">\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-3\">\n                <Button\n                  onClick={handlePreviewVoice}\n                  size=\"sm\"\n                  className=\"flex-shrink-0\"\n                >\n                  <Play className=\"h-4 w-4 mr-2\" />\n                  Preview Voice\n                </Button>\n                <input\n                  type=\"text\"\n                  value={previewText}\n                  onChange={(e) => setPreviewText(e.target.value)}\n                  placeholder=\"Enter text to preview...\"\n                  className=\"flex-1 px-3 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Right Panel - Configuration */}\n      <div className=\"w-1/2 overflow-y-auto bg-white\">\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <div>\n              <h1 className=\"text-2xl font-bold flex items-center space-x-2\">\n                <Bot className=\"h-6 w-6 text-blue-600\" />\n                <span>AI Avatar Setup</span>\n              </h1>\n              <p className=\"text-gray-600 mt-1\">\n                Customize your AI teaching assistant's appearance and personality\n              </p>\n            </div>\n            <div className=\"flex space-x-2\">\n              <Button\n                variant=\"outline\"\n                onClick={handleResetToDefault}\n                size=\"sm\"\n              >\n                Reset to Default\n              </Button>\n              <Button\n                onClick={handleSaveAvatar}\n                disabled={isSaving}\n                size=\"sm\"\n              >\n                {isSaving ? (\n                  <>\n                    <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\" />\n                    Saving...\n                  </>\n                ) : (\n                  <>\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    Save Avatar\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n\n          <Tabs defaultValue=\"appearance\" className=\"space-y-6\">\n            <TabsList className=\"grid w-full grid-cols-3\">\n              <TabsTrigger value=\"appearance\" className=\"flex items-center space-x-2\">\n                <Palette className=\"h-4 w-4\" />\n                <span>Appearance</span>\n              </TabsTrigger>\n              <TabsTrigger value=\"voice\" className=\"flex items-center space-x-2\">\n                <Volume2 className=\"h-4 w-4\" />\n                <span>Voice</span>\n              </TabsTrigger>\n              <TabsTrigger value=\"personality\" className=\"flex items-center space-x-2\">\n                <User className=\"h-4 w-4\" />\n                <span>Personality</span>\n              </TabsTrigger>\n            </TabsList>\n\n            {/* Appearance Tab */}\n            <TabsContent value=\"appearance\" className=\"space-y-6\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Physical Appearance</CardTitle>\n                  <CardDescription>\n                    Customize the visual appearance of your AI avatar\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"skinTone\">Skin Tone</Label>\n                    <Select\n                      value={currentConfig.appearance.skinTone}\n                      onValueChange={(value) => updateAvatarAppearance({ skinTone: value })}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"light\">Light</SelectItem>\n                        <SelectItem value=\"medium\">Medium</SelectItem>\n                        <SelectItem value=\"dark\">Dark</SelectItem>\n                        <SelectItem value=\"olive\">Olive</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"hairColor\">Hair Color</Label>\n                    <Select\n                      value={currentConfig.appearance.hairColor}\n                      onValueChange={(value) => updateAvatarAppearance({ hairColor: value })}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"black\">Black</SelectItem>\n                        <SelectItem value=\"brown\">Brown</SelectItem>\n                        <SelectItem value=\"blonde\">Blonde</SelectItem>\n                        <SelectItem value=\"red\">Red</SelectItem>\n                        <SelectItem value=\"gray\">Gray</SelectItem>\n                        <SelectItem value=\"white\">White</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"eyeColor\">Eye Color</Label>\n                    <Select\n                      value={currentConfig.appearance.eyeColor}\n                      onValueChange={(value) => updateAvatarAppearance({ eyeColor: value })}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"brown\">Brown</SelectItem>\n                        <SelectItem value=\"blue\">Blue</SelectItem>\n                        <SelectItem value=\"green\">Green</SelectItem>\n                        <SelectItem value=\"hazel\">Hazel</SelectItem>\n                        <SelectItem value=\"gray\">Gray</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"clothing\">Clothing Style</Label>\n                    <Select\n                      value={currentConfig.appearance.clothing}\n                      onValueChange={(value) => updateAvatarAppearance({ clothing: value })}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"professional\">Professional</SelectItem>\n                        <SelectItem value=\"casual\">Casual</SelectItem>\n                        <SelectItem value=\"formal\">Formal</SelectItem>\n                        <SelectItem value=\"academic\">Academic</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            {/* Voice Tab */}\n            <TabsContent value=\"voice\" className=\"space-y-6\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Voice Settings</CardTitle>\n                  <CardDescription>\n                    Adjust the voice characteristics of your AI avatar\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div>\n                    <Label>Pitch: {currentConfig.voiceSettings.pitch.toFixed(1)}</Label>\n                    <Slider\n                      value={[currentConfig.voiceSettings.pitch]}\n                      onValueChange={([value]) => updateVoiceSettings({ pitch: value })}\n                      min={0.5}\n                      max={2.0}\n                      step={0.1}\n                      className=\"mt-2\"\n                    />\n                  </div>\n\n                  <div>\n                    <Label>Speed: {currentConfig.voiceSettings.speed.toFixed(1)}</Label>\n                    <Slider\n                      value={[currentConfig.voiceSettings.speed]}\n                      onValueChange={([value]) => updateVoiceSettings({ speed: value })}\n                      min={0.5}\n                      max={2.0}\n                      step={0.1}\n                      className=\"mt-2\"\n                    />\n                  </div>\n\n                  <div>\n                    <Label>Volume: {Math.round(currentConfig.voiceSettings.volume * 100)}%</Label>\n                    <Slider\n                      value={[currentConfig.voiceSettings.volume]}\n                      onValueChange={([value]) => updateVoiceSettings({ volume: value })}\n                      min={0.1}\n                      max={1.0}\n                      step={0.1}\n                      className=\"mt-2\"\n                    />\n                  </div>\n\n                  <Button\n                    onClick={handlePreviewVoice}\n                    className=\"w-full\"\n                    variant=\"outline\"\n                  >\n                    <Volume2 className=\"h-4 w-4 mr-2\" />\n                    Test Voice Settings\n                  </Button>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            {/* Personality Tab */}\n            <TabsContent value=\"personality\" className=\"space-y-6\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Personality Traits</CardTitle>\n                  <CardDescription>\n                    Define how your AI avatar interacts with students\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div>\n                    <Label htmlFor=\"tone\">Communication Tone</Label>\n                    <Select\n                      value={currentConfig.personality.tone}\n                      onValueChange={(value: any) => updatePersonality({ tone: value })}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"formal\">Formal</SelectItem>\n                        <SelectItem value=\"casual\">Casual</SelectItem>\n                        <SelectItem value=\"friendly\">Friendly</SelectItem>\n                        <SelectItem value=\"professional\">Professional</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div>\n                    <Label>Enthusiasm Level: {currentConfig.personality.enthusiasm}/10</Label>\n                    <Slider\n                      value={[currentConfig.personality.enthusiasm]}\n                      onValueChange={([value]) => updatePersonality({ enthusiasm: value })}\n                      min={1}\n                      max={10}\n                      step={1}\n                      className=\"mt-2\"\n                    />\n                  </div>\n\n                  <div>\n                    <Label>Patience Level: {currentConfig.personality.patience}/10</Label>\n                    <Slider\n                      value={[currentConfig.personality.patience]}\n                      onValueChange={([value]) => updatePersonality({ patience: value })}\n                      min={1}\n                      max={10}\n                      step={1}\n                      className=\"mt-2\"\n                    />\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,8EAA8E;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAxBA;;;;;;;;;;;;;;AA+Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD;IAC1F,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,sCAAsC;IACtC,MAAM,gBAAgB,gBAAgB;QACpC,GAAG,4HAAA,CAAA,sBAAmB;QACtB,IAAI;QACJ,WAAW;QACX,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IAEA,MAAM,mBAAmB;QACvB,YAAY;QACZ,IAAI;YACF,0CAA0C;YAC1C,QAAQ,GAAG,CAAC,gCAAgC;YAE5C,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,qBAAqB;QACzB,sDAAsD;QACtD,QAAQ,GAAG,CAAC,wCAAwC,cAAc,aAAa;QAC/E,kBAAkB;IACpB;IAEA,MAAM,uBAAuB;QAC3B,UAAU;YACR,GAAG,4HAAA,CAAA,sBAAmB;YACtB,IAAI,cAAc,EAAE;YACpB,WAAW,cAAc,SAAS;YAClC,WAAW,cAAc,SAAS;YAClC,WAAW,IAAI;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAA+B;;;;;;;;;;;kCAKtE,8OAAC,sIAAA,CAAA,UAAU;wBACT,gBAAgB;wBAChB,gBAAgB;wBAChB,WAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAIpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,MAAK;sDACN;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU;4CACV,MAAK;sDAEJ,yBACC;;kEACE,8OAAC;wDAAI,WAAU;;;;;;oDAAsF;;6EAIvG;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;sCAQ3C,8OAAC,gIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAa,WAAU;;8CACxC,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAa,WAAU;;8DACxC,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAQ,WAAU;;8DACnC,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAc,WAAU;;8DACzC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAa,WAAU;8CACxC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,cAAc,UAAU,CAAC,QAAQ;gEACxC,eAAe,CAAC,QAAU,uBAAuB;wEAAE,UAAU;oEAAM;;kFAEnE,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;0FAC1B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;0FACzB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAKhC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,cAAc,UAAU,CAAC,SAAS;gEACzC,eAAe,CAAC,QAAU,uBAAuB;wEAAE,WAAW;oEAAM;;kFAEpE,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;0FAC1B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;0FAC1B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAM;;;;;;0FACxB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;0FACzB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAKhC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,cAAc,UAAU,CAAC,QAAQ;gEACxC,eAAe,CAAC,QAAU,uBAAuB;wEAAE,UAAU;oEAAM;;kFAEnE,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;0FAC1B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;0FACzB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;0FAC1B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;0FAC1B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;;;;;;;;;;;;;;;;;;;kEAK/B,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,cAAc,UAAU,CAAC,QAAQ;gEACxC,eAAe,CAAC,QAAU,uBAAuB;wEAAE,UAAU;oEAAM;;kFAEnE,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAe;;;;;;0FACjC,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASzC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;8CACnC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAQ,cAAc,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;0EACzD,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,cAAc,aAAa,CAAC,KAAK;iEAAC;gEAC1C,eAAe,CAAC,CAAC,MAAM,GAAK,oBAAoB;wEAAE,OAAO;oEAAM;gEAC/D,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAQ,cAAc,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;0EACzD,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,cAAc,aAAa,CAAC,KAAK;iEAAC;gEAC1C,eAAe,CAAC,CAAC,MAAM,GAAK,oBAAoB;wEAAE,OAAO;oEAAM;gEAC/D,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAS,KAAK,KAAK,CAAC,cAAc,aAAa,CAAC,MAAM,GAAG;oEAAK;;;;;;;0EACrE,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,cAAc,aAAa,CAAC,MAAM;iEAAC;gEAC3C,eAAe,CAAC,CAAC,MAAM,GAAK,oBAAoB;wEAAE,QAAQ;oEAAM;gEAChE,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAId,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,WAAU;wDACV,SAAQ;;0EAER,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAQ5C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAc,WAAU;8CACzC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,cAAc,WAAW,CAAC,IAAI;gEACrC,eAAe,CAAC,QAAe,kBAAkB;wEAAE,MAAM;oEAAM;;kFAE/D,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAW;;;;;;0FAC7B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAe;;;;;;;;;;;;;;;;;;;;;;;;kEAKvC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAmB,cAAc,WAAW,CAAC,UAAU;oEAAC;;;;;;;0EAC/D,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,cAAc,WAAW,CAAC,UAAU;iEAAC;gEAC7C,eAAe,CAAC,CAAC,MAAM,GAAK,kBAAkB;wEAAE,YAAY;oEAAM;gEAClE,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAiB,cAAc,WAAW,CAAC,QAAQ;oEAAC;;;;;;;0EAC3D,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;oEAAC,cAAc,WAAW,CAAC,QAAQ;iEAAC;gEAC3C,eAAe,CAAC,CAAC,MAAM,GAAK,kBAAkB;wEAAE,UAAU;oEAAM;gEAChE,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC", "debugId": null}}]}