# AI Tutor Platform Frontend

A comprehensive AI-powered educational platform built with Next.js 14, React Three Fiber, Tailwind CSS, and Zustand.

## 🚀 Features

### 🎓 Role-Based Dashboards
- **Student Dashboard**: View subjects, study materials, progress tracking, AI chat interface
- **Teacher Dashboard**: Manage materials, customize AI avatars, review AI responses, analytics
- **HOD Dashboard**: Monitor teachers/students, send announcements, department analytics
- **Admin Dashboard**: Global platform management, organization oversight, system monitoring

### 🤖 AI Chat Interface
- Real-time conversations with AI tutors
- Streaming responses via Server-Sent Events (SSE)
- Furigana support for Japanese language learning
- Grammar breakdown and analysis
- Voice synthesis with lip-sync animations
- Message rating and regeneration

### 🎭 3D AI Avatar System
- Interactive 3D teacher models with React Three Fiber
- Customizable avatar appearance and personality
- Lip-sync animations with viseme data
- Dynamic blackboard overlay for visual explanations
- Multiple environment presets and lighting controls
- Camera controls and preset positions

### 🔐 Authentication & Authorization
- NextAuth.js integration with JWT tokens
- Role-based route protection
- Demo accounts for all user types
- Secure session management

## 🏗️ Architecture

### Directory Structure
```
frontend/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Authentication routes
│   │   └── sign-in/page.tsx      # Sign-in page
│   ├── (dash)/                   # Dashboard routes
│   │   ├── student/page.tsx      # Student dashboard
│   │   ├── teacher/page.tsx      # Teacher dashboard
│   │   ├── hod/page.tsx          # HOD dashboard
│   │   └── admin/page.tsx        # Admin dashboard
│   ├── layout.tsx                # Root layout
│   └── globals.css               # Global styles
├── components/                   # Reusable components
│   ├── chat/                     # Chat interface components
│   │   ├── MessageList.tsx       # Message display with furigana
│   │   └── TypingBox.tsx         # Message input with voice/file support
│   ├── 3d/                       # 3D components
│   │   ├── Experience.tsx        # Main 3D scene wrapper
│   │   ├── TeacherModel.tsx      # 3D teacher avatar with animations
│   │   └── Blackboard.tsx        # 3D blackboard object
│   └── ui/                       # shadcn/ui components
├── store/                        # Zustand state management
│   ├── useUser.ts                # User and app state
│   ├── useChat.ts                # Chat state and actions
│   └── useAITeacher.ts           # 3D avatar and scene state
├── lib/                          # Utilities and API clients
│   └── api.ts                    # API client with mock data
└── types/                        # TypeScript type definitions
```

## 🛠️ Technology Stack

- **Framework**: Next.js 14 with App Router
- **UI Library**: React 18 with TypeScript
- **Styling**: Tailwind CSS + shadcn/ui components
- **3D Graphics**: React Three Fiber + Drei
- **State Management**: Zustand with devtools
- **Authentication**: NextAuth.js
- **File Uploads**: AWS S3 with signed URLs
- **Real-time**: Server-Sent Events (SSE)

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Git

### Installation
```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Start development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## 🎮 Demo Accounts

The platform includes demo accounts for testing all user roles:

| Role | Email | Password | Features |
|------|-------|----------|----------|
| Student | <EMAIL> | demo123 | View subjects, chat with AI, track progress |
| Teacher | <EMAIL> | demo123 | Manage materials, customize avatar, review responses |
| HOD | <EMAIL> | demo123 | Monitor department, send announcements |
| Admin | <EMAIL> | demo123 | Global management, system monitoring |

## 📦 Deployment

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
