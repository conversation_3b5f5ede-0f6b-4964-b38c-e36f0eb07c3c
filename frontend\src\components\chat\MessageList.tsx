'use client';

// MessageList component for displaying chat messages with furigana and grammar breakdown

import { useEffect, useRef } from 'react';
import { ChatMessage } from '@/types';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  User,
  Bot,
  Volume2,
  Copy,
  RotateCcw,
  BookOpen,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MessageListProps {
  messages: ChatMessage[];
  isLoading?: boolean;
  isStreaming?: boolean;
  onRegenerateResponse?: () => void;
  onPlayAudio?: (audioUrl: string) => void;
}

interface FuriganaDisplayProps {
  furigana: ChatMessage['furigana'];
}

interface GrammarBreakdownProps {
  grammar: ChatMessage['grammarBreakdown'];
}

// Component to display furigana text
function FuriganaDisplay({ furigana }: FuriganaDisplayProps) {
  if (!furigana || furigana.length === 0) return null;

  return (
    <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
      <div className="flex items-center space-x-2 mb-2">
        <BookOpen className="h-4 w-4 text-blue-600" />
        <span className="text-sm font-medium text-blue-800">Furigana Reading</span>
      </div>
      <div className="space-y-2">
        {furigana.map((item, index) => (
          <div key={index} className="inline-block mr-3 mb-2">
            <div className="text-center">
              <div className="text-xs text-blue-600 leading-none">{item.reading}</div>
              <div className="text-lg font-medium text-gray-900">{item.kanji}</div>
              {item.meaning && (
                <div className="text-xs text-gray-600 mt-1">{item.meaning}</div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Component to display grammar breakdown
function GrammarBreakdown({ grammar }: GrammarBreakdownProps) {
  if (!grammar || grammar.length === 0) return null;

  return (
    <div className="mt-3 p-3 bg-green-50 rounded-lg border border-green-200">
      <div className="flex items-center space-x-2 mb-2">
        <MessageSquare className="h-4 w-4 text-green-600" />
        <span className="text-sm font-medium text-green-800">Grammar Analysis</span>
      </div>
      <div className="space-y-2">
        {grammar.map((item, index) => (
          <div key={index} className="border-l-2 border-green-300 pl-3">
            <div className="flex items-center space-x-2 mb-1">
              <span className="font-medium text-gray-900">{item.text}</span>
              <Badge variant="outline" className="text-xs">
                {item.partOfSpeech}
              </Badge>
            </div>
            <p className="text-sm text-gray-700">{item.explanation}</p>
            {item.example && (
              <p className="text-xs text-gray-600 mt-1 italic">
                Example: {item.example}
              </p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// Individual message component
function MessageItem({ 
  message, 
  onPlayAudio, 
  onRegenerateResponse 
}: { 
  message: ChatMessage;
  onPlayAudio?: (audioUrl: string) => void;
  onRegenerateResponse?: () => void;
}) {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  const handleCopyMessage = () => {
    navigator.clipboard.writeText(message.content);
  };

  const handlePlayAudio = () => {
    if (message.audioUrl && onPlayAudio) {
      onPlayAudio(message.audioUrl);
    }
  };

  return (
    <div className={cn(
      "flex gap-3 mb-4",
      isUser ? "justify-end" : "justify-start"
    )}>
      {!isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <Bot className="h-4 w-4 text-white" />
          </div>
        </div>
      )}
      
      <div className={cn(
        "max-w-[80%] space-y-1",
        isUser ? "items-end" : "items-start"
      )}>
        <Card className={cn(
          "p-3",
          isUser 
            ? "bg-blue-600 text-white" 
            : "bg-white border border-gray-200"
        )}>
          <div className="space-y-2">
            <p className={cn(
              "text-sm leading-relaxed",
              isUser ? "text-white" : "text-gray-900"
            )}>
              {message.content}
            </p>
            
            {/* Action buttons for assistant messages */}
            {isAssistant && (
              <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyMessage}
                  className="h-6 px-2 text-xs"
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy
                </Button>
                
                {message.hasAudio && message.audioUrl && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePlayAudio}
                    className="h-6 px-2 text-xs"
                  >
                    <Volume2 className="h-3 w-3 mr-1" />
                    Play
                  </Button>
                )}
                
                {onRegenerateResponse && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onRegenerateResponse}
                    className="h-6 px-2 text-xs"
                  >
                    <RotateCcw className="h-3 w-3 mr-1" />
                    Regenerate
                  </Button>
                )}
              </div>
            )}
          </div>
        </Card>
        
        {/* Furigana display for assistant messages */}
        {isAssistant && message.furigana && (
          <FuriganaDisplay furigana={message.furigana} />
        )}
        
        {/* Grammar breakdown for assistant messages */}
        {isAssistant && message.grammarBreakdown && (
          <GrammarBreakdown grammar={message.grammarBreakdown} />
        )}
        
        {/* Timestamp */}
        <div className={cn(
          "text-xs text-gray-500",
          isUser ? "text-right" : "text-left"
        )}>
          {message.timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </div>
      </div>
      
      {isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-white" />
          </div>
        </div>
      )}
    </div>
  );
}

// Loading indicator for streaming messages
function LoadingIndicator() {
  return (
    <div className="flex gap-3 mb-4">
      <div className="flex-shrink-0">
        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
          <Bot className="h-4 w-4 text-white" />
        </div>
      </div>
      <Card className="p-3 bg-white border border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
          <span className="text-sm text-gray-600">AI is thinking...</span>
        </div>
      </Card>
    </div>
  );
}

// Main MessageList component
export default function MessageList({
  messages,
  isLoading = false,
  isStreaming = false,
  onRegenerateResponse,
  onPlayAudio
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading, isStreaming]);

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.length === 0 ? (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Start a conversation
            </h3>
            <p className="text-gray-600">
              Ask me anything about your subject. I&apos;m here to help you learn!
            </p>
          </div>
        </div>
      ) : (
        <>
          {messages.map((message) => (
            <MessageItem
              key={message.id}
              message={message}
              onPlayAudio={onPlayAudio}
              onRegenerateResponse={
                message.role === 'assistant' ? onRegenerateResponse : undefined
              }
            />
          ))}
          
          {(isLoading || isStreaming) && <LoadingIndicator />}
        </>
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
}
