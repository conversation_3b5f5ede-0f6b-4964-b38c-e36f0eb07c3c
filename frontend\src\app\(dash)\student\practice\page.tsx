'use client';

// Student Practice Page

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowLeft, 
  Play, 
  Clock, 
  CheckCircle, 
  Target,
  Trophy,
  BookOpen,
  Brain,
  Zap
} from 'lucide-react';
import { useSubjects } from '@/store/useUser';
import { mockData } from '@/lib/api';

interface PracticeSession {
  id: string;
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number; // in minutes
  questionsCount: number;
  completedCount: number;
  subjectId: string;
  subjectName: string;
  type: 'quiz' | 'flashcards' | 'writing' | 'listening';
}

export default function StudentPracticePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const subjectFilter = searchParams.get('subject');
  const { data: session } = useSession();
  console.log('User session:', session); // For debugging
  const subjects = useSubjects();
  const [isLoading, setIsLoading] = useState(true);

  const availableSubjects = subjects.length > 0 ? subjects : mockData.subjects;
  const selectedSubject = availableSubjects.find(s => s.id === subjectFilter);

  // Mock practice sessions data
  const mockPracticeSessions: PracticeSession[] = [
    {
      id: '1',
      title: 'Hiragana Recognition',
      description: 'Practice identifying hiragana characters',
      difficulty: 'beginner',
      estimatedTime: 15,
      questionsCount: 20,
      completedCount: 12,
      subjectId: '1',
      subjectName: 'Japanese Language',
      type: 'flashcards'
    },
    {
      id: '2',
      title: 'Basic Grammar Quiz',
      description: 'Test your understanding of basic Japanese grammar patterns',
      difficulty: 'intermediate',
      estimatedTime: 25,
      questionsCount: 15,
      completedCount: 0,
      subjectId: '1',
      subjectName: 'Japanese Language',
      type: 'quiz'
    },
    {
      id: '3',
      title: 'Listening Comprehension',
      description: 'Practice understanding spoken Japanese',
      difficulty: 'intermediate',
      estimatedTime: 30,
      questionsCount: 10,
      completedCount: 5,
      subjectId: '1',
      subjectName: 'Japanese Language',
      type: 'listening'
    },
    {
      id: '4',
      title: 'Algebra Problem Solving',
      description: 'Practice solving algebraic equations',
      difficulty: 'beginner',
      estimatedTime: 20,
      questionsCount: 12,
      completedCount: 12,
      subjectId: '2',
      subjectName: 'Mathematics',
      type: 'quiz'
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'quiz':
        return <Brain className="h-5 w-5 text-blue-600" />;
      case 'flashcards':
        return <Zap className="h-5 w-5 text-purple-600" />;
      case 'writing':
        return <BookOpen className="h-5 w-5 text-green-600" />;
      case 'listening':
        return <Play className="h-5 w-5 text-orange-600" />;
      default:
        return <Target className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'quiz':
        return 'Quiz';
      case 'flashcards':
        return 'Flashcards';
      case 'writing':
        return 'Writing';
      case 'listening':
        return 'Listening';
      default:
        return 'Practice';
    }
  };

  const filteredSessions = subjectFilter 
    ? mockPracticeSessions.filter(session => session.subjectId === subjectFilter)
    : mockPracticeSessions;

  const handleStartPractice = (sessionId: string) => {
    // TODO: Implement actual practice session when backend is ready
    console.log('Starting practice session:', sessionId);
    // For now, just show an alert
    alert('Practice session would start here!');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading practice sessions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/student')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Target className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Practice Sessions</h1>
              <p className="text-gray-600">
                {selectedSubject 
                  ? `Practice ${selectedSubject.name} skills`
                  : 'Improve your skills with interactive practice'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Subject Filter Info */}
      {selectedSubject && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <BookOpen className="h-5 w-5 text-blue-600" />
                <div>
                  <h3 className="font-medium text-blue-900">{selectedSubject.name}</h3>
                  <p className="text-sm text-blue-700">
                    {filteredSessions.length} practice session{filteredSessions.length !== 1 ? 's' : ''} available
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/student/practice')}
              >
                View All Subjects
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Practice Sessions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSessions.map((session) => {
          const progress = (session.completedCount / session.questionsCount) * 100;
          const isCompleted = session.completedCount === session.questionsCount;

          return (
            <Card key={session.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(session.type)}
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg">{session.title}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="secondary" className="text-xs">
                          {getTypeLabel(session.type)}
                        </Badge>
                        <Badge className={`text-xs ${getDifficultyColor(session.difficulty)}`}>
                          {session.difficulty}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  {isCompleted && (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  )}
                </div>
                <CardDescription className="line-clamp-2">
                  {session.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Progress */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Progress</span>
                      <span className="font-medium">
                        {session.completedCount}/{session.questionsCount}
                      </span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>

                  {/* Session Info */}
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{session.estimatedTime} min</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <BookOpen className="h-4 w-4" />
                      <span>{session.subjectName}</span>
                    </div>
                  </div>

                  {/* Action Button */}
                  <Button
                    onClick={() => handleStartPractice(session.id)}
                    className="w-full"
                    variant={isCompleted ? "outline" : "default"}
                  >
                    {isCompleted ? (
                      <>
                        <Trophy className="h-4 w-4 mr-2" />
                        Review
                      </>
                    ) : session.completedCount > 0 ? (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Continue
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Start Practice
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredSessions.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No practice sessions available
            </h3>
            <p className="text-gray-600 mb-4">
              {selectedSubject 
                ? `No practice sessions are available for ${selectedSubject.name} yet.`
                : 'No practice sessions are available yet.'}
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/student')}
            >
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
