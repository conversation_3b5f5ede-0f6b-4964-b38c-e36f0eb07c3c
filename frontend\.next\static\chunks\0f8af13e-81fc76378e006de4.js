"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[683],{1966:(e,t,r)=>{let a,n;r.d(t,{EY:()=>ee,PY:()=>C});var i=r(3264),o=r(5202),s=r(4253),l=r(5454),h=r(3064);let u=(0,o.kl)({name:"Typr Font Parser",dependencies:[function(){var e,t,r;return"undefined"==typeof window&&(self.window=self),(e={},(t={parse:function(e){var r=t._bin,a=new Uint8Array(e);if("ttcf"==r.readASCII(a,0,4)){var n=4;r.readUshort(a,n),n+=2,r.readUshort(a,n),n+=2;var i=r.readUint(a,n);n+=4;for(var o=[],s=0;s<i;s++){var l=r.readUint(a,n);n+=4,o.push(t._readFont(a,l))}return o}return[t._readFont(a,0)]},_readFont:function(e,r){var a=t._bin,n=r;a.readFixed(e,r),r+=4;var i=a.readUshort(e,r);r+=2,a.readUshort(e,r),r+=2,a.readUshort(e,r),r+=2,a.readUshort(e,r),r+=2;for(var o=["cmap","head","hhea","maxp","hmtx","name","OS/2","post","loca","glyf","kern","CFF ","GDEF","GPOS","GSUB","SVG "],s={_data:e,_offset:n},l={},h=0;h<i;h++){var u=a.readASCII(e,r,4);r+=4,a.readUint(e,r),r+=4;var f=a.readUint(e,r);r+=4;var d=a.readUint(e,r);r+=4,l[u]={offset:f,length:d}}for(h=0;h<o.length;h++){var c=o[h];l[c]&&(s[c.trim()]=t[c.trim()].parse(e,l[c].offset,l[c].length,s))}return s},_tabOffset:function(e,r,a){for(var n=t._bin,i=n.readUshort(e,a+4),o=a+12,s=0;s<i;s++){var l=n.readASCII(e,o,4);o+=4,n.readUint(e,o),o+=4;var h=n.readUint(e,o);if(o+=4,n.readUint(e,o),o+=4,l==r)return h}return 0}})._bin={readFixed:function(e,t){return(e[t]<<8|e[t+1])+(e[t+2]<<8|e[t+3])/65540},readF2dot14:function(e,r){return t._bin.readShort(e,r)/16384},readInt:function(e,r){return t._bin._view(e).getInt32(r)},readInt8:function(e,r){return t._bin._view(e).getInt8(r)},readShort:function(e,r){return t._bin._view(e).getInt16(r)},readUshort:function(e,r){return t._bin._view(e).getUint16(r)},readUshorts:function(e,r,a){for(var n=[],i=0;i<a;i++)n.push(t._bin.readUshort(e,r+2*i));return n},readUint:function(e,r){return t._bin._view(e).getUint32(r)},readUint64:function(e,r){return 0x100000000*t._bin.readUint(e,r)+t._bin.readUint(e,r+4)},readASCII:function(e,t,r){for(var a="",n=0;n<r;n++)a+=String.fromCharCode(e[t+n]);return a},readUnicode:function(e,t,r){for(var a="",n=0;n<r;n++)a+=String.fromCharCode(e[t++]<<8|e[t++]);return a},_tdec:"undefined"!=typeof window&&window.TextDecoder?new window.TextDecoder:null,readUTF8:function(e,r,a){var n=t._bin._tdec;return n&&0==r&&a==e.length?n.decode(e):t._bin.readASCII(e,r,a)},readBytes:function(e,t,r){for(var a=[],n=0;n<r;n++)a.push(e[t+n]);return a},readASCIIArray:function(e,t,r){for(var a=[],n=0;n<r;n++)a.push(String.fromCharCode(e[t+n]));return a},_view:function(e){return e._dataView||(e._dataView=e.buffer?new DataView(e.buffer,e.byteOffset,e.byteLength):new DataView(new Uint8Array(e).buffer))}},t._lctf={},t._lctf.parse=function(e,r,a,n,i){var o=t._bin,s={},l=r;o.readFixed(e,r),r+=4;var h=o.readUshort(e,r);r+=2;var u=o.readUshort(e,r);r+=2;var f=o.readUshort(e,r);return r+=2,s.scriptList=t._lctf.readScriptList(e,l+h),s.featureList=t._lctf.readFeatureList(e,l+u),s.lookupList=t._lctf.readLookupList(e,l+f,i),s},t._lctf.readLookupList=function(e,r,a){var n=t._bin,i=r,o=[],s=n.readUshort(e,r);r+=2;for(var l=0;l<s;l++){var h=n.readUshort(e,r);r+=2;var u=t._lctf.readLookupTable(e,i+h,a);o.push(u)}return o},t._lctf.readLookupTable=function(e,r,a){var n=t._bin,i=r,o={tabs:[]};o.ltype=n.readUshort(e,r),r+=2,o.flag=n.readUshort(e,r),r+=2;var s=n.readUshort(e,r);r+=2;for(var l=o.ltype,h=0;h<s;h++){var u=n.readUshort(e,r);r+=2;var f=a(e,l,i+u,o);o.tabs.push(f)}return o},t._lctf.numOfOnes=function(e){for(var t=0,r=0;r<32;r++)0!=(e>>>r&1)&&t++;return t},t._lctf.readClassDef=function(e,r){var a=t._bin,n=[],i=a.readUshort(e,r);if(r+=2,1==i){var o=a.readUshort(e,r);r+=2;var s=a.readUshort(e,r);r+=2;for(var l=0;l<s;l++)n.push(o+l),n.push(o+l),n.push(a.readUshort(e,r)),r+=2}if(2==i){var h=a.readUshort(e,r);for(r+=2,l=0;l<h;l++)n.push(a.readUshort(e,r)),r+=2,n.push(a.readUshort(e,r)),r+=2,n.push(a.readUshort(e,r)),r+=2}return n},t._lctf.getInterval=function(e,t){for(var r=0;r<e.length;r+=3){var a=e[r],n=e[r+1];if(e[r+2],a<=t&&t<=n)return r}return -1},t._lctf.readCoverage=function(e,r){var a=t._bin,n={};n.fmt=a.readUshort(e,r),r+=2;var i=a.readUshort(e,r);return r+=2,1==n.fmt&&(n.tab=a.readUshorts(e,r,i)),2==n.fmt&&(n.tab=a.readUshorts(e,r,3*i)),n},t._lctf.coverageIndex=function(e,r){var a=e.tab;if(1==e.fmt)return a.indexOf(r);if(2==e.fmt){var n=t._lctf.getInterval(a,r);if(-1!=n)return a[n+2]+(r-a[n])}return -1},t._lctf.readFeatureList=function(e,r){var a=t._bin,n=r,i=[],o=a.readUshort(e,r);r+=2;for(var s=0;s<o;s++){var l=a.readASCII(e,r,4);r+=4;var h=a.readUshort(e,r);r+=2;var u=t._lctf.readFeatureTable(e,n+h);u.tag=l.trim(),i.push(u)}return i},t._lctf.readFeatureTable=function(e,r){var a=t._bin,n=r,i={},o=a.readUshort(e,r);r+=2,o>0&&(i.featureParams=n+o);var s=a.readUshort(e,r);r+=2,i.tab=[];for(var l=0;l<s;l++)i.tab.push(a.readUshort(e,r+2*l));return i},t._lctf.readScriptList=function(e,r){var a=t._bin,n=r,i={},o=a.readUshort(e,r);r+=2;for(var s=0;s<o;s++){var l=a.readASCII(e,r,4);r+=4;var h=a.readUshort(e,r);r+=2,i[l.trim()]=t._lctf.readScriptTable(e,n+h)}return i},t._lctf.readScriptTable=function(e,r){var a=t._bin,n=r,i={},o=a.readUshort(e,r);r+=2,o>0&&(i.default=t._lctf.readLangSysTable(e,n+o));var s=a.readUshort(e,r);r+=2;for(var l=0;l<s;l++){var h=a.readASCII(e,r,4);r+=4;var u=a.readUshort(e,r);r+=2,i[h.trim()]=t._lctf.readLangSysTable(e,n+u)}return i},t._lctf.readLangSysTable=function(e,r){var a=t._bin,n={};a.readUshort(e,r),r+=2,n.reqFeature=a.readUshort(e,r),r+=2;var i=a.readUshort(e,r);return r+=2,n.features=a.readUshorts(e,r,i),n},t.CFF={},t.CFF.parse=function(e,r,a){var n=t._bin;(e=new Uint8Array(e.buffer,r,a))[r=0],e[++r],e[++r],e[++r],r++;var i=[];r=t.CFF.readIndex(e,r,i);for(var o=[],s=0;s<i.length-1;s++)o.push(n.readASCII(e,r+i[s],i[s+1]-i[s]));r+=i[i.length-1];var l=[];r=t.CFF.readIndex(e,r,l);var h=[];for(s=0;s<l.length-1;s++)h.push(t.CFF.readDict(e,r+l[s],r+l[s+1]));r+=l[l.length-1];var u=h[0],f=[];r=t.CFF.readIndex(e,r,f);var d=[];for(s=0;s<f.length-1;s++)d.push(n.readASCII(e,r+f[s],f[s+1]-f[s]));if(r+=f[f.length-1],t.CFF.readSubrs(e,r,u),u.CharStrings){r=u.CharStrings,f=[],r=t.CFF.readIndex(e,r,f);var c=[];for(s=0;s<f.length-1;s++)c.push(n.readBytes(e,r+f[s],f[s+1]-f[s]));u.CharStrings=c}if(u.ROS){r=u.FDArray;var p=[];for(r=t.CFF.readIndex(e,r,p),u.FDArray=[],s=0;s<p.length-1;s++){var v=t.CFF.readDict(e,r+p[s],r+p[s+1]);t.CFF._readFDict(e,v,d),u.FDArray.push(v)}r+=p[p.length-1],r=u.FDSelect,u.FDSelect=[];var g=e[r];if(r++,3!=g)throw g;var y=n.readUshort(e,r);for(r+=2,s=0;s<y+1;s++)u.FDSelect.push(n.readUshort(e,r),e[r+2]),r+=3}return u.Encoding&&(u.Encoding=t.CFF.readEncoding(e,u.Encoding,u.CharStrings.length)),u.charset&&(u.charset=t.CFF.readCharset(e,u.charset,u.CharStrings.length)),t.CFF._readFDict(e,u,d),u},t.CFF._readFDict=function(e,r,a){var n;for(var i in r.Private&&(n=r.Private[1],r.Private=t.CFF.readDict(e,n,n+r.Private[0]),r.Private.Subrs&&t.CFF.readSubrs(e,n+r.Private.Subrs,r.Private)),r)-1!=["FamilyName","FontName","FullName","Notice","version","Copyright"].indexOf(i)&&(r[i]=a[r[i]-426+35])},t.CFF.readSubrs=function(e,r,a){var n=t._bin,i=[];r=t.CFF.readIndex(e,r,i);var o=i.length;a.Bias=o<1240?107:o<33900?1131:32768,a.Subrs=[];for(var s=0;s<i.length-1;s++)a.Subrs.push(n.readBytes(e,r+i[s],i[s+1]-i[s]))},t.CFF.tableSE=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,0,111,112,113,114,0,115,116,117,118,119,120,121,122,0,123,0,124,125,126,127,128,129,130,131,0,132,133,0,134,135,136,137,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,138,0,139,0,0,0,0,140,141,142,143,0,0,0,0,0,144,0,0,0,145,0,0,146,147,148,149,0,0,0,0],t.CFF.glyphByUnicode=function(e,t){for(var r=0;r<e.charset.length;r++)if(e.charset[r]==t)return r;return -1},t.CFF.glyphBySE=function(e,r){return r<0||r>255?-1:t.CFF.glyphByUnicode(e,t.CFF.tableSE[r])},t.CFF.readEncoding=function(e,r,a){t._bin;var n=[".notdef"],i=e[r];if(r++,0!=i)throw"error: unknown encoding format: "+i;var o=e[r];r++;for(var s=0;s<o;s++)n.push(e[r+s]);return n},t.CFF.readCharset=function(e,r,a){var n=t._bin,i=[".notdef"],o=e[r];if(r++,0==o)for(var s=0;s<a;s++){var l=n.readUshort(e,r);r+=2,i.push(l)}else{if(1!=o&&2!=o)throw"error: format: "+o;for(;i.length<a;){l=n.readUshort(e,r),r+=2;var h=0;for(1==o?(h=e[r],r++):(h=n.readUshort(e,r),r+=2),s=0;s<=h;s++)i.push(l),l++}}return i},t.CFF.readIndex=function(e,r,a){var n=t._bin,i=n.readUshort(e,r)+1,o=e[r+=2];if(r++,1==o)for(var s=0;s<i;s++)a.push(e[r+s]);else if(2==o)for(s=0;s<i;s++)a.push(n.readUshort(e,r+2*s));else if(3==o)for(s=0;s<i;s++)a.push(0xffffff&n.readUint(e,r+3*s-1));else if(1!=i)throw"unsupported offset size: "+o+", count: "+i;return(r+=i*o)-1},t.CFF.getCharString=function(e,r,a){var n=t._bin,i=e[r],o=e[r+1];e[r+2],e[r+3],e[r+4];var s=1,l=null,h=null;i<=20&&(l=i,s=1),12==i&&(l=100*i+o,s=2),21<=i&&i<=27&&(l=i,s=1),28==i&&(h=n.readShort(e,r+1),s=3),29<=i&&i<=31&&(l=i,s=1),32<=i&&i<=246&&(h=i-139,s=1),247<=i&&i<=250&&(h=256*(i-247)+o+108,s=2),251<=i&&i<=254&&(h=-(256*(i-251))-o-108,s=2),255==i&&(h=n.readInt(e,r+1)/65535,s=5),a.val=null!=h?h:"o"+l,a.size=s},t.CFF.readCharString=function(e,r,a){for(var n=r+a,i=t._bin,o=[];r<n;){var s=e[r],l=e[r+1];e[r+2],e[r+3],e[r+4];var h=1,u=null,f=null;s<=20&&(u=s,h=1),12==s&&(u=100*s+l,h=2),19!=s&&20!=s||(u=s,h=2),21<=s&&s<=27&&(u=s,h=1),28==s&&(f=i.readShort(e,r+1),h=3),29<=s&&s<=31&&(u=s,h=1),32<=s&&s<=246&&(f=s-139,h=1),247<=s&&s<=250&&(f=256*(s-247)+l+108,h=2),251<=s&&s<=254&&(f=-(256*(s-251))-l-108,h=2),255==s&&(f=i.readInt(e,r+1)/65535,h=5),o.push(null!=f?f:"o"+u),r+=h}return o},t.CFF.readDict=function(e,r,a){for(var n=t._bin,i={},o=[];r<a;){var s=e[r],l=e[r+1];e[r+2],e[r+3],e[r+4];var h=1,u=null,f=null;if(28==s&&(f=n.readShort(e,r+1),h=3),29==s&&(f=n.readInt(e,r+1),h=5),32<=s&&s<=246&&(f=s-139,h=1),247<=s&&s<=250&&(f=256*(s-247)+l+108,h=2),251<=s&&s<=254&&(f=-(256*(s-251))-l-108,h=2),255==s)throw f=n.readInt(e,r+1)/65535,h=5,"unknown number";if(30==s){var d=[];for(h=1;;){var c=e[r+h];h++;var p=c>>4,v=15&c;if(15!=p&&d.push(p),15!=v&&d.push(v),15==v)break}for(var g="",y=[0,1,2,3,4,5,6,7,8,9,".","e","e-","reserved","-","endOfNumber"],m=0;m<d.length;m++)g+=y[d[m]];f=parseFloat(g)}s<=21&&(u=["version","Notice","FullName","FamilyName","Weight","FontBBox","BlueValues","OtherBlues","FamilyBlues","FamilyOtherBlues","StdHW","StdVW","escape","UniqueID","XUID","charset","Encoding","CharStrings","Private","Subrs","defaultWidthX","nominalWidthX"][s],h=1,12==s)&&(u=["Copyright","isFixedPitch","ItalicAngle","UnderlinePosition","UnderlineThickness","PaintType","CharstringType","FontMatrix","StrokeWidth","BlueScale","BlueShift","BlueFuzz","StemSnapH","StemSnapV","ForceBold",0,0,"LanguageGroup","ExpansionFactor","initialRandomSeed","SyntheticBase","PostScript","BaseFontName","BaseFontBlend",0,0,0,0,0,0,"ROS","CIDFontVersion","CIDFontRevision","CIDFontType","CIDCount","UIDBase","FDArray","FDSelect","FontName"][l],h=2),null!=u?(i[u]=1==o.length?o[0]:o,o=[]):o.push(f),r+=h}return i},t.cmap={},t.cmap.parse=function(e,r,a){e=new Uint8Array(e.buffer,r,a),r=0;var n=t._bin,i={};n.readUshort(e,r),r+=2;var o=n.readUshort(e,r);r+=2;var s=[];i.tables=[];for(var l=0;l<o;l++){var h=n.readUshort(e,r);r+=2;var u=n.readUshort(e,r);r+=2;var f=n.readUint(e,r);r+=4;var d="p"+h+"e"+u,c=s.indexOf(f);if(-1==c){c=i.tables.length,s.push(f);var p,v=n.readUshort(e,f);0==v?p=t.cmap.parse0(e,f):4==v?p=t.cmap.parse4(e,f):6==v?p=t.cmap.parse6(e,f):12==v?p=t.cmap.parse12(e,f):console.debug("unknown format: "+v,h,u,f),i.tables.push(p)}if(null!=i[d])throw"multiple tables for one platform+encoding";i[d]=c}return i},t.cmap.parse0=function(e,r){var a=t._bin,n={};n.format=a.readUshort(e,r),r+=2;var i=a.readUshort(e,r);r+=2,a.readUshort(e,r),r+=2,n.map=[];for(var o=0;o<i-6;o++)n.map.push(e[r+o]);return n},t.cmap.parse4=function(e,r){var a=t._bin,n=r,i={};i.format=a.readUshort(e,r),r+=2;var o=a.readUshort(e,r);r+=2,a.readUshort(e,r),r+=2;var s=a.readUshort(e,r);r+=2;var l=s/2;i.searchRange=a.readUshort(e,r),r+=2,i.entrySelector=a.readUshort(e,r),r+=2,i.rangeShift=a.readUshort(e,r),r+=2,i.endCount=a.readUshorts(e,r,l),r+=2*l,r+=2,i.startCount=a.readUshorts(e,r,l),r+=2*l,i.idDelta=[];for(var h=0;h<l;h++)i.idDelta.push(a.readShort(e,r)),r+=2;for(i.idRangeOffset=a.readUshorts(e,r,l),r+=2*l,i.glyphIdArray=[];r<n+o;)i.glyphIdArray.push(a.readUshort(e,r)),r+=2;return i},t.cmap.parse6=function(e,r){var a=t._bin,n={};n.format=a.readUshort(e,r),r+=2,a.readUshort(e,r),r+=2,a.readUshort(e,r),r+=2,n.firstCode=a.readUshort(e,r),r+=2;var i=a.readUshort(e,r);r+=2,n.glyphIdArray=[];for(var o=0;o<i;o++)n.glyphIdArray.push(a.readUshort(e,r)),r+=2;return n},t.cmap.parse12=function(e,r){var a=t._bin,n={};n.format=a.readUshort(e,r),r+=2,r+=2,a.readUint(e,r),r+=4,a.readUint(e,r),r+=4;var i=a.readUint(e,r);r+=4,n.groups=[];for(var o=0;o<i;o++){var s=r+12*o,l=a.readUint(e,s+0),h=a.readUint(e,s+4),u=a.readUint(e,s+8);n.groups.push([l,h,u])}return n},t.glyf={},t.glyf.parse=function(e,t,r,a){for(var n=[],i=0;i<a.maxp.numGlyphs;i++)n.push(null);return n},t.glyf._parseGlyf=function(e,r){var a,n=t._bin,i=e._data,o=t._tabOffset(i,"glyf",e._offset)+e.loca[r];if(e.loca[r]==e.loca[r+1])return null;var s={};if(s.noc=n.readShort(i,o),o+=2,s.xMin=n.readShort(i,o),o+=2,s.yMin=n.readShort(i,o),o+=2,s.xMax=n.readShort(i,o),o+=2,s.yMax=n.readShort(i,o),o+=2,s.xMin>=s.xMax||s.yMin>=s.yMax)return null;if(s.noc>0){s.endPts=[];for(var l=0;l<s.noc;l++)s.endPts.push(n.readUshort(i,o)),o+=2;var h=n.readUshort(i,o);if(o+=2,i.length-o<h)return null;s.instructions=n.readBytes(i,o,h),o+=h;var u=s.endPts[s.noc-1]+1;for(l=0,s.flags=[];l<u;l++){var f=i[o];if(o++,s.flags.push(f),0!=(8&f)){var d=i[o];o++;for(var c=0;c<d;c++)s.flags.push(f),l++}}for(l=0,s.xs=[];l<u;l++){var p=0!=(2&s.flags[l]),v=0!=(16&s.flags[l]);p?(s.xs.push(v?i[o]:-i[o]),o++):v?s.xs.push(0):(s.xs.push(n.readShort(i,o)),o+=2)}for(l=0,s.ys=[];l<u;l++)p=0!=(4&s.flags[l]),v=0!=(32&s.flags[l]),p?(s.ys.push(v?i[o]:-i[o]),o++):v?s.ys.push(0):(s.ys.push(n.readShort(i,o)),o+=2);var g=0,y=0;for(l=0;l<u;l++)g+=s.xs[l],y+=s.ys[l],s.xs[l]=g,s.ys[l]=y}else{s.parts=[];do{a=n.readUshort(i,o),o+=2;var m={m:{a:1,b:0,c:0,d:1,tx:0,ty:0},p1:-1,p2:-1};if(s.parts.push(m),m.glyphIndex=n.readUshort(i,o),o+=2,1&a){var U=n.readShort(i,o);o+=2;var S=n.readShort(i,o);o+=2}else U=n.readInt8(i,o),o++,S=n.readInt8(i,o),o++;2&a?(m.m.tx=U,m.m.ty=S):(m.p1=U,m.p2=S),8&a?(m.m.a=m.m.d=n.readF2dot14(i,o),o+=2):64&a?(m.m.a=n.readF2dot14(i,o),o+=2,m.m.d=n.readF2dot14(i,o),o+=2):128&a&&(m.m.a=n.readF2dot14(i,o),o+=2,m.m.b=n.readF2dot14(i,o),o+=2,m.m.c=n.readF2dot14(i,o),o+=2,m.m.d=n.readF2dot14(i,o),o+=2)}while(32&a);if(256&a){var b=n.readUshort(i,o);for(o+=2,s.instr=[],l=0;l<b;l++)s.instr.push(i[o]),o++}}return s},t.GDEF={},t.GDEF.parse=function(e,r,a,n){var i=r;r+=4;var o=t._bin.readUshort(e,r);return{glyphClassDef:0===o?null:t._lctf.readClassDef(e,i+o)}},t.GPOS={},t.GPOS.parse=function(e,r,a,n){return t._lctf.parse(e,r,a,n,t.GPOS.subt)},t.GPOS.subt=function(e,r,a,n){var i=t._bin,o=a,s={};if(s.fmt=i.readUshort(e,a),a+=2,1==r||2==r||3==r||7==r||8==r&&s.fmt<=2){var l=i.readUshort(e,a);a+=2,s.coverage=t._lctf.readCoverage(e,l+o)}if(1==r&&1==s.fmt){var h=i.readUshort(e,a);a+=2,0!=h&&(s.pos=t.GPOS.readValueRecord(e,a,h))}else if(2==r&&s.fmt>=1&&s.fmt<=2){h=i.readUshort(e,a),a+=2;var u=i.readUshort(e,a);a+=2;var f=t._lctf.numOfOnes(h),d=t._lctf.numOfOnes(u);if(1==s.fmt){s.pairsets=[];var c=i.readUshort(e,a);a+=2;for(var p=0;p<c;p++){var v=o+i.readUshort(e,a);a+=2;var g=i.readUshort(e,v);v+=2;for(var y=[],m=0;m<g;m++){var U=i.readUshort(e,v);v+=2,0!=h&&(F=t.GPOS.readValueRecord(e,v,h),v+=2*f),0!=u&&(w=t.GPOS.readValueRecord(e,v,u),v+=2*d),y.push({gid2:U,val1:F,val2:w})}s.pairsets.push(y)}}if(2==s.fmt){var S=i.readUshort(e,a);a+=2;var b=i.readUshort(e,a);a+=2;var x=i.readUshort(e,a);a+=2;var k=i.readUshort(e,a);for(a+=2,s.classDef1=t._lctf.readClassDef(e,o+S),s.classDef2=t._lctf.readClassDef(e,o+b),s.matrix=[],p=0;p<x;p++){var T=[];for(m=0;m<k;m++){var F=null,w=null;0!=h&&(F=t.GPOS.readValueRecord(e,a,h),a+=2*f),0!=u&&(w=t.GPOS.readValueRecord(e,a,u),a+=2*d),T.push({val1:F,val2:w})}s.matrix.push(T)}}}else if(4==r&&1==s.fmt)s.markCoverage=t._lctf.readCoverage(e,i.readUshort(e,a)+o),s.baseCoverage=t._lctf.readCoverage(e,i.readUshort(e,a+2)+o),s.markClassCount=i.readUshort(e,a+4),s.markArray=t.GPOS.readMarkArray(e,i.readUshort(e,a+6)+o),s.baseArray=t.GPOS.readBaseArray(e,i.readUshort(e,a+8)+o,s.markClassCount);else if(6==r&&1==s.fmt)s.mark1Coverage=t._lctf.readCoverage(e,i.readUshort(e,a)+o),s.mark2Coverage=t._lctf.readCoverage(e,i.readUshort(e,a+2)+o),s.markClassCount=i.readUshort(e,a+4),s.mark1Array=t.GPOS.readMarkArray(e,i.readUshort(e,a+6)+o),s.mark2Array=t.GPOS.readBaseArray(e,i.readUshort(e,a+8)+o,s.markClassCount);else{if(9==r&&1==s.fmt){var C=i.readUshort(e,a);a+=2;var _=i.readUint(e,a);if(a+=4,9==n.ltype)n.ltype=C;else if(n.ltype!=C)throw"invalid extension substitution";return t.GPOS.subt(e,n.ltype,o+_)}console.debug("unsupported GPOS table LookupType",r,"format",s.fmt)}return s},t.GPOS.readValueRecord=function(e,r,a){var n=t._bin,i=[];return i.push(1&a?n.readShort(e,r):0),r+=1&a?2:0,i.push(2&a?n.readShort(e,r):0),r+=2&a?2:0,i.push(4&a?n.readShort(e,r):0),r+=4&a?2:0,i.push(8&a?n.readShort(e,r):0),r+=8&a?2:0,i},t.GPOS.readBaseArray=function(e,r,a){var n=t._bin,i=[],o=r,s=n.readUshort(e,r);r+=2;for(var l=0;l<s;l++){for(var h=[],u=0;u<a;u++)h.push(t.GPOS.readAnchorRecord(e,o+n.readUshort(e,r))),r+=2;i.push(h)}return i},t.GPOS.readMarkArray=function(e,r){var a=t._bin,n=[],i=r,o=a.readUshort(e,r);r+=2;for(var s=0;s<o;s++){var l=t.GPOS.readAnchorRecord(e,a.readUshort(e,r+2)+i);l.markClass=a.readUshort(e,r),n.push(l),r+=4}return n},t.GPOS.readAnchorRecord=function(e,r){var a=t._bin,n={};return n.fmt=a.readUshort(e,r),n.x=a.readShort(e,r+2),n.y=a.readShort(e,r+4),n},t.GSUB={},t.GSUB.parse=function(e,r,a,n){return t._lctf.parse(e,r,a,n,t.GSUB.subt)},t.GSUB.subt=function(e,r,a,n){var i=t._bin,o=a,s={};if(s.fmt=i.readUshort(e,a),a+=2,1!=r&&2!=r&&4!=r&&5!=r&&6!=r)return null;if(1==r||2==r||4==r||5==r&&s.fmt<=2||6==r&&s.fmt<=2){var l=i.readUshort(e,a);a+=2,s.coverage=t._lctf.readCoverage(e,o+l)}if(1==r&&s.fmt>=1&&s.fmt<=2){if(1==s.fmt)s.delta=i.readShort(e,a),a+=2;else if(2==s.fmt){var h=i.readUshort(e,a);a+=2,s.newg=i.readUshorts(e,a,h),a+=2*s.newg.length}}else if(2==r&&1==s.fmt){h=i.readUshort(e,a),a+=2,s.seqs=[];for(var u=0;u<h;u++){var f=i.readUshort(e,a)+o;a+=2;var d=i.readUshort(e,f);s.seqs.push(i.readUshorts(e,f+2,d))}}else if(4==r)for(s.vals=[],h=i.readUshort(e,a),a+=2,u=0;u<h;u++){var c=i.readUshort(e,a);a+=2,s.vals.push(t.GSUB.readLigatureSet(e,o+c))}else if(5==r&&2==s.fmt){if(2==s.fmt){var p=i.readUshort(e,a);a+=2,s.cDef=t._lctf.readClassDef(e,o+p),s.scset=[];var v=i.readUshort(e,a);for(a+=2,u=0;u<v;u++){var g=i.readUshort(e,a);a+=2,s.scset.push(0==g?null:t.GSUB.readSubClassSet(e,o+g))}}}else if(6==r&&3==s.fmt){if(3==s.fmt){for(u=0;u<3;u++){h=i.readUshort(e,a),a+=2;for(var y=[],m=0;m<h;m++)y.push(t._lctf.readCoverage(e,o+i.readUshort(e,a+2*m)));a+=2*h,0==u&&(s.backCvg=y),1==u&&(s.inptCvg=y),2==u&&(s.ahedCvg=y)}h=i.readUshort(e,a),a+=2,s.lookupRec=t.GSUB.readSubstLookupRecords(e,a,h)}}else{if(7==r&&1==s.fmt){var U=i.readUshort(e,a);a+=2;var S=i.readUint(e,a);if(a+=4,9==n.ltype)n.ltype=U;else if(n.ltype!=U)throw"invalid extension substitution";return t.GSUB.subt(e,n.ltype,o+S)}console.debug("unsupported GSUB table LookupType",r,"format",s.fmt)}return s},t.GSUB.readSubClassSet=function(e,r){var a=t._bin.readUshort,n=r,i=[],o=a(e,r);r+=2;for(var s=0;s<o;s++){var l=a(e,r);r+=2,i.push(t.GSUB.readSubClassRule(e,n+l))}return i},t.GSUB.readSubClassRule=function(e,r){var a=t._bin.readUshort,n={},i=a(e,r),o=a(e,r+=2);r+=2,n.input=[];for(var s=0;s<i-1;s++)n.input.push(a(e,r)),r+=2;return n.substLookupRecords=t.GSUB.readSubstLookupRecords(e,r,o),n},t.GSUB.readSubstLookupRecords=function(e,r,a){for(var n=t._bin.readUshort,i=[],o=0;o<a;o++)i.push(n(e,r),n(e,r+2)),r+=4;return i},t.GSUB.readChainSubClassSet=function(e,r){var a=t._bin,n=r,i=[],o=a.readUshort(e,r);r+=2;for(var s=0;s<o;s++){var l=a.readUshort(e,r);r+=2,i.push(t.GSUB.readChainSubClassRule(e,n+l))}return i},t.GSUB.readChainSubClassRule=function(e,r){for(var a=t._bin,n={},i=["backtrack","input","lookahead"],o=0;o<i.length;o++){var s=a.readUshort(e,r);r+=2,1==o&&s--,n[i[o]]=a.readUshorts(e,r,s),r+=2*n[i[o]].length}return s=a.readUshort(e,r),r+=2,n.subst=a.readUshorts(e,r,2*s),r+=2*n.subst.length,n},t.GSUB.readLigatureSet=function(e,r){var a=t._bin,n=r,i=[],o=a.readUshort(e,r);r+=2;for(var s=0;s<o;s++){var l=a.readUshort(e,r);r+=2,i.push(t.GSUB.readLigature(e,n+l))}return i},t.GSUB.readLigature=function(e,r){var a=t._bin,n={chain:[]};n.nglyph=a.readUshort(e,r),r+=2;var i=a.readUshort(e,r);r+=2;for(var o=0;o<i-1;o++)n.chain.push(a.readUshort(e,r)),r+=2;return n},t.head={},t.head.parse=function(e,r,a){var n=t._bin,i={};return n.readFixed(e,r),r+=4,i.fontRevision=n.readFixed(e,r),r+=4,n.readUint(e,r),r+=4,n.readUint(e,r),r+=4,i.flags=n.readUshort(e,r),r+=2,i.unitsPerEm=n.readUshort(e,r),r+=2,i.created=n.readUint64(e,r),r+=8,i.modified=n.readUint64(e,r),r+=8,i.xMin=n.readShort(e,r),r+=2,i.yMin=n.readShort(e,r),r+=2,i.xMax=n.readShort(e,r),r+=2,i.yMax=n.readShort(e,r),r+=2,i.macStyle=n.readUshort(e,r),r+=2,i.lowestRecPPEM=n.readUshort(e,r),r+=2,i.fontDirectionHint=n.readShort(e,r),r+=2,i.indexToLocFormat=n.readShort(e,r),r+=2,i.glyphDataFormat=n.readShort(e,r),r+=2,i},t.hhea={},t.hhea.parse=function(e,r,a){var n=t._bin,i={};return n.readFixed(e,r),r+=4,i.ascender=n.readShort(e,r),r+=2,i.descender=n.readShort(e,r),r+=2,i.lineGap=n.readShort(e,r),r+=2,i.advanceWidthMax=n.readUshort(e,r),r+=2,i.minLeftSideBearing=n.readShort(e,r),r+=2,i.minRightSideBearing=n.readShort(e,r),r+=2,i.xMaxExtent=n.readShort(e,r),r+=2,i.caretSlopeRise=n.readShort(e,r),r+=2,i.caretSlopeRun=n.readShort(e,r),r+=2,i.caretOffset=n.readShort(e,r),r+=2,r+=8,i.metricDataFormat=n.readShort(e,r),r+=2,i.numberOfHMetrics=n.readUshort(e,r),r+=2,i},t.hmtx={},t.hmtx.parse=function(e,r,a,n){for(var i=t._bin,o={aWidth:[],lsBearing:[]},s=0,l=0,h=0;h<n.maxp.numGlyphs;h++)h<n.hhea.numberOfHMetrics&&(s=i.readUshort(e,r),r+=2,l=i.readShort(e,r),r+=2),o.aWidth.push(s),o.lsBearing.push(l);return o},t.kern={},t.kern.parse=function(e,r,a,n){var i=t._bin,o=i.readUshort(e,r);if(r+=2,1==o)return t.kern.parseV1(e,r-2,a,n);var s=i.readUshort(e,r);r+=2;for(var l={glyph1:[],rval:[]},h=0;h<s;h++){r+=2,a=i.readUshort(e,r),r+=2;var u=i.readUshort(e,r);r+=2;var f=u>>>8;if(0!=(f&=15))throw"unknown kern table format: "+f;r=t.kern.readFormat0(e,r,l)}return l},t.kern.parseV1=function(e,r,a,n){var i=t._bin;i.readFixed(e,r),r+=4;var o=i.readUint(e,r);r+=4;for(var s={glyph1:[],rval:[]},l=0;l<o;l++){i.readUint(e,r),r+=4;var h=i.readUshort(e,r);r+=2,i.readUshort(e,r),r+=2;var u=h>>>8;if(0!=(u&=15))throw"unknown kern table format: "+u;r=t.kern.readFormat0(e,r,s)}return s},t.kern.readFormat0=function(e,r,a){var n=t._bin,i=-1,o=n.readUshort(e,r);r+=2,n.readUshort(e,r),r+=2,n.readUshort(e,r),r+=2,n.readUshort(e,r),r+=2;for(var s=0;s<o;s++){var l=n.readUshort(e,r);r+=2;var h=n.readUshort(e,r);r+=2;var u=n.readShort(e,r);r+=2,l!=i&&(a.glyph1.push(l),a.rval.push({glyph2:[],vals:[]}));var f=a.rval[a.rval.length-1];f.glyph2.push(h),f.vals.push(u),i=l}return r},t.loca={},t.loca.parse=function(e,r,a,n){var i=t._bin,o=[],s=n.head.indexToLocFormat,l=n.maxp.numGlyphs+1;if(0==s)for(var h=0;h<l;h++)o.push(i.readUshort(e,r+(h<<1))<<1);if(1==s)for(h=0;h<l;h++)o.push(i.readUint(e,r+(h<<2)));return o},t.maxp={},t.maxp.parse=function(e,r,a){var n=t._bin,i={},o=n.readUint(e,r);return r+=4,i.numGlyphs=n.readUshort(e,r),r+=2,65536==o&&(i.maxPoints=n.readUshort(e,r),r+=2,i.maxContours=n.readUshort(e,r),r+=2,i.maxCompositePoints=n.readUshort(e,r),r+=2,i.maxCompositeContours=n.readUshort(e,r),r+=2,i.maxZones=n.readUshort(e,r),r+=2,i.maxTwilightPoints=n.readUshort(e,r),r+=2,i.maxStorage=n.readUshort(e,r),r+=2,i.maxFunctionDefs=n.readUshort(e,r),r+=2,i.maxInstructionDefs=n.readUshort(e,r),r+=2,i.maxStackElements=n.readUshort(e,r),r+=2,i.maxSizeOfInstructions=n.readUshort(e,r),r+=2,i.maxComponentElements=n.readUshort(e,r),r+=2,i.maxComponentDepth=n.readUshort(e,r),r+=2),i},t.name={},t.name.parse=function(e,r,a){var n=t._bin,i={};n.readUshort(e,r),r+=2;var o=n.readUshort(e,r);r+=2,n.readUshort(e,r);for(var s,l=["copyright","fontFamily","fontSubfamily","ID","fullName","version","postScriptName","trademark","manufacturer","designer","description","urlVendor","urlDesigner","licence","licenceURL","---","typoFamilyName","typoSubfamilyName","compatibleFull","sampleText","postScriptCID","wwsFamilyName","wwsSubfamilyName","lightPalette","darkPalette"],h=r+=2,u=0;u<o;u++){var f=n.readUshort(e,r);r+=2;var d=n.readUshort(e,r);r+=2;var c=n.readUshort(e,r);r+=2;var p=n.readUshort(e,r);r+=2;var v=n.readUshort(e,r);r+=2;var g=n.readUshort(e,r);r+=2;var y,m=l[p],U=h+12*o+g;if(0==f)y=n.readUnicode(e,U,v/2);else if(3==f&&0==d)y=n.readUnicode(e,U,v/2);else if(0==d)y=n.readASCII(e,U,v);else if(1==d)y=n.readUnicode(e,U,v/2);else if(3==d)y=n.readUnicode(e,U,v/2);else{if(1!=f)throw"unknown encoding "+d+", platformID: "+f;y=n.readASCII(e,U,v),console.debug("reading unknown MAC encoding "+d+" as ASCII")}var S="p"+f+","+c.toString(16);null==i[S]&&(i[S]={}),i[S][void 0!==m?m:p]=y,i[S]._lang=c}for(var b in i)if(null!=i[b].postScriptName&&1033==i[b]._lang)return i[b];for(var b in i)if(null!=i[b].postScriptName&&0==i[b]._lang)return i[b];for(var b in i)if(null!=i[b].postScriptName&&3084==i[b]._lang)return i[b];for(var b in i)if(null!=i[b].postScriptName)return i[b];for(var b in i){s=b;break}return console.debug("returning name table with languageID "+i[s]._lang),i[s]},t["OS/2"]={},t["OS/2"].parse=function(e,r,a){var n=t._bin.readUshort(e,r);r+=2;var i={};if(0==n)t["OS/2"].version0(e,r,i);else if(1==n)t["OS/2"].version1(e,r,i);else if(2==n||3==n||4==n)t["OS/2"].version2(e,r,i);else{if(5!=n)throw"unknown OS/2 table version: "+n;t["OS/2"].version5(e,r,i)}return i},t["OS/2"].version0=function(e,r,a){var n=t._bin;return a.xAvgCharWidth=n.readShort(e,r),r+=2,a.usWeightClass=n.readUshort(e,r),r+=2,a.usWidthClass=n.readUshort(e,r),r+=2,a.fsType=n.readUshort(e,r),r+=2,a.ySubscriptXSize=n.readShort(e,r),r+=2,a.ySubscriptYSize=n.readShort(e,r),r+=2,a.ySubscriptXOffset=n.readShort(e,r),r+=2,a.ySubscriptYOffset=n.readShort(e,r),r+=2,a.ySuperscriptXSize=n.readShort(e,r),r+=2,a.ySuperscriptYSize=n.readShort(e,r),r+=2,a.ySuperscriptXOffset=n.readShort(e,r),r+=2,a.ySuperscriptYOffset=n.readShort(e,r),r+=2,a.yStrikeoutSize=n.readShort(e,r),r+=2,a.yStrikeoutPosition=n.readShort(e,r),r+=2,a.sFamilyClass=n.readShort(e,r),r+=2,a.panose=n.readBytes(e,r,10),r+=10,a.ulUnicodeRange1=n.readUint(e,r),r+=4,a.ulUnicodeRange2=n.readUint(e,r),r+=4,a.ulUnicodeRange3=n.readUint(e,r),r+=4,a.ulUnicodeRange4=n.readUint(e,r),r+=4,a.achVendID=[n.readInt8(e,r),n.readInt8(e,r+1),n.readInt8(e,r+2),n.readInt8(e,r+3)],r+=4,a.fsSelection=n.readUshort(e,r),r+=2,a.usFirstCharIndex=n.readUshort(e,r),r+=2,a.usLastCharIndex=n.readUshort(e,r),r+=2,a.sTypoAscender=n.readShort(e,r),r+=2,a.sTypoDescender=n.readShort(e,r),r+=2,a.sTypoLineGap=n.readShort(e,r),r+=2,a.usWinAscent=n.readUshort(e,r),r+=2,a.usWinDescent=n.readUshort(e,r),r+=2},t["OS/2"].version1=function(e,r,a){var n=t._bin;return r=t["OS/2"].version0(e,r,a),a.ulCodePageRange1=n.readUint(e,r),r+=4,a.ulCodePageRange2=n.readUint(e,r),r+=4},t["OS/2"].version2=function(e,r,a){var n=t._bin;return r=t["OS/2"].version1(e,r,a),a.sxHeight=n.readShort(e,r),r+=2,a.sCapHeight=n.readShort(e,r),r+=2,a.usDefault=n.readUshort(e,r),r+=2,a.usBreak=n.readUshort(e,r),r+=2,a.usMaxContext=n.readUshort(e,r),r+=2},t["OS/2"].version5=function(e,r,a){var n=t._bin;return r=t["OS/2"].version2(e,r,a),a.usLowerOpticalPointSize=n.readUshort(e,r),r+=2,a.usUpperOpticalPointSize=n.readUshort(e,r),r+=2},t.post={},t.post.parse=function(e,r,a){var n=t._bin,i={};return i.version=n.readFixed(e,r),r+=4,i.italicAngle=n.readFixed(e,r),r+=4,i.underlinePosition=n.readShort(e,r),r+=2,i.underlineThickness=n.readShort(e,r),r+=2,i},null==t&&(t={}),null==t.U&&(t.U={}),t.U.codeToGlyph=function(e,t){var r=e.cmap,a=-1;if(null!=r.p0e4?a=r.p0e4:null!=r.p3e1?a=r.p3e1:null!=r.p1e0?a=r.p1e0:null!=r.p0e3&&(a=r.p0e3),-1==a)throw"no familiar platform and encoding!";var n=r.tables[a];if(0==n.format)return t>=n.map.length?0:n.map[t];if(4==n.format){for(var i=-1,o=0;o<n.endCount.length;o++)if(t<=n.endCount[o]){i=o;break}return -1==i||n.startCount[i]>t?0:65535&(0!=n.idRangeOffset[i]?n.glyphIdArray[t-n.startCount[i]+(n.idRangeOffset[i]>>1)-(n.idRangeOffset.length-i)]:t+n.idDelta[i])}if(12==n.format){if(t>n.groups[n.groups.length-1][1])return 0;for(o=0;o<n.groups.length;o++){var s=n.groups[o];if(s[0]<=t&&t<=s[1])return s[2]+(t-s[0])}return 0}throw"unknown cmap table format "+n.format},t.U.glyphToPath=function(e,r){var a={cmds:[],crds:[]};if(e.SVG&&e.SVG.entries[r]){var n=e.SVG.entries[r];return null==n?a:("string"==typeof n&&(n=t.SVG.toPath(n),e.SVG.entries[r]=n),n)}if(e.CFF){var i={x:0,y:0,stack:[],nStems:0,haveWidth:!1,width:e.CFF.Private?e.CFF.Private.defaultWidthX:0,open:!1},o=e.CFF,s=e.CFF.Private;if(o.ROS){for(var l=0;o.FDSelect[l+2]<=r;)l+=2;s=o.FDArray[o.FDSelect[l+1]].Private}t.U._drawCFF(e.CFF.CharStrings[r],i,o,s,a)}else e.glyf&&t.U._drawGlyf(r,e,a);return a},t.U._drawGlyf=function(e,r,a){var n=r.glyf[e];null==n&&(n=r.glyf[e]=t.glyf._parseGlyf(r,e)),null!=n&&(n.noc>-1?t.U._simpleGlyph(n,a):t.U._compoGlyph(n,r,a))},t.U._simpleGlyph=function(e,r){for(var a=0;a<e.noc;a++){for(var n=0==a?0:e.endPts[a-1]+1,i=e.endPts[a],o=n;o<=i;o++){var s=o==n?i:o-1,l=o==i?n:o+1,h=1&e.flags[o],u=1&e.flags[s],f=1&e.flags[l],d=e.xs[o],c=e.ys[o];if(o==n)if(h){if(!u){t.U.P.moveTo(r,d,c);continue}t.U.P.moveTo(r,e.xs[s],e.ys[s])}else u?t.U.P.moveTo(r,e.xs[s],e.ys[s]):t.U.P.moveTo(r,(e.xs[s]+d)/2,(e.ys[s]+c)/2);h?u&&t.U.P.lineTo(r,d,c):f?t.U.P.qcurveTo(r,d,c,e.xs[l],e.ys[l]):t.U.P.qcurveTo(r,d,c,(d+e.xs[l])/2,(c+e.ys[l])/2)}t.U.P.closePath(r)}},t.U._compoGlyph=function(e,r,a){for(var n=0;n<e.parts.length;n++){var i={cmds:[],crds:[]},o=e.parts[n];t.U._drawGlyf(o.glyphIndex,r,i);for(var s=o.m,l=0;l<i.crds.length;l+=2){var h=i.crds[l],u=i.crds[l+1];a.crds.push(h*s.a+u*s.b+s.tx),a.crds.push(h*s.c+u*s.d+s.ty)}for(l=0;l<i.cmds.length;l++)a.cmds.push(i.cmds[l])}},t.U._getGlyphClass=function(e,r){var a=t._lctf.getInterval(r,e);return -1==a?0:r[a+2]},t.U._applySubs=function(e,r,a,n){for(var i=e.length-r-1,o=0;o<a.tabs.length;o++)if(null!=a.tabs[o]){var s,l=a.tabs[o];if(!l.coverage||-1!=(s=t._lctf.coverageIndex(l.coverage,e[r]))){if(1==a.ltype)e[r],1==l.fmt?e[r]=e[r]+l.delta:e[r]=l.newg[s];else if(4==a.ltype)for(var h=l.vals[s],u=0;u<h.length;u++){var f=h[u],d=f.chain.length;if(!(d>i)){for(var c=!0,p=0,v=0;v<d;v++){for(;-1==e[r+p+(1+v)];)p++;f.chain[v]!=e[r+p+(1+v)]&&(c=!1)}if(c){for(v=0,e[r]=f.nglyph;v<d+p;v++)e[r+v+1]=-1;break}}}else if(5==a.ltype&&2==l.fmt)for(var g=t._lctf.getInterval(l.cDef,e[r]),y=l.cDef[g+2],m=l.scset[y],U=0;U<m.length;U++){var S=m[U],b=S.input;if(!(b.length>i)){for(c=!0,v=0;v<b.length;v++){var x=t._lctf.getInterval(l.cDef,e[r+1+v]);if(-1==g&&l.cDef[x+2]!=b[v]){c=!1;break}}if(c){var k=S.substLookupRecords;for(u=0;u<k.length;u+=2)k[u],k[u+1]}}}else if(6==a.ltype&&3==l.fmt){if(!t.U._glsCovered(e,l.backCvg,r-l.backCvg.length)||!t.U._glsCovered(e,l.inptCvg,r)||!t.U._glsCovered(e,l.ahedCvg,r+l.inptCvg.length))continue;var T=l.lookupRec;for(U=0;U<T.length;U+=2){g=T[U];var F=n[T[U+1]];t.U._applySubs(e,r+g,F,n)}}}}},t.U._glsCovered=function(e,r,a){for(var n=0;n<r.length;n++)if(-1==t._lctf.coverageIndex(r[n],e[a+n]))return!1;return!0},t.U.glyphsToPath=function(e,r,a){for(var n={cmds:[],crds:[]},i=0,o=0;o<r.length;o++){var s=r[o];if(-1!=s){for(var l=o<r.length-1&&-1!=r[o+1]?r[o+1]:0,h=t.U.glyphToPath(e,s),u=0;u<h.crds.length;u+=2)n.crds.push(h.crds[u]+i),n.crds.push(h.crds[u+1]);for(a&&n.cmds.push(a),u=0;u<h.cmds.length;u++)n.cmds.push(h.cmds[u]);a&&n.cmds.push("X"),i+=e.hmtx.aWidth[s],o<r.length-1&&(i+=t.U.getPairAdjustment(e,s,l))}}return n},t.U.P={},t.U.P.moveTo=function(e,t,r){e.cmds.push("M"),e.crds.push(t,r)},t.U.P.lineTo=function(e,t,r){e.cmds.push("L"),e.crds.push(t,r)},t.U.P.curveTo=function(e,t,r,a,n,i,o){e.cmds.push("C"),e.crds.push(t,r,a,n,i,o)},t.U.P.qcurveTo=function(e,t,r,a,n){e.cmds.push("Q"),e.crds.push(t,r,a,n)},t.U.P.closePath=function(e){e.cmds.push("Z")},t.U._drawCFF=function(e,r,a,n,i){for(var o=r.stack,s=r.nStems,l=r.haveWidth,h=r.width,u=r.open,f=0,d=r.x,c=r.y,p=0,v=0,g=0,y=0,m=0,U=0,S=0,b=0,x=0,k=0,T={val:0,size:0};f<e.length;){t.CFF.getCharString(e,f,T);var F=T.val;if(f+=T.size,"o1"==F||"o18"==F)o.length%2==0||l||(h=o.shift()+n.nominalWidthX),s+=o.length>>1,o.length=0,l=!0;else if("o3"==F||"o23"==F)o.length%2==0||l||(h=o.shift()+n.nominalWidthX),s+=o.length>>1,o.length=0,l=!0;else if("o4"==F)o.length>1&&!l&&(h=o.shift()+n.nominalWidthX,l=!0),u&&t.U.P.closePath(i),c+=o.pop(),t.U.P.moveTo(i,d,c),u=!0;else if("o5"==F)for(;o.length>0;)d+=o.shift(),c+=o.shift(),t.U.P.lineTo(i,d,c);else if("o6"==F||"o7"==F)for(var w=o.length,C="o6"==F,_=0;_<w;_++){var D=o.shift();C?d+=D:c+=D,C=!C,t.U.P.lineTo(i,d,c)}else if("o8"==F||"o24"==F){w=o.length;for(var A=0;A+6<=w;)p=d+o.shift(),v=c+o.shift(),g=p+o.shift(),y=v+o.shift(),d=g+o.shift(),c=y+o.shift(),t.U.P.curveTo(i,p,v,g,y,d,c),A+=6;"o24"==F&&(d+=o.shift(),c+=o.shift(),t.U.P.lineTo(i,d,c))}else{if("o11"==F)break;if("o1234"==F||"o1235"==F||"o1236"==F||"o1237"==F)"o1234"==F&&(v=c,g=(p=d+o.shift())+o.shift(),k=y=v+o.shift(),U=y,b=c,d=(S=(m=(x=g+o.shift())+o.shift())+o.shift())+o.shift(),t.U.P.curveTo(i,p,v,g,y,x,k),t.U.P.curveTo(i,m,U,S,b,d,c)),"o1235"==F&&(p=d+o.shift(),v=c+o.shift(),g=p+o.shift(),y=v+o.shift(),x=g+o.shift(),k=y+o.shift(),m=x+o.shift(),U=k+o.shift(),S=m+o.shift(),b=U+o.shift(),d=S+o.shift(),c=b+o.shift(),o.shift(),t.U.P.curveTo(i,p,v,g,y,x,k),t.U.P.curveTo(i,m,U,S,b,d,c)),"o1236"==F&&(p=d+o.shift(),v=c+o.shift(),g=p+o.shift(),k=y=v+o.shift(),U=y,S=(m=(x=g+o.shift())+o.shift())+o.shift(),b=U+o.shift(),d=S+o.shift(),t.U.P.curveTo(i,p,v,g,y,x,k),t.U.P.curveTo(i,m,U,S,b,d,c)),"o1237"==F&&(p=d+o.shift(),v=c+o.shift(),g=p+o.shift(),y=v+o.shift(),x=g+o.shift(),k=y+o.shift(),m=x+o.shift(),U=k+o.shift(),Math.abs((S=m+o.shift())-d)>Math.abs((b=U+o.shift())-c)?d=S+o.shift():c=b+o.shift(),t.U.P.curveTo(i,p,v,g,y,x,k),t.U.P.curveTo(i,m,U,S,b,d,c));else if("o14"==F){if(o.length>0&&!l&&(h=o.shift()+a.nominalWidthX,l=!0),4==o.length){var G=o.shift(),P=o.shift(),O=o.shift(),I=o.shift(),B=t.CFF.glyphBySE(a,O),R=t.CFF.glyphBySE(a,I);t.U._drawCFF(a.CharStrings[B],r,a,n,i),r.x=G,r.y=P,t.U._drawCFF(a.CharStrings[R],r,a,n,i)}u&&(t.U.P.closePath(i),u=!1)}else if("o19"==F||"o20"==F)o.length%2==0||l||(h=o.shift()+n.nominalWidthX),s+=o.length>>1,o.length=0,l=!0,f+=s+7>>3;else if("o21"==F)o.length>2&&!l&&(h=o.shift()+n.nominalWidthX,l=!0),c+=o.pop(),d+=o.pop(),u&&t.U.P.closePath(i),t.U.P.moveTo(i,d,c),u=!0;else if("o22"==F)o.length>1&&!l&&(h=o.shift()+n.nominalWidthX,l=!0),d+=o.pop(),u&&t.U.P.closePath(i),t.U.P.moveTo(i,d,c),u=!0;else if("o25"==F){for(;o.length>6;)d+=o.shift(),c+=o.shift(),t.U.P.lineTo(i,d,c);p=d+o.shift(),v=c+o.shift(),g=p+o.shift(),y=v+o.shift(),d=g+o.shift(),c=y+o.shift(),t.U.P.curveTo(i,p,v,g,y,d,c)}else if("o26"==F)for(o.length%2&&(d+=o.shift());o.length>0;)p=d,v=c+o.shift(),d=g=p+o.shift(),c=(y=v+o.shift())+o.shift(),t.U.P.curveTo(i,p,v,g,y,d,c);else if("o27"==F)for(o.length%2&&(c+=o.shift());o.length>0;)v=c,g=(p=d+o.shift())+o.shift(),y=v+o.shift(),d=g+o.shift(),c=y,t.U.P.curveTo(i,p,v,g,y,d,c);else if("o10"==F||"o29"==F){var M="o10"==F?n:a;if(0==o.length)console.debug("error: empty stack");else{var E=o.pop(),L=M.Subrs[E+M.Bias];r.x=d,r.y=c,r.nStems=s,r.haveWidth=l,r.width=h,r.open=u,t.U._drawCFF(L,r,a,n,i),d=r.x,c=r.y,s=r.nStems,l=r.haveWidth,h=r.width,u=r.open}}else if("o30"==F||"o31"==F){var W=o.length,z=(A=0,"o31"==F);for(A+=W-(w=-3&W);A<w;)z?(v=c,g=(p=d+o.shift())+o.shift(),c=(y=v+o.shift())+o.shift(),w-A==5?(d=g+o.shift(),A++):d=g,z=!1):(p=d,v=c+o.shift(),g=p+o.shift(),y=v+o.shift(),d=g+o.shift(),w-A==5?(c=y+o.shift(),A++):c=y,z=!0),t.U.P.curveTo(i,p,v,g,y,d,c),A+=4}else{if("o"==(F+"").charAt(0))throw console.debug("Unknown operation: "+F,e),F;o.push(F)}}}r.x=d,r.y=c,r.nStems=s,r.haveWidth=l,r.width=h,r.open=u},e.Typr=r=t,e.default={Typr:r},Object.defineProperty(e,"__esModule",{value:!0}),e).Typr},function(){return function(e){var t=Uint8Array,r=Uint16Array,a=Uint32Array,n=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),o=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(e,t){for(var n=new r(31),i=0;i<31;++i)n[i]=t+=1<<e[i-1];var o=new a(n[30]);for(i=1;i<30;++i)for(var s=n[i];s<n[i+1];++s)o[s]=s-n[i]<<5|i;return[n,o]},l=s(n,2),h=l[0],u=l[1];h[28]=258,u[258]=28;for(var f=s(i,0)[0],d=new r(32768),c=0;c<32768;++c){var p=(43690&c)>>>1|(21845&c)<<1;p=(61680&(p=(52428&p)>>>2|(13107&p)<<2))>>>4|(3855&p)<<4,d[c]=((65280&p)>>>8|(255&p)<<8)>>>1}var v=function(e,t,a){for(var n=e.length,i=0,o=new r(t);i<n;++i)++o[e[i]-1];var s,l=new r(t);for(i=0;i<t;++i)l[i]=l[i-1]+o[i-1]<<1;if(a){s=new r(1<<t);var h=15-t;for(i=0;i<n;++i)if(e[i])for(var u=i<<4|e[i],f=t-e[i],c=l[e[i]-1]++<<f,p=c|(1<<f)-1;c<=p;++c)s[d[c]>>>h]=u}else for(s=new r(n),i=0;i<n;++i)e[i]&&(s[i]=d[l[e[i]-1]++]>>>15-e[i]);return s},g=new t(288);for(c=0;c<144;++c)g[c]=8;for(c=144;c<256;++c)g[c]=9;for(c=256;c<280;++c)g[c]=7;for(c=280;c<288;++c)g[c]=8;var y=new t(32);for(c=0;c<32;++c)y[c]=5;var m=v(g,9,1),U=v(y,5,1),S=function(e){for(var t=e[0],r=1;r<e.length;++r)e[r]>t&&(t=e[r]);return t},b=function(e,t,r){var a=t/8|0;return(e[a]|e[a+1]<<8)>>(7&t)&r},x=function(e,t){var r=t/8|0;return(e[r]|e[r+1]<<8|e[r+2]<<16)>>(7&t)},k=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],T=function(e,t,r){var a=Error(t||k[e]);if(a.code=e,Error.captureStackTrace&&Error.captureStackTrace(a,T),!r)throw a;return a},F=function(e,s,l){var u,d,c,p,g=e.length;if(!g||l&&!l.l&&g<5)return s||new t(0);var y=!s||l,k=!l||l.i;l||(l={}),s||(s=new t(3*g));var F,w=function(e){var r=s.length;if(e>r){var a=new t(Math.max(2*r,e));a.set(s),s=a}},C=l.f||0,_=l.p||0,D=l.b||0,A=l.l,G=l.d,P=l.m,O=l.n,I=8*g;do{if(!A){l.f=C=b(e,_,1);var B=b(e,_+1,3);if(_+=3,!B){var R=e[(X=((F=_)/8|0)+(7&F&&1)+4)-4]|e[X-3]<<8,M=X+R;if(M>g){k&&T(0);break}y&&w(D+R),s.set(e.subarray(X,M),D),l.b=D+=R,l.p=_=8*M;continue}if(1==B)A=m,G=U,P=9,O=5;else if(2==B){var E=b(e,_,31)+257,L=b(e,_+10,15)+4,W=E+b(e,_+5,31)+1;_+=14;for(var z=new t(W),V=new t(19),j=0;j<L;++j)V[o[j]]=b(e,_+3*j,7);_+=3*L;var N=S(V),H=(1<<N)-1,q=v(V,N,1);for(j=0;j<W;){var X,Y=q[b(e,_,H)];if(_+=15&Y,(X=Y>>>4)<16)z[j++]=X;else{var $=0,Q=0;for(16==X?(Q=3+b(e,_,3),_+=2,$=z[j-1]):17==X?(Q=3+b(e,_,7),_+=3):18==X&&(Q=11+b(e,_,127),_+=7);Q--;)z[j++]=$}}var Z=z.subarray(0,E),J=z.subarray(E);P=S(Z),O=S(J),A=v(Z,P,1),G=v(J,O,1)}else T(1);if(_>I){k&&T(0);break}}y&&w(D+131072);for(var K=(1<<P)-1,ee=(1<<O)-1,et=_;;et=_){var er=($=A[x(e,_)&K])>>>4;if((_+=15&$)>I){k&&T(0);break}if($||T(2),er<256)s[D++]=er;else{if(256==er){et=_,A=null;break}var ea=er-254;if(er>264){var en=n[j=er-257];ea=b(e,_,(1<<en)-1)+h[j],_+=en}var ei=G[x(e,_)&ee],eo=ei>>>4;if(ei||T(3),_+=15&ei,J=f[eo],eo>3&&(en=i[eo],J+=x(e,_)&(1<<en)-1,_+=en),_>I){k&&T(0);break}y&&w(D+131072);for(var es=D+ea;D<es;D+=4)s[D]=s[D-J],s[D+1]=s[D+1-J],s[D+2]=s[D+2-J],s[D+3]=s[D+3-J];D=es}}l.l=A,l.p=et,l.b=D,A&&(C=1,l.m=P,l.d=G,l.n=O)}while(!C);return D==s.length?s:(u=s,(d=0)<0&&(d=0),(null==(c=D)||c>u.length)&&(c=u.length),(p=new(u instanceof r?r:u instanceof a?a:t)(c-d)).set(u.subarray(d,c)),p)},w=new t(0),C="undefined"!=typeof TextDecoder&&new TextDecoder;try{C.decode(w,{stream:!0})}catch(e){}return e.convert_streams=function(e){var t=new DataView(e),r=0;function a(){var e=t.getUint16(r);return r+=2,e}function n(){var e=t.getUint32(r);return r+=4,e}function i(e){y.setUint16(m,e),m+=2}function o(e){y.setUint32(m,e),m+=4}for(var s={signature:n(),flavor:n(),length:n(),numTables:a(),reserved:a(),totalSfntSize:n(),majorVersion:a(),minorVersion:a(),metaOffset:n(),metaLength:n(),metaOrigLength:n(),privOffset:n(),privLength:n()},l=0;Math.pow(2,l)<=s.numTables;)l++;l--;for(var h=16*Math.pow(2,l),u=16*s.numTables-h,f=12,d=[],c=0;c<s.numTables;c++)d.push({tag:n(),offset:n(),compLength:n(),origLength:n(),origChecksum:n()}),f+=16;var p,v=new Uint8Array(12+16*d.length+d.reduce(function(e,t){return e+t.origLength+4},0)),g=v.buffer,y=new DataView(g),m=0;return o(s.flavor),i(s.numTables),i(h),i(l),i(u),d.forEach(function(e){o(e.tag),o(e.origChecksum),o(f),o(e.origLength),e.outOffset=f,(f+=e.origLength)%4!=0&&(f+=4-f%4)}),d.forEach(function(t){var r=e.slice(t.offset,t.offset+t.compLength);if(t.compLength!=t.origLength){var a=new Uint8Array(t.origLength);F(new Uint8Array(r,2),a)}else a=new Uint8Array(r);v.set(a,t.outOffset);var n=0;(f=t.outOffset+t.origLength)%4!=0&&(n=4-f%4),v.set(new Uint8Array(n).buffer,t.outOffset+t.origLength),p=f+n}),g.slice(0,p)},Object.defineProperty(e,"__esModule",{value:!0}),e}({}).convert_streams},function(e,t){let r,a={M:2,L:2,Q:4,C:6,Z:0},n={C:"18g,ca,368,1kz",D:"17k,6,2,2+4,5+c,2+6,2+1,10+1,9+f,j+11,2+1,a,2,2+1,15+2,3,j+2,6+3,2+8,2,2,2+1,w+a,4+e,3+3,2,3+2,3+5,23+w,2f+4,3,2+9,2,b,2+3,3,1k+9,6+1,3+1,2+2,2+d,30g,p+y,1,1+1g,f+x,2,sd2+1d,jf3+4,f+3,2+4,2+2,b+3,42,2,4+2,2+1,2,3,t+1,9f+w,2,el+2,2+g,d+2,2l,2+1,5,3+1,2+1,2,3,6,16wm+1v",R:"17m+3,2,2,6+3,m,15+2,2+2,h+h,13,3+8,2,2,3+1,2,p+1,x,5+4,5,a,2,2,3,u,c+2,g+1,5,2+1,4+1,5j,6+1,2,b,2+2,f,2+1,1s+2,2,3+1,7,1ez0,2,2+1,4+4,b,4,3,b,42,2+2,4,3,2+1,2,o+3,ae,ep,x,2o+2,3+1,3,5+1,6",L:"x9u,jff,a,fd,jv",T:"4t,gj+33,7o+4,1+1,7c+18,2,2+1,2+1,2,21+a,2,1b+k,h,2u+6,3+5,3+1,2+3,y,2,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,3,7,6+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+d,1,1+1,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,ek,3+1,r+4,1e+4,6+5,2p+c,1+3,1,1+2,1+b,2db+2,3y,2p+v,ff+3,30+1,n9x,1+2,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,5s,6y+2,ea,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+9,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2,2b+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,470+8,at4+4,1o+6,t5,1s+3,2a,f5l+1,2+3,43o+2,a+7,1+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,1,gzau,v+2n,3l+6n"},i=[null,"isol","init","fina","medi"];function o(t,r){let a=t.GDEF&&t.GDEF.glyphClassDef;return a?e.U._getGlyphClass(r,a):0}function s(...e){for(let t=0;t<e.length;t++)if("number"==typeof e[t])return e[t]}return function(l){let h=new Uint8Array(l,0,4),u=e._bin.readASCII(h,0,4);if("wOFF"===u)l=t(l);else if("wOF2"===u)throw Error("woff2 fonts not supported");return function(t){let l=Object.create(null),h=t["OS/2"],u=t.hhea,f=t.head.unitsPerEm,d=s(h&&h.sTypoAscender,u&&u.ascender,f),c={unitsPerEm:f,ascender:d,descender:s(h&&h.sTypoDescender,u&&u.descender,0),capHeight:s(h&&h.sCapHeight,d),xHeight:s(h&&h.sxHeight,d),lineGap:s(h&&h.sTypoLineGap,u&&u.lineGap),supportsCodePoint:r=>e.U.codeToGlyph(t,r)>0,forEachGlyph(s,h,u,f){let d=0,p=1/c.unitsPerEm*h,v=function(t,a){let o=[];for(let r=0;r<a.length;r++){let n=a.codePointAt(r);n>65535&&r++,o.push(e.U.codeToGlyph(t,n))}let s=t.GSUB;if(s){let t,{lookupList:l,featureList:h}=s,u=/^(rlig|liga|mset|isol|init|fina|medi|half|pres|blws|ccmp)$/,f=[];h.forEach(s=>{if(u.test(s.tag))for(let h=0;h<s.tab.length;h++){if(f[s.tab[h]])continue;f[s.tab[h]]=!0;let u=l[s.tab[h]],d=/^(isol|init|fina|medi)$/.test(s.tag);d&&!t&&(t=function(e){let t=new Uint8Array(e.length),a=32,i=1,o=-1;for(let s=0;s<e.length;s++){let l=e.codePointAt(s),h=0|function(e){if(!r){let e={R:2,L:1,D:4,C:16,U:32,T:8};for(let t in r=new Map,n){let a=0;n[t].split(",").forEach(n=>{let[i,o]=n.split("+");i=parseInt(i,36),o=o?parseInt(o,36):0,r.set(a+=i,e[t]);for(let n=o;n--;)r.set(++a,e[t])})}}return r.get(e)||32}(l),u=1;!(8&h)&&(21&a?22&h?(u=3,(1===i||3===i)&&t[o]++):33&h&&(2===i||4===i)&&t[o]--:34&a&&(2===i||4===i)&&t[o]--,i=t[s]=u,a=h,o=s,l>65535&&s++)}return t}(a));for(let r=0;r<o.length;r++)t&&d&&i[t[r]]!==s.tag||e.U._applySubs(o,r,u,l)}})}return o}(t,s),g=0,y=function(t,r){let a=new Int16Array(3*r.length),n=0;for(;n<r.length;n++){let h=r[n];if(-1===h)continue;a[3*n+2]=t.hmtx.aWidth[h];let u=t.GPOS;if(u){let f=u.lookupList;for(let u=0;u<f.length;u++){let d=f[u];for(let u=0;u<d.tabs.length;u++){let f=d.tabs[u];if(1===d.ltype){if(-1!==e._lctf.coverageIndex(f.coverage,h)&&f.pos){l(f.pos,n);break}}else if(2===d.ltype){let t=null,a=i();if(-1!==a){let i=e._lctf.coverageIndex(f.coverage,r[a]);if(-1!==i){if(1===f.fmt){let e=f.pairsets[i];for(let r=0;r<e.length;r++)e[r].gid2===h&&(t=e[r])}else if(2===f.fmt){let n=e.U._getGlyphClass(r[a],f.classDef1),i=e.U._getGlyphClass(h,f.classDef2);t=f.matrix[n][i]}if(t){t.val1&&l(t.val1,a),t.val2&&l(t.val2,n);break}}}}else if(4===d.ltype){let t=e._lctf.coverageIndex(f.markCoverage,h);if(-1!==t){let o=i(s),l=-1===o?-1:e._lctf.coverageIndex(f.baseCoverage,r[o]);if(-1!==l){let e=f.markArray[t],r=f.baseArray[l][e.markClass];a[3*n]=r.x-e.x+a[3*o]-a[3*o+2],a[3*n+1]=r.y-e.y+a[3*o+1];break}}}else if(6===d.ltype){let s=e._lctf.coverageIndex(f.mark1Coverage,h);if(-1!==s){let l=i();if(-1!==l){let i=r[l];if(3===o(t,i)){let t=e._lctf.coverageIndex(f.mark2Coverage,i);if(-1!==t){let e=f.mark1Array[s],r=f.mark2Array[t][e.markClass];a[3*n]=r.x-e.x+a[3*l]-a[3*l+2],a[3*n+1]=r.y-e.y+a[3*l+1];break}}}}}}}}else if(t.kern&&!t.cff){let e=i();if(-1!==e){let n=t.kern.glyph1.indexOf(r[e]);if(-1!==n){let r=t.kern.rval[n].glyph2.indexOf(h);-1!==r&&(a[3*e+2]+=t.kern.rval[n].vals[r])}}}}return a;function i(e){for(let t=n-1;t>=0;t--)if(-1!==r[t]&&(!e||e(r[t])))return t;return -1}function s(e){return 1===o(t,e)}function l(e,t){for(let r=0;r<3;r++)a[3*t+r]+=e[r]||0}}(t,v);return v.forEach((r,n)=>{if(-1!==r){let i=l[r];if(!i){let n,o,s,h,{cmds:u,crds:f}=e.U.glyphToPath(t,r),d="",c=0;for(let e=0,t=u.length;e<t;e++){let t=a[u[e]];d+=u[e];for(let e=1;e<=t;e++)d+=(e>1?",":"")+f[c++]}if(f.length){n=o=1/0,s=h=-1/0;for(let e=0,t=f.length;e<t;e+=2){let t=f[e],r=f[e+1];t<n&&(n=t),r<o&&(o=r),t>s&&(s=t),r>h&&(h=r)}}else n=s=o=h=0;i=l[r]={index:r,advanceWidth:t.hmtx.aWidth[r],xMin:n,yMin:o,xMax:s,yMax:h,path:d}}f.call(null,i,d+y[3*n]*p,y[3*n+1]*p,g),d+=y[3*n+2]*p,u&&(d+=u*h)}g+=s.codePointAt(g)>65535?2:1}),d}};return c}(e.parse(l)[0])}}],init:(e,t,r)=>r(e(),t())}),f=(0,o.kl)({name:"FontResolver",dependencies:[function(e,t){let r=Object.create(null),a=Object.create(null);function n(t,n){let i=r[t];if(i)n(i);else if(a[t])a[t].push(n);else{a[t]=[n];let i=e=>{console.error(`Failure loading font ${t}`,e)};try{let n=new XMLHttpRequest;n.open("get",t,!0),n.responseType="arraybuffer",n.onload=function(){if(n.status>=400)i(Error(n.statusText));else if(n.status>0)try{var o;let i=e(n.response);i.src=t,(o=i).src=t,r[t]=o,a[t].forEach(e=>e(o)),delete a[t]}catch(e){i(e)}},n.onerror=i,n.send()}catch(e){i(e)}}}return function(e,a,{lang:i,fonts:o=[],style:s="normal",weight:l="normal",unicodeFontsURL:h}={}){let u=new Uint8Array(e.length),f=[];e.length||v();let d=new Map,c=[];if("italic"!==s&&(s="normal"),"number"!=typeof l&&(l="bold"===l?700:400),o&&!Array.isArray(o)&&(o=[o]),(o=o.slice().filter(e=>!e.lang||e.lang.test(i)).reverse()).length){let t=0;!function a(i=0){for(let s=i,l=e.length;s<l;s++){let i=e.codePointAt(s);if(1===t&&f[u[s-1]].supportsCodePoint(i)||s>0&&/\s/.test(e[s]))u[s]=u[s-1],2===t&&(c[c.length-1][1]=s);else for(let e=u[s],l=o.length;e<=l;e++)if(e===l)(2===t?c[c.length-1]:c[c.length]=[s,s])[1]=s,t=2;else{u[s]=e;let{src:l,unicodeRange:h}=o[e];if(!h||function(e,t){for(let r=0;r<t.length;r++){let[a,n=a]=t[r];if(a<=e&&e<=n)return!0}return!1}(i,h)){let e=r[l];if(!e)return void n(l,()=>{a(s)});if(e.supportsCodePoint(i)){let r=d.get(e);"number"!=typeof r&&(r=f.length,f.push(e),d.set(e,r)),u[s]=r,t=1;break}}}i>65535&&s+1<l&&(u[s+1]=u[s],s++,2===t&&(c[c.length-1][1]=s))}p()}()}else c.push([0,e.length-1]),p();function p(){if(c.length){let r=c.map(t=>e.substring(t[0],t[1]+1)).join("\n");t.getFontsForString(r,{lang:i||void 0,style:s,weight:l,dataUrl:h}).then(({fontUrls:e,chars:t})=>{let r=f.length,a=0;c.forEach(e=>{for(let n=0,i=e[1]-e[0];n<=i;n++)u[e[0]+n]=t[a++]+r;a++});let i=0;e.forEach((t,a)=>{n(t,t=>{f[a+r]=t,++i===e.length&&v()})})})}else v()}function v(){a({chars:u,fonts:f})}}},u,function(){return function(e){var t=function(){this.buckets=new Map};t.prototype.add=function(e){var t=e>>5;this.buckets.set(t,(this.buckets.get(t)||0)|1<<(31&e))},t.prototype.has=function(e){var t=this.buckets.get(e>>5);return void 0!==t&&0!=(t&1<<(31&e))},t.prototype.serialize=function(){var e=[];return this.buckets.forEach(function(t,r){e.push((+r).toString(36)+":"+t.toString(36))}),e.join(",")},t.prototype.deserialize=function(e){var t=this;this.buckets.clear(),e.split(",").forEach(function(e){var r=e.split(":");t.buckets.set(parseInt(r[0],36),parseInt(r[1],36))})};function r(e,t){var r=255&e,a=t.codePointAt(r/6|0);return 0!=((a=(a||48)-48)&1<<r%6)}function a(e,t){var r=function(e,r){for(var a=e;a<=r;a++)t(a)};e.replace(/U\+/gi,"").replace(/^,+|,+$/g,"").split(/,+/).map(function(e){return e.split("-").map(function(e){return parseInt(e.trim(),16)})}).forEach(function(e){var t=e[0],a=e[1];void 0===a&&(a=t),r(t,a)})}var n,i={},o={},s=new WeakMap,l="https://cdn.jsdelivr.net/gh/lojjic/unicode-font-resolver@v1.0.1/packages/data",h=new Map;function u(e,t,r){return e[t]?t:e[r]?r:function(e){for(var t in e)return t}(e)}return e.CodePointSet=t,e.clearCache=function(){i={},o={}},e.getFontsForString=function(e,f){void 0===f&&(f={});var d=f.lang;void 0===d&&(d=/\p{Script=Hangul}/u.test(e)?"ko":/\p{Script=Hiragana}|\p{Script=Katakana}/u.test(e)?"ja":"en");var c=f.category;void 0===c&&(c="sans-serif");var p=f.style;void 0===p&&(p="normal");var v=f.weight;void 0===v&&(v=400);var g=(f.dataUrl||l).replace(/\/$/g,""),y=new Map,m=new Uint8Array(e.length),U={},S={},b=Array(e.length),x=new Map,k=!1;function T(e){var t=h.get(e);return t||(t=fetch(g+"/"+e).then(function(e){if(!e.ok)throw Error(e.statusText);return e.json().then(function(e){if(!Array.isArray(e)||1!==e[0])throw Error("Incorrect schema version; need 1, got "+e[0]);return e[1]})}).catch(function(t){if(g!==l)return k||(console.error('unicode-font-resolver: Failed loading from dataUrl "'+g+'", trying default CDN. '+t.message),k=!0),g=l,h.delete(e),T(e);throw t}),h.set(e,t)),t}for(var F=0;F<e.length;F++)!function(t){var r=e.codePointAt(t),a=function(e){var t=(-256&e).toString(16),r=((-256&e)+256-1).toString(16);return"codepoint-index/plane"+(e>>16)+"/"+t+"-"+r+".json"}(r);b[t]=a,i[a]||x.has(a)||x.set(a,T(a).then(function(e){i[a]=e})),r>65535&&(F=++t)}(F);return Promise.all(x.values()).then(function(){x.clear();for(var t=0;t<e.length;t++)!function(a){var n=e.codePointAt(a),s=null,l=i[b[a]],h=void 0;for(var u in l){var f=S[u];if(void 0===f&&(f=S[u]=new RegExp(u).test(d||"en")),f){for(var c in h=u,l[u])if(r(n,l[u][c])){s=c;break}break}}if(!s){e:for(var p in l)if(p!==h){for(var v in l[p])if(r(n,l[p][v])){s=v;break e}}}s||(console.debug("No font coverage for U+"+n.toString(16)),s="latin"),b[a]=s,o[s]||x.has(s)||x.set(s,T("font-meta/"+s+".json").then(function(e){o[s]=e})),n>65535&&(t=++a)}(t);return Promise.all(x.values())}).then(function(){for(var r,i=null,l=0;l<e.length;l++){var h=e.codePointAt(l);if(i&&(n||(n=new Set,a("9-D,20,85,A0,1680,2000-200A,2028-202F,205F,3000",function(e){n.add(e)})),n.has(h)||(function(e){var r=s.get(e);return r||(r=new t,a(e.ranges,function(e){return r.add(e)}),s.set(e,r)),r})(i).has(h)))m[l]=m[l-1];else{var f=U[(i=o[b[l]]).id];if(!f){var d=i.typeforms,S=u(d,c,"sans-serif"),x=u(d[S],p,"normal"),k=function(e,t){var r=t;if(!e.includes(r)){r=1/0;for(var a=0;a<e.length;a++)Math.abs(e[a]-t)<Math.abs(r-t)&&(r=e[a])}return r}(null==(r=d[S])?void 0:r[x],v);f=U[i.id]=g+"/font-files/"+i.id+"/"+S+"."+x+"."+k+".woff"}var T=y.get(f);null==T&&(T=y.size,y.set(f,T)),m[l]=T}h>65535&&(m[++l]=m[l-1])}return{fontUrls:Array.from(y.keys()),chars:m}})},Object.defineProperty(e,"__esModule",{value:!0}),e}({})}],init:(e,t,r)=>e(t,r())}),d=()=>(self.performance||Date).now(),c=(0,s.A)(),p=[],v=0;function g(){let e=d();for(;p.length&&d()-e<5;)p.shift()();v=p.length?setTimeout(g,0):0}let y={},m=0;function U(e,t,r,a,n,i,l,h,u,f){let p="TroikaTextSDFGenerator_JS_"+m++%4,v=y[p];return v||(v=y[p]={workerModule:(0,o.kl)({name:p,workerId:p,dependencies:[s.A,d],init(e,t){let r=e().javascript.generate;return function(...e){let a=t();return{textureData:r(...e),timing:t()-a}}},getTransferables:e=>[e.textureData.buffer]}),requests:0,idleTimer:null}),v.requests++,clearTimeout(v.idleTimer),v.workerModule(e,t,r,a,n,i).then(({textureData:r,timing:a})=>{let n=d(),i=new Uint8Array(4*r.length);for(let e=0;e<r.length;e++)i[4*e+f]=r[e];return c.webglUtils.renderImageData(l,i,h,u,e,t,1<<3-f),a+=d()-n,0==--v.requests&&(v.idleTimer=setTimeout(()=>{(0,o.Qw)(p)},2e3)),{timing:a}})}let S=c.webglUtils.resizeWebGLCanvasWithoutClearing,b={defaultFontURL:null,unicodeFontsURL:null,sdfGlyphSize:64,sdfMargin:1/16,sdfExponent:9,textureWidth:2048,useWorker:!0},x=new i.Q1f;function k(){return(self.performance||Date).now()}let T=Object.create(null);function F(e,t){e=function(e,t){for(let r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);return e}({},e);let r=k(),{defaultFontURL:a}=b,n=[];if(a&&n.push({label:"default",src:_(a)}),e.font&&n.push({label:"user",src:_(e.font)}),e.font=n,e.text=""+e.text,e.sdfGlyphSize=e.sdfGlyphSize||b.sdfGlyphSize,e.unicodeFontsURL=e.unicodeFontsURL||b.unicodeFontsURL,null!=e.colorRanges){let t={};for(let r in e.colorRanges)if(e.colorRanges.hasOwnProperty(r)){let a=e.colorRanges[r];"number"!=typeof a&&(a=x.set(a).getHex()),t[r]=a}e.colorRanges=t}Object.freeze(e);let{textureWidth:o,sdfExponent:s}=b,{sdfGlyphSize:l}=e,h=o/l*4,u=T[l];if(!u){let e=document.createElement("canvas");e.width=o,e.height=256*l/h,(u=T[l]={glyphCount:0,sdfGlyphSize:l,sdfCanvas:e,sdfTexture:new i.gPd(e,void 0,void 0,void 0,i.k6q,i.k6q),contextLost:!1,glyphsByFont:new Map}).sdfTexture.generateMipmaps=!1,function(e){let t=e.sdfCanvas;t.addEventListener("webglcontextlost",t=>{console.log("Context Lost",t),t.preventDefault(),e.contextLost=!0}),t.addEventListener("webglcontextrestored",t=>{console.log("Context Restored",t),e.contextLost=!1;let r=[];e.glyphsByFont.forEach(t=>{t.forEach(t=>{r.push(w(t,e,!0))})}),Promise.all(r).then(()=>{D(e),e.sdfTexture.needsUpdate=!0})})}(u)}let{sdfTexture:f,sdfCanvas:d}=u;(b.useWorker?G:P)(e).then(a=>{let{glyphIds:n,glyphFontIndices:i,fontData:c,glyphPositions:p,fontSize:v,timings:g}=a,y=[],m=new Float32Array(4*n.length),U=0,x=0,T=k(),F=c.map(e=>{let t=u.glyphsByFont.get(e.src);return t||u.glyphsByFont.set(e.src,t=new Map),t});n.forEach((e,t)=>{let r=i[t],{src:o,unitsPerEm:s}=c[r],h=F[r].get(e);if(!h){let{path:t,pathBounds:n}=a.glyphData[o][e],i=Math.max(n[2]-n[0],n[3]-n[1])/l*(b.sdfMargin*l+.5),s=u.glyphCount++,f=[n[0]-i,n[1]-i,n[2]+i,n[3]+i];F[r].set(e,h={path:t,atlasIndex:s,sdfViewBox:f}),y.push(h)}let{sdfViewBox:f}=h,d=p[x++],g=p[x++],S=v/s;m[U++]=d+f[0]*S,m[U++]=g+f[1]*S,m[U++]=d+f[2]*S,m[U++]=g+f[3]*S,n[t]=h.atlasIndex}),g.quads=(g.quads||0)+(k()-T);let C=k();g.sdf={};let _=d.height,A=Math.pow(2,Math.ceil(Math.log2(Math.ceil(u.glyphCount/h)*l)));A>_&&(console.info(`Increasing SDF texture size ${_}->${A}`),S(d,o,A),f.dispose()),Promise.all(y.map(t=>w(t,u,e.gpuAccelerateSDF).then(({timing:e})=>{g.sdf[t.atlasIndex]=e}))).then(()=>{y.length&&!u.contextLost&&(D(u),f.needsUpdate=!0),g.sdfTotal=k()-C,g.total=k()-r,t(Object.freeze({parameters:e,sdfTexture:f,sdfGlyphSize:l,sdfExponent:s,glyphBounds:m,glyphAtlasIndices:n,glyphColors:a.glyphColors,caretPositions:a.caretPositions,chunkedBounds:a.chunkedBounds,ascender:a.ascender,descender:a.descender,lineHeight:a.lineHeight,capHeight:a.capHeight,xHeight:a.xHeight,topBaseline:a.topBaseline,blockBounds:a.blockBounds,visibleBounds:a.visibleBounds,timings:a.timings}))})}),Promise.resolve().then(()=>{!u.contextLost&&(d._warm||(c.webgl.isSupported(d),d._warm=!0))})}function w({path:e,atlasIndex:t,sdfViewBox:r},{sdfGlyphSize:n,sdfCanvas:i,contextLost:o},s){if(o)return Promise.resolve({timing:-1});let{textureWidth:l,sdfExponent:h}=b,u=Math.max(r[2]-r[0],r[3]-r[1]),f=Math.floor(t/4),y=f%(l/n)*n,m=Math.floor(f/(l/n))*n;return function(e,t,r,n,i,o,s,l,h,u,f=!0){return f?((...e)=>new Promise((t,r)=>{p.push(()=>{let a=d();try{c.webgl.generateIntoCanvas(...e),t({timing:d()-a})}catch(e){r(e)}}),v||(v=setTimeout(g,0))}))(e,t,r,n,i,o,s,l,h,u).then(null,f=>(a||(console.warn("WebGL SDF generation failed, falling back to JS",f),a=!0),U(e,t,r,n,i,o,s,l,h,u))):U(e,t,r,n,i,o,s,l,h,u)}(n,n,e,r,u,h,i,y,m,t%4,s)}function C({font:e,characters:t,sdfGlyphSize:r},a){F({font:e,sdfGlyphSize:r,text:Array.isArray(t)?t.join("\n"):""+t},a)}function _(e){return n||(n="undefined"==typeof document?{}:document.createElement("a")),n.href=e,n.href}function D(e){if("function"!=typeof createImageBitmap){console.info("Safari<15: applying SDF canvas workaround");let{sdfCanvas:t,sdfTexture:r}=e,{width:a,height:n}=t,i=e.sdfCanvas.getContext("webgl"),o=r.image.data;o&&o.length===a*n*4||(o=new Uint8Array(a*n*4),r.image={width:a,height:n,data:o},r.flipY=!1,r.isDataTexture=!0),i.readPixels(0,0,a,n,i.RGBA,i.UNSIGNED_BYTE,o)}}let A=(0,o.kl)({name:"Typesetter",dependencies:[function(e,t){let r=1/0,a=/[\u00AD\u034F\u061C\u115F-\u1160\u17B4-\u17B5\u180B-\u180E\u200B-\u200F\u202A-\u202E\u2060-\u206F\u3164\uFE00-\uFE0F\uFEFF\uFFA0\uFFF0-\uFFF8]/,n=`[^\\S\\u00A0]`,i=RegExp(`${n}|[\\-\\u007C\\u00AD\\u2010\\u2012-\\u2014\\u2027\\u2056\\u2E17\\u2E40]`);function o({text:o="",font:f,lang:d,sdfGlyphSize:c=64,fontSize:p=400,fontWeight:v=1,fontStyle:g="normal",letterSpacing:y=0,lineHeight:m="normal",maxWidth:U=r,direction:S,textAlign:b="left",textIndent:x=0,whiteSpace:k="normal",overflowWrap:T="normal",anchorX:F=0,anchorY:w=0,metricsOnly:C=!1,unicodeFontsURL:_,preResolvedFonts:D=null,includeCaretPositions:A=!1,chunkedBoundsSize:G=8192,colorRanges:P=null},O){let I=h(),B={fontLoad:0,typesetting:0};o.indexOf("\r")>-1&&(console.info("Typesetter: got text with \\r chars; normalizing to \\n"),o=o.replace(/\r\n/g,"\n").replace(/\r/g,"\n")),p*=1,y*=1,U*=1,m=m||"normal",x*=1,function({text:t,lang:r,fonts:a,style:n,weight:i,preResolvedFonts:o,unicodeFontsURL:s},l){let h=({chars:e,fonts:t})=>{let r,a,n=[];for(let i=0;i<e.length;i++)e[i]!==a?(a=e[i],n.push(r={start:i,end:i,fontObj:t[e[i]]})):r.end=i;l(n)};o?h(o):e(t,h,{lang:r,fonts:a,style:n,weight:i,unicodeFontsURL:s})}({text:o,lang:d,style:g,weight:v,fonts:"string"==typeof f?[{src:f}]:f,unicodeFontsURL:_,preResolvedFonts:D},e=>{B.fontLoad=h()-I;let f=isFinite(U),d=null,c=null,v=null,g=null,_=null,D=null,R=null,M=null,E=0,L=0,W="nowrap"!==k,z=new Map,V=h(),j=x,N=0,H=new u,q=[H];e.forEach(e=>{let t,r,{fontObj:s}=e,{ascender:l,descender:h,unitsPerEm:d,lineGap:c,capHeight:v,xHeight:g}=s,S=z.get(s);if(!S){let e=p/d,t="normal"===m?(l-h+c)*e:m*p,r=(t-(l-h)*e)/2,a=Math.min(t,(l-h)*e),n=(l+h)/2*e+a/2;S={index:z.size,src:s.src,fontObj:s,fontSizeMult:e,unitsPerEm:d,ascender:l*e,descender:h*e,capHeight:v*e,xHeight:g*e,lineHeight:t,baseline:-r-l*e,caretTop:n,caretBottom:n-a},z.set(s,S)}let{fontSizeMult:b}=S,k=o.slice(e.start,e.end+1);s.forEachGlyph(k,p,y,(s,l,h,d)=>{let c;l+=N,d+=e.start,t=l,r=s;let v=o.charAt(d),g=s.advanceWidth*b,m=H.count;if("isEmpty"in s||(s.isWhitespace=!!v&&new RegExp(n).test(v),s.canBreakAfter=!!v&&i.test(v),s.isEmpty=s.xMin===s.xMax||s.yMin===s.yMax||a.test(v)),!s.isWhitespace&&!s.isEmpty&&L++,W&&f&&!s.isWhitespace&&l+g+j>U&&m){if(H.glyphAt(m-1).glyphObj.canBreakAfter)c=new u,j=-l;else for(let e=m;e--;)if(0===e&&"break-word"===T){c=new u,j=-l;break}else if(H.glyphAt(e).glyphObj.canBreakAfter){let t=(c=H.splitAt(e+1)).glyphAt(0).x;j-=t;for(let e=c.count;e--;)c.glyphAt(e).x-=t;break}c&&(H.isSoftWrapped=!0,H=c,q.push(H),E=U)}let k=H.glyphAt(H.count);k.glyphObj=s,k.x=l+j,k.y=h,k.width=g,k.charIndex=d,k.fontData=S,"\n"===v&&(H=new u,q.push(H),j=-(l+g+y*p)+x)}),N=t+r.advanceWidth*b+y*p});let X=0;q.forEach(e=>{let t=!0;for(let r=e.count;r--;){let a=e.glyphAt(r);t&&!a.glyphObj.isWhitespace&&(e.width=a.x+a.width,e.width>E&&(E=e.width),t=!1);let{lineHeight:n,capHeight:i,xHeight:o,baseline:s}=a.fontData;n>e.lineHeight&&(e.lineHeight=n);let l=s-e.baseline;l<0&&(e.baseline+=l,e.cap+=l,e.ex+=l),e.cap=Math.max(e.cap,e.baseline+i),e.ex=Math.max(e.ex,e.baseline+o)}e.baseline-=X,e.cap-=X,e.ex-=X,X+=e.lineHeight});let Y=0,$=0;if(F&&("number"==typeof F?Y=-F:"string"==typeof F&&(Y=-E*("left"===F?0:"center"===F?.5:"right"===F?1:s(F)))),w&&("number"==typeof w?$=-w:"string"==typeof w&&($="top"===w?0:"top-baseline"===w?-q[0].baseline:"top-cap"===w?-q[0].cap:"top-ex"===w?-q[0].ex:"middle"===w?X/2:"bottom"===w?X:"bottom-baseline"===w?-q[q.length-1].baseline:s(w)*X)),!C){let e,a,n=t.getEmbeddingLevels(o,S);d=new Uint16Array(L),c=new Uint8Array(L),v=new Float32Array(2*L),g={},R=[r,r,-r,-r],M=[],A&&(D=new Float32Array(4*o.length)),P&&(_=new Uint8Array(3*L));let i=0,s=-1,h=-1;if(q.forEach((u,f)=>{let{count:p,width:y}=u;if(p>0){let f,m=0;for(let e=p;e--&&u.glyphAt(e).glyphObj.isWhitespace;)m++;let U=0,S=0;if("center"===b)U=(E-y)/2;else if("right"===b)U=E-y;else if("justify"===b&&u.isSoftWrapped){let e=0;for(let t=p-m;t--;)u.glyphAt(t).glyphObj.isWhitespace&&e++;S=(E-y)/e}if(S||U){let e=0;for(let t=0;t<p;t++){let r=u.glyphAt(t),a=r.glyphObj;r.x+=U+e,0!==S&&a.isWhitespace&&t<p-m&&(e+=S,r.width+=S)}}let x=t.getReorderSegments(o,n,u.glyphAt(0).charIndex,u.glyphAt(u.count-1).charIndex);for(let e=0;e<x.length;e++){let[t,r]=x[e],a=1/0,n=-1/0;for(let e=0;e<p;e++)if(u.glyphAt(e).charIndex>=t){let t=e,i=e;for(;i<p;i++){let e=u.glyphAt(i);if(e.charIndex>r)break;i<p-m&&(a=Math.min(a,e.x),n=Math.max(n,e.x+e.width))}for(let e=t;e<i;e++){let t=u.glyphAt(e);t.x=n-(t.x+t.width-a)}break}}let k=e=>f=e;for(let y=0;y<p;y++){let p=u.glyphAt(y),m=(f=p.glyphObj).index,U=1&n.levels[p.charIndex];if(U){let e=t.getMirroredCharacter(o[p.charIndex]);e&&p.fontData.fontObj.forEachGlyph(e,0,0,k)}if(A){let{charIndex:e,fontData:t}=p,r=p.x+Y,a=p.x+p.width+Y;D[4*e]=U?a:r,D[4*e+1]=U?r:a,D[4*e+2]=u.baseline+t.caretBottom+$,D[4*e+3]=u.baseline+t.caretTop+$;let n=e-s;n>1&&l(D,s,n),s=e}if(P){let{charIndex:e}=p;for(;e>h;)h++,P.hasOwnProperty(h)&&(a=P[h])}if(!f.isWhitespace&&!f.isEmpty){let t=i++,{fontSizeMult:n,src:o,index:s}=p.fontData,l=g[o]||(g[o]={});l[m]||(l[m]={path:f.path,pathBounds:[f.xMin,f.yMin,f.xMax,f.yMax]});let h=p.x+Y,y=p.y+u.baseline+$;v[2*t]=h,v[2*t+1]=y;let U=h+f.xMin*n,S=y+f.yMin*n,b=h+f.xMax*n,x=y+f.yMax*n;U<R[0]&&(R[0]=U),S<R[1]&&(R[1]=S),b>R[2]&&(R[2]=b),x>R[3]&&(R[3]=x),t%G==0&&(e={start:t,end:t,rect:[r,r,-r,-r]},M.push(e)),e.end++;let k=e.rect;if(U<k[0]&&(k[0]=U),S<k[1]&&(k[1]=S),b>k[2]&&(k[2]=b),x>k[3]&&(k[3]=x),d[t]=m,c[t]=s,P){let e=3*t;_[e]=a>>16&255,_[e+1]=a>>8&255,_[e+2]=255&a}}}}}),D){let e=o.length-s;e>1&&l(D,s,e)}}let Q=[];z.forEach(({index:e,src:t,unitsPerEm:r,ascender:a,descender:n,lineHeight:i,capHeight:o,xHeight:s})=>{Q[e]={src:t,unitsPerEm:r,ascender:a,descender:n,lineHeight:i,capHeight:o,xHeight:s}}),B.typesetting=h()-V,O({glyphIds:d,glyphFontIndices:c,glyphPositions:v,glyphData:g,fontData:Q,caretPositions:D,glyphColors:_,chunkedBounds:M,fontSize:p,topBaseline:$+q[0].baseline,blockBounds:[Y,$-X,Y+E,$],visibleBounds:R,timings:B})})}function s(e){let t=e.match(/^([\d.]+)%$/),r=t?parseFloat(t[1]):NaN;return isNaN(r)?0:r/100}function l(e,t,r){let a=e[4*t],n=e[4*t+1],i=e[4*t+2],o=e[4*t+3],s=(n-a)/r;for(let n=0;n<r;n++){let r=(t+n)*4;e[r]=a+s*n,e[r+1]=a+s*(n+1),e[r+2]=i,e[r+3]=o}}function h(){return(self.performance||Date).now()}function u(){this.data=[]}let f=["glyphObj","x","y","width","charIndex","fontData"];return u.prototype={width:0,lineHeight:0,baseline:0,cap:0,ex:0,isSoftWrapped:!1,get count(){return Math.ceil(this.data.length/f.length)},glyphAt(e){let t=u.flyweight;return t.data=this.data,t.index=e,t},splitAt(e){let t=new u;return t.data=this.data.splice(e*f.length),t}},u.flyweight=f.reduce((e,t,r,a)=>(Object.defineProperty(e,t,{get(){return this.data[this.index*f.length+r]},set(e){this.data[this.index*f.length+r]=e}}),e),{data:null,index:0}),{typeset:o,measure:function(e,t){o({...e,metricsOnly:!0},e=>{let[r,a,n,i]=e.blockBounds;t({width:n-r,height:i-a})})}}},f,l.A],init:(e,t,r)=>e(t,r())}),G=(0,o.kl)({name:"Typesetter",dependencies:[A],init:e=>function(t){return new Promise(r=>{e.typeset(t,r)})},getTransferables(e){let t=[];for(let r in e)e[r]&&e[r].buffer&&t.push(e[r].buffer);return t}}),P=G.onMainThread,O={},I="aTroikaGlyphIndex";class B extends i.CmU{constructor(){super(),this.detail=1,this.curveRadius=0,this.groups=[{start:0,count:1/0,materialIndex:0},{start:0,count:1/0,materialIndex:1}],this.boundingSphere=new i.iyt,this.boundingBox=new i.NRn}computeBoundingSphere(){}computeBoundingBox(){}set detail(e){if(e!==this._detail){var t;let r;this._detail=e,("number"!=typeof e||e<1)&&(e=1);let a=((r=O[t=e])||(r=O[t]=new i.bdM(1,1,t,t).translate(.5,.5,0)),r);["position","normal","uv"].forEach(e=>{this.attributes[e]=a.attributes[e].clone()}),this.setIndex(a.getIndex().clone())}}get detail(){return this._detail}set curveRadius(e){e!==this._curveRadius&&(this._curveRadius=e,this._updateBounds())}get curveRadius(){return this._curveRadius}updateGlyphs(e,t,r,a,n){this.updateAttributeData("aTroikaGlyphBounds",e,4),this.updateAttributeData(I,t,1),this.updateAttributeData("aTroikaGlyphColor",n,3),this._blockBounds=r,this._chunkedBounds=a,this.instanceCount=t.length,this._updateBounds()}_updateBounds(){let e=this._blockBounds;if(e){let{curveRadius:t,boundingBox:r}=this;if(t){let{PI:a,floor:n,min:i,max:o,sin:s,cos:l}=Math,h=a/2,u=2*a,f=Math.abs(t),d=e[0]/f,c=e[2]/f,p=n((d+h)/u)!==n((c+h)/u)?-f:i(s(d)*f,s(c)*f),v=n((d-h)/u)!==n((c-h)/u)?f:o(s(d)*f,s(c)*f),g=n((d+a)/u)!==n((c+a)/u)?2*f:o(f-l(d)*f,f-l(c)*f);r.min.set(p,e[1],t<0?-g:0),r.max.set(v,e[3],t<0?0:g)}else r.min.set(e[0],e[1],0),r.max.set(e[2],e[3],0);r.getBoundingSphere(this.boundingSphere)}}applyClipRect(e){let t=this.getAttribute(I).count,r=this._chunkedBounds;if(r)for(let a=r.length;a--;){t=r[a].end;let n=r[a].rect;if(n[1]<e.w&&n[3]>e.y&&n[0]<e.z&&n[2]>e.x)break}this.instanceCount=t}updateAttributeData(e,t,r){let a=this.getAttribute(e);t?a&&a.array.length===t.length?(a.array.set(t),a.needsUpdate=!0):(this.setAttribute(e,new i.uWO(t,r)),delete this._maxInstanceCount,this.dispose()):a&&this.deleteAttribute(e)}}let R=`
uniform vec2 uTroikaSDFTextureSize;
uniform float uTroikaSDFGlyphSize;
uniform vec4 uTroikaTotalBounds;
uniform vec4 uTroikaClipRect;
uniform mat3 uTroikaOrient;
uniform bool uTroikaUseGlyphColors;
uniform float uTroikaEdgeOffset;
uniform float uTroikaBlurRadius;
uniform vec2 uTroikaPositionOffset;
uniform float uTroikaCurveRadius;
attribute vec4 aTroikaGlyphBounds;
attribute float aTroikaGlyphIndex;
attribute vec3 aTroikaGlyphColor;
varying vec2 vTroikaGlyphUV;
varying vec4 vTroikaTextureUVBounds;
varying float vTroikaTextureChannel;
varying vec3 vTroikaGlyphColor;
varying vec2 vTroikaGlyphDimensions;
`,M=`
vec4 bounds = aTroikaGlyphBounds;
bounds.xz += uTroikaPositionOffset.x;
bounds.yw -= uTroikaPositionOffset.y;

vec4 outlineBounds = vec4(
  bounds.xy - uTroikaEdgeOffset - uTroikaBlurRadius,
  bounds.zw + uTroikaEdgeOffset + uTroikaBlurRadius
);
vec4 clippedBounds = vec4(
  clamp(outlineBounds.xy, uTroikaClipRect.xy, uTroikaClipRect.zw),
  clamp(outlineBounds.zw, uTroikaClipRect.xy, uTroikaClipRect.zw)
);

vec2 clippedXY = (mix(clippedBounds.xy, clippedBounds.zw, position.xy) - bounds.xy) / (bounds.zw - bounds.xy);

position.xy = mix(bounds.xy, bounds.zw, clippedXY);

uv = (position.xy - uTroikaTotalBounds.xy) / (uTroikaTotalBounds.zw - uTroikaTotalBounds.xy);

float rad = uTroikaCurveRadius;
if (rad != 0.0) {
  float angle = position.x / rad;
  position.xz = vec2(sin(angle) * rad, rad - cos(angle) * rad);
  normal.xz = vec2(sin(angle), cos(angle));
}
  
position = uTroikaOrient * position;
normal = uTroikaOrient * normal;

vTroikaGlyphUV = clippedXY.xy;
vTroikaGlyphDimensions = vec2(bounds[2] - bounds[0], bounds[3] - bounds[1]);


float txCols = uTroikaSDFTextureSize.x / uTroikaSDFGlyphSize;
vec2 txUvPerSquare = uTroikaSDFGlyphSize / uTroikaSDFTextureSize;
vec2 txStartUV = txUvPerSquare * vec2(
  mod(floor(aTroikaGlyphIndex / 4.0), txCols),
  floor(floor(aTroikaGlyphIndex / 4.0) / txCols)
);
vTroikaTextureUVBounds = vec4(txStartUV, vec2(txStartUV) + txUvPerSquare);
vTroikaTextureChannel = mod(aTroikaGlyphIndex, 4.0);
`,E=`
uniform sampler2D uTroikaSDFTexture;
uniform vec2 uTroikaSDFTextureSize;
uniform float uTroikaSDFGlyphSize;
uniform float uTroikaSDFExponent;
uniform float uTroikaEdgeOffset;
uniform float uTroikaFillOpacity;
uniform float uTroikaBlurRadius;
uniform vec3 uTroikaStrokeColor;
uniform float uTroikaStrokeWidth;
uniform float uTroikaStrokeOpacity;
uniform bool uTroikaSDFDebug;
varying vec2 vTroikaGlyphUV;
varying vec4 vTroikaTextureUVBounds;
varying float vTroikaTextureChannel;
varying vec2 vTroikaGlyphDimensions;

float troikaSdfValueToSignedDistance(float alpha) {
  // Inverse of exponential encoding in webgl-sdf-generator
  
  float maxDimension = max(vTroikaGlyphDimensions.x, vTroikaGlyphDimensions.y);
  float absDist = (1.0 - pow(2.0 * (alpha > 0.5 ? 1.0 - alpha : alpha), 1.0 / uTroikaSDFExponent)) * maxDimension;
  float signedDist = absDist * (alpha > 0.5 ? -1.0 : 1.0);
  return signedDist;
}

float troikaGlyphUvToSdfValue(vec2 glyphUV) {
  vec2 textureUV = mix(vTroikaTextureUVBounds.xy, vTroikaTextureUVBounds.zw, glyphUV);
  vec4 rgba = texture2D(uTroikaSDFTexture, textureUV);
  float ch = floor(vTroikaTextureChannel + 0.5); //NOTE: can't use round() in WebGL1
  return ch == 0.0 ? rgba.r : ch == 1.0 ? rgba.g : ch == 2.0 ? rgba.b : rgba.a;
}

float troikaGlyphUvToDistance(vec2 uv) {
  return troikaSdfValueToSignedDistance(troikaGlyphUvToSdfValue(uv));
}

float troikaGetAADist() {
  
  #if defined(GL_OES_standard_derivatives) || __VERSION__ >= 300
  return length(fwidth(vTroikaGlyphUV * vTroikaGlyphDimensions)) * 0.5;
  #else
  return vTroikaGlyphDimensions.x / 64.0;
  #endif
}

float troikaGetFragDistValue() {
  vec2 clampedGlyphUV = clamp(vTroikaGlyphUV, 0.5 / uTroikaSDFGlyphSize, 1.0 - 0.5 / uTroikaSDFGlyphSize);
  float distance = troikaGlyphUvToDistance(clampedGlyphUV);
 
  // Extrapolate distance when outside bounds:
  distance += clampedGlyphUV == vTroikaGlyphUV ? 0.0 : 
    length((vTroikaGlyphUV - clampedGlyphUV) * vTroikaGlyphDimensions);

  

  return distance;
}

float troikaGetEdgeAlpha(float distance, float distanceOffset, float aaDist) {
  #if defined(IS_DEPTH_MATERIAL) || defined(IS_DISTANCE_MATERIAL)
  float alpha = step(-distanceOffset, -distance);
  #else

  float alpha = smoothstep(
    distanceOffset + aaDist,
    distanceOffset - aaDist,
    distance
  );
  #endif

  return alpha;
}
`,L=`
float aaDist = troikaGetAADist();
float fragDistance = troikaGetFragDistValue();
float edgeAlpha = uTroikaSDFDebug ?
  troikaGlyphUvToSdfValue(vTroikaGlyphUV) :
  troikaGetEdgeAlpha(fragDistance, uTroikaEdgeOffset, max(aaDist, uTroikaBlurRadius));

#if !defined(IS_DEPTH_MATERIAL) && !defined(IS_DISTANCE_MATERIAL)
vec4 fillRGBA = gl_FragColor;
fillRGBA.a *= uTroikaFillOpacity;
vec4 strokeRGBA = uTroikaStrokeWidth == 0.0 ? fillRGBA : vec4(uTroikaStrokeColor, uTroikaStrokeOpacity);
if (fillRGBA.a == 0.0) fillRGBA.rgb = strokeRGBA.rgb;
gl_FragColor = mix(fillRGBA, strokeRGBA, smoothstep(
  -uTroikaStrokeWidth - aaDist,
  -uTroikaStrokeWidth + aaDist,
  fragDistance
));
gl_FragColor.a *= edgeAlpha;
#endif

if (edgeAlpha == 0.0) {
  discard;
}
`,W=new i.V9B({color:0xffffff,side:i.$EB,transparent:!0}),z=new i.kn4,V=new i.Pq0,j=new i.Pq0,N=[],H=new i.Pq0,q="+x+y";function X(e){return Array.isArray(e)?e[0]:e}let Y=()=>{let e=new i.eaF(new i.bdM(1,1),W);return Y=()=>e,e},$=()=>{let e=new i.eaF(new i.bdM(1,1,32,1),W);return $=()=>e,e},Q={type:"syncstart"},Z={type:"synccomplete"},J=["font","fontSize","fontStyle","fontWeight","lang","letterSpacing","lineHeight","maxWidth","overflowWrap","text","direction","textAlign","textIndent","whiteSpace","anchorX","anchorY","colorRanges","sdfGlyphSize"],K=J.concat("material","color","depthOffset","clipRect","curveRadius","orientation","glyphGeometryDetail");class ee extends i.eaF{constructor(){super(new B,null),this.text="",this.anchorX=0,this.anchorY=0,this.curveRadius=0,this.direction="auto",this.font=null,this.unicodeFontsURL=null,this.fontSize=.1,this.fontWeight="normal",this.fontStyle="normal",this.lang=null,this.letterSpacing=0,this.lineHeight="normal",this.maxWidth=1/0,this.overflowWrap="normal",this.textAlign="left",this.textIndent=0,this.whiteSpace="normal",this.material=null,this.color=null,this.colorRanges=null,this.outlineWidth=0,this.outlineColor=0,this.outlineOpacity=1,this.outlineBlur=0,this.outlineOffsetX=0,this.outlineOffsetY=0,this.strokeWidth=0,this.strokeColor=8421504,this.strokeOpacity=1,this.fillOpacity=1,this.depthOffset=0,this.clipRect=null,this.orientation=q,this.glyphGeometryDetail=1,this.sdfGlyphSize=null,this.gpuAccelerateSDF=!0,this.debugSDF=!1}sync(e){this._needsSync&&(this._needsSync=!1,this._isSyncing?(this._queuedSyncs||(this._queuedSyncs=[])).push(e):(this._isSyncing=!0,this.dispatchEvent(Q),F({text:this.text,font:this.font,lang:this.lang,fontSize:this.fontSize||.1,fontWeight:this.fontWeight||"normal",fontStyle:this.fontStyle||"normal",letterSpacing:this.letterSpacing||0,lineHeight:this.lineHeight||"normal",maxWidth:this.maxWidth,direction:this.direction||"auto",textAlign:this.textAlign,textIndent:this.textIndent,whiteSpace:this.whiteSpace,overflowWrap:this.overflowWrap,anchorX:this.anchorX,anchorY:this.anchorY,colorRanges:this.colorRanges,includeCaretPositions:!0,sdfGlyphSize:this.sdfGlyphSize,gpuAccelerateSDF:this.gpuAccelerateSDF,unicodeFontsURL:this.unicodeFontsURL},t=>{this._isSyncing=!1,this._textRenderInfo=t,this.geometry.updateGlyphs(t.glyphBounds,t.glyphAtlasIndices,t.blockBounds,t.chunkedBounds,t.glyphColors);let r=this._queuedSyncs;r&&(this._queuedSyncs=null,this._needsSync=!0,this.sync(()=>{r.forEach(e=>e&&e())})),this.dispatchEvent(Z),e&&e()})))}onBeforeRender(e,t,r,a,n,i){this.sync(),n.isTroikaTextMaterial&&this._prepareForRender(n)}dispose(){this.geometry.dispose()}get textRenderInfo(){return this._textRenderInfo||null}createDerivedMaterial(e){let t=(0,h.Fh)(e,{chained:!0,extensions:{derivatives:!0},uniforms:{uTroikaSDFTexture:{value:null},uTroikaSDFTextureSize:{value:new i.I9Y},uTroikaSDFGlyphSize:{value:0},uTroikaSDFExponent:{value:0},uTroikaTotalBounds:{value:new i.IUQ(0,0,0,0)},uTroikaClipRect:{value:new i.IUQ(0,0,0,0)},uTroikaEdgeOffset:{value:0},uTroikaFillOpacity:{value:1},uTroikaPositionOffset:{value:new i.I9Y},uTroikaCurveRadius:{value:0},uTroikaBlurRadius:{value:0},uTroikaStrokeWidth:{value:0},uTroikaStrokeColor:{value:new i.Q1f},uTroikaStrokeOpacity:{value:1},uTroikaOrient:{value:new i.dwI},uTroikaUseGlyphColors:{value:!0},uTroikaSDFDebug:{value:!1}},vertexDefs:R,vertexTransform:M,fragmentDefs:E,fragmentColorTransform:L,customRewriter({vertexShader:e,fragmentShader:t}){let r=/\buniform\s+vec3\s+diffuse\b/;return r.test(t)&&(t=t.replace(r,"varying vec3 vTroikaGlyphColor").replace(/\bdiffuse\b/g,"vTroikaGlyphColor"),r.test(e)||(e=e.replace(h.Do,"uniform vec3 diffuse;\n$&\nvTroikaGlyphColor = uTroikaUseGlyphColors ? aTroikaGlyphColor / 255.0 : diffuse;\n"))),{vertexShader:e,fragmentShader:t}}});return t.transparent=!0,t.forceSinglePass=!0,Object.defineProperties(t,{isTroikaTextMaterial:{value:!0},shadowSide:{get(){return this.side},set(){}}}),t}get material(){let e=this._derivedMaterial,t=this._baseMaterial||this._defaultMaterial||(this._defaultMaterial=W.clone());if(e&&e.isDerivedFrom(t)||(e=this._derivedMaterial=this.createDerivedMaterial(t),t.addEventListener("dispose",function r(){t.removeEventListener("dispose",r),e.dispose()})),!this.hasOutline())return e;{let t=e._outlineMtl;return t||((t=e._outlineMtl=Object.create(e,{id:{value:e.id+.1}})).isTextOutlineMaterial=!0,t.depthWrite=!1,t.map=null,e.addEventListener("dispose",function r(){e.removeEventListener("dispose",r),t.dispose()})),[t,e]}}set material(e){e&&e.isTroikaTextMaterial?(this._derivedMaterial=e,this._baseMaterial=e.baseMaterial):this._baseMaterial=e}hasOutline(){return!!(this.outlineWidth||this.outlineBlur||this.outlineOffsetX||this.outlineOffsetY)}get glyphGeometryDetail(){return this.geometry.detail}set glyphGeometryDetail(e){this.geometry.detail=e}get curveRadius(){return this.geometry.curveRadius}set curveRadius(e){this.geometry.curveRadius=e}get customDepthMaterial(){return X(this.material).getDepthMaterial()}set customDepthMaterial(e){}get customDistanceMaterial(){return X(this.material).getDistanceMaterial()}set customDistanceMaterial(e){}_prepareForRender(e){let t=e.isTextOutlineMaterial,r=e.uniforms,a=this.textRenderInfo;if(a){let e,n,i,{sdfTexture:o,blockBounds:s}=a;r.uTroikaSDFTexture.value=o,r.uTroikaSDFTextureSize.value.set(o.image.width,o.image.height),r.uTroikaSDFGlyphSize.value=a.sdfGlyphSize,r.uTroikaSDFExponent.value=a.sdfExponent,r.uTroikaTotalBounds.value.fromArray(s),r.uTroikaUseGlyphColors.value=!t&&!!a.glyphColors;let l=0,h=0,u=0,f=0,d=0;if(t){let{outlineWidth:t,outlineOffsetX:r,outlineOffsetY:a,outlineBlur:n,outlineOpacity:i}=this;l=this._parsePercent(t)||0,h=Math.max(0,this._parsePercent(n)||0),e=i,f=this._parsePercent(r)||0,d=this._parsePercent(a)||0}else(u=Math.max(0,this._parsePercent(this.strokeWidth)||0))&&(i=this.strokeColor,r.uTroikaStrokeColor.value.set(null==i?8421504:i),null==(n=this.strokeOpacity)&&(n=1)),e=this.fillOpacity;r.uTroikaEdgeOffset.value=l,r.uTroikaPositionOffset.value.set(f,d),r.uTroikaBlurRadius.value=h,r.uTroikaStrokeWidth.value=u,r.uTroikaStrokeOpacity.value=n,r.uTroikaFillOpacity.value=null==e?1:e,r.uTroikaCurveRadius.value=this.curveRadius||0;let c=this.clipRect;if(c&&Array.isArray(c)&&4===c.length)r.uTroikaClipRect.value.fromArray(c);else{let e=100*(this.fontSize||.1);r.uTroikaClipRect.value.set(s[0]-e,s[1]-e,s[2]+e,s[3]+e)}this.geometry.applyClipRect(r.uTroikaClipRect.value)}r.uTroikaSDFDebug.value=!!this.debugSDF,e.polygonOffset=!!this.depthOffset,e.polygonOffsetFactor=e.polygonOffsetUnits=this.depthOffset||0;let n=t?this.outlineColor||0:this.color;if(null==n)delete e.color;else{let t=e.hasOwnProperty("color")?e.color:e.color=new i.Q1f;(n!==t._input||"object"==typeof n)&&t.set(t._input=n)}let o=this.orientation||q;if(o!==e._orientation){let t=r.uTroikaOrient.value,a=(o=o.replace(/[^-+xyz]/g,""))!==q&&o.match(/^([-+])([xyz])([-+])([xyz])$/);if(a){let[,e,r,n,i]=a;V.set(0,0,0)[r]="-"===e?1:-1,j.set(0,0,0)[i]="-"===n?-1:1,z.lookAt(H,V.cross(j),j),t.setFromMatrix4(z)}else t.identity();e._orientation=o}}_parsePercent(e){if("string"==typeof e){let t=e.match(/^(-?[\d.]+)%$/),r=t?parseFloat(t[1]):NaN;e=(isNaN(r)?0:r/100)*this.fontSize}return e}localPositionToTextCoords(e,t=new i.I9Y){t.copy(e);let r=this.curveRadius;return r&&(t.x=Math.atan2(e.x,Math.abs(r)-Math.abs(e.z))*Math.abs(r)),t}worldPositionToTextCoords(e,t=new i.I9Y){return V.copy(e),this.localPositionToTextCoords(this.worldToLocal(V),t)}raycast(e,t){let{textRenderInfo:r,curveRadius:a}=this;if(r){let n=r.blockBounds,i=a?$():Y(),o=i.geometry,{position:s,uv:l}=o.attributes;for(let e=0;e<l.count;e++){let t=n[0]+l.getX(e)*(n[2]-n[0]),r=n[1]+l.getY(e)*(n[3]-n[1]),i=0;a&&(i=a-Math.cos(t/a)*a,t=Math.sin(t/a)*a),s.setXYZ(e,t,r,i)}o.boundingSphere=this.geometry.boundingSphere,o.boundingBox=this.geometry.boundingBox,i.matrixWorld=this.matrixWorld,i.material.side=this.material.side,N.length=0,i.raycast(e,N);for(let e=0;e<N.length;e++)N[e].object=this,t.push(N[e])}}copy(e){let t=this.geometry;return super.copy(e),this.geometry=t,K.forEach(t=>{this[t]=e[t]}),this}clone(){return new this.constructor().copy(this)}}J.forEach(e=>{let t="_private_"+e;Object.defineProperty(ee.prototype,e,{get(){return this[t]},set(e){e!==this[t]&&(this[t]=e,this._needsSync=!0)}})}),new i.NRn,new i.Q1f,new WeakMap,new WeakMap}}]);