'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  EyeOff, 
  Mail, 
  Lock, 
  GraduationCap,
  Users,
  Building2,
  Shield
} from 'lucide-react';
import Link from 'next/link';

// Mock users for demo purposes
const mockUsers = [
  {
    email: '<EMAIL>',
    password: 'demo123',
    role: 'student',
    name: '<PERSON>',
    redirectTo: '/student'
  },
  {
    email: '<EMAIL>',
    password: 'demo123',
    role: 'teacher',
    name: 'Dr. <PERSON>',
    redirectTo: '/teacher'
  },
  {
    email: '<EMAIL>',
    password: 'demo123',
    role: 'hod',
    name: 'Dr. <PERSON>',
    redirectTo: '/hod'
  },
  {
    email: '<EMAIL>',
    password: 'demo123',
    role: 'admin',
    name: 'Administrator',
    redirectTo: '/admin'
  }
];

export default function SignInPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Mock authentication - in production, this would call your auth API
      const user = mockUsers.find(u => u.email === email && u.password === password);
      
      if (!user) {
        setError('Invalid email or password');
        setIsLoading(false);
        return;
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In production, you would:
      // 1. Call your authentication API
      // 2. Store the JWT token
      // 3. Set up the user session
      
      // For demo, we'll just redirect based on role
      router.push(user.redirectTo);
      
    } catch (error) {
      setError('An error occurred during sign in');
      setIsLoading(false);
    }
  };

  const handleDemoLogin = (user: typeof mockUsers[0]) => {
    setEmail(user.email);
    setPassword(user.password);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Left Side - Branding */}
        <div className="space-y-6">
          <div className="space-y-4">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900">
              AI Tutor
              <span className="text-blue-600"> Platform</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Personalized learning with AI-powered conversations, 
              interactive 3D avatars, and intelligent tutoring systems.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-white rounded-lg border shadow-sm">
              <GraduationCap className="w-8 h-8 text-blue-600 mb-2" />
              <h3 className="font-semibold text-gray-900">Smart Learning</h3>
              <p className="text-sm text-gray-600">AI adapts to your learning style</p>
            </div>
            
            <div className="p-4 bg-white rounded-lg border shadow-sm">
              <Users className="w-8 h-8 text-green-600 mb-2" />
              <h3 className="font-semibold text-gray-900">Interactive Chat</h3>
              <p className="text-sm text-gray-600">Real-time conversations with AI</p>
            </div>
            
            <div className="p-4 bg-white rounded-lg border shadow-sm">
              <Building2 className="w-8 h-8 text-purple-600 mb-2" />
              <h3 className="font-semibold text-gray-900">3D Avatars</h3>
              <p className="text-sm text-gray-600">Immersive learning experience</p>
            </div>
            
            <div className="p-4 bg-white rounded-lg border shadow-sm">
              <Shield className="w-8 h-8 text-orange-600 mb-2" />
              <h3 className="font-semibold text-gray-900">Secure Platform</h3>
              <p className="text-sm text-gray-600">Enterprise-grade security</p>
            </div>
          </div>
        </div>

        {/* Right Side - Sign In Form */}
        <div className="w-full max-w-md mx-auto">
          <Card className="shadow-xl border-0">
            <CardHeader className="space-y-1 text-center">
              <CardTitle className="text-2xl font-bold">Welcome Back</CardTitle>
              <CardDescription>
                Sign in to your account to continue learning
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* Demo Accounts */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">
                  Quick Demo Access:
                </Label>
                <div className="grid grid-cols-2 gap-2">
                  {mockUsers.map((user) => (
                    <Button
                      key={user.role}
                      variant="outline"
                      size="sm"
                      onClick={() => handleDemoLogin(user)}
                      className="justify-start text-xs"
                    >
                      <Badge variant="secondary" className="mr-2 text-xs">
                        {user.role}
                      </Badge>
                      {user.name.split(' ')[0]}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">Or continue with</span>
                </div>
              </div>

              {/* Sign In Form */}
              <form onSubmit={handleSignIn} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="pl-10 pr-10"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                  </div>
                </div>

                {error && (
                  <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                    {error}
                  </div>
                )}

                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Signing in...</span>
                    </div>
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </form>

              <div className="text-center space-y-2">
                <Link 
                  href="/forgot-password" 
                  className="text-sm text-blue-600 hover:underline"
                >
                  Forgot your password?
                </Link>
                
                <div className="text-sm text-gray-600">
                  Don't have an account?{' '}
                  <Link href="/sign-up" className="text-blue-600 hover:underline">
                    Contact your administrator
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Demo Credentials Info */}
          <Card className="mt-4 bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <h4 className="font-medium text-blue-900 mb-2">Demo Credentials:</h4>
              <div className="space-y-1 text-sm text-blue-800">
                <p><strong>Student:</strong> <EMAIL> / demo123</p>
                <p><strong>Teacher:</strong> <EMAIL> / demo123</p>
                <p><strong>HOD:</strong> <EMAIL> / demo123</p>
                <p><strong>Admin:</strong> <EMAIL> / demo123</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
