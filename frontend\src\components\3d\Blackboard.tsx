'use client';

// Blackboard component for displaying text translations and explanations

import { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { Text, Html } from '@react-three/drei';
import * as THREE from 'three';

interface BlackboardProps {
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: number;
  text?: string;
  width?: number;
  height?: number;
}

// HTML overlay for rich text content
function BlackboardOverlay({ 
  text, 
  position, 
  width = 3, 
  height = 2 
}: { 
  text: string;
  position: [number, number, number];
  width?: number;
  height?: number;
}) {
  const overlayRef = useRef<HTMLDivElement>(null);
  
  // Parse text for different content types
  const parseContent = (content: string) => {
    // Check if content contains furigana markup
    if (content.includes('[') && content.includes(']')) {
      return content.split(/(\[[^\]]+\])/).map((part, index) => {
        if (part.startsWith('[') && part.endsWith(']')) {
          const furiganaText = part.slice(1, -1);
          const [kanji, reading] = furiganaText.split('|');
          return (
            <span key={index} className="inline-block mx-1">
              <span className="block text-xs text-center text-blue-300">{reading}</span>
              <span className="block text-lg font-bold">{kanji}</span>
            </span>
          );
        }
        return <span key={index}>{part}</span>;
      });
    }
    
    // Regular text
    return content;
  };
  
  if (!text) return null;
  
  return (
    <Html
      position={position}
      transform
      occlude
      style={{
        width: `${width * 100}px`,
        height: `${height * 100}px`,
        pointerEvents: 'none',
      }}
    >
      <div
        ref={overlayRef}
        className="w-full h-full p-4 text-white font-mono text-sm leading-relaxed overflow-hidden"
        style={{
          background: 'rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(1px)',
        }}
      >
        <div className="space-y-2">
          {text.split('\n').map((line, index) => (
            <div key={index} className="flex flex-wrap">
              {parseContent(line)}
            </div>
          ))}
        </div>
      </div>
    </Html>
  );
}

// Chalk dust particle effect
function ChalkDust({ position }: { position: [number, number, number] }) {
  const particlesRef = useRef<THREE.Points>(null);
  
  const particleCount = 50;
  const positions = useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount; i++) {
      pos[i * 3] = (Math.random() - 0.5) * 2;
      pos[i * 3 + 1] = (Math.random() - 0.5) * 2;
      pos[i * 3 + 2] = (Math.random() - 0.5) * 0.1;
    }
    return pos;
  }, []);
  
  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1;
      
      // Animate particles
      const positions = particlesRef.current.geometry.attributes.position.array as Float32Array;
      for (let i = 0; i < particleCount; i++) {
        positions[i * 3 + 1] += Math.sin(state.clock.elapsedTime + i) * 0.001;
      }
      particlesRef.current.geometry.attributes.position.needsUpdate = true;
    }
  });
  
  return (
    <points ref={particlesRef} position={position}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
          args={[positions, 3]}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.02}
        color="#ffffff"
        transparent
        opacity={0.6}
        sizeAttenuation
      />
    </points>
  );
}

// Main Blackboard component
export default function Blackboard({
  position = [0, 0, 0],
  rotation = [0, 0, 0],
  scale = 1,
  text = '',
  width = 3,
  height = 2
}: BlackboardProps) {
  const boardRef = useRef<THREE.Mesh>(null);
  const frameRef = useRef<THREE.Group>(null);
  
  // Subtle animation for the blackboard
  useFrame((state) => {
    if (frameRef.current) {
      frameRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 0.5) * 0.01;
    }
  });
  
  return (
    <group ref={frameRef} position={position} rotation={rotation} scale={scale}>
      {/* Blackboard frame */}
      <mesh position={[0, 0, -0.05]} castShadow>
        <boxGeometry args={[width + 0.2, height + 0.2, 0.1]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>
      
      {/* Blackboard surface */}
      <mesh ref={boardRef} castShadow receiveShadow>
        <planeGeometry args={[width, height]} />
        <meshStandardMaterial 
          color="#1a1a1a" 
          roughness={0.8}
          metalness={0.1}
        />
      </mesh>
      
      {/* Chalk tray */}
      <mesh position={[0, -height/2 - 0.05, 0.05]} castShadow>
        <boxGeometry args={[width * 0.8, 0.05, 0.1]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>
      
      {/* Chalk pieces */}
      {[0, 0.3, -0.3].map((offset, index) => (
        <mesh 
          key={index}
          position={[offset, -height/2 - 0.02, 0.1]} 
          rotation={[0, 0, Math.random() * Math.PI]}
          castShadow
        >
          <cylinderGeometry args={[0.01, 0.01, 0.1]} />
          <meshStandardMaterial color="#ffffff" />
        </mesh>
      ))}
      
      {/* Text content using 3D text */}
      {text && (
        <Text
          position={[0, 0, 0.01]}
          fontSize={0.15}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
          maxWidth={width * 0.9}
          textAlign="center"
          font="/fonts/chalk-font.woff"
          outlineWidth={0.002}
          outlineColor="#cccccc"
        >
          {text}
        </Text>
      )}
      
      {/* HTML overlay for rich content */}
      {text && (
        <BlackboardOverlay 
          text={text} 
          position={[0, 0, 0.02]} 
          width={width} 
          height={height} 
        />
      )}
      
      {/* Chalk dust effect */}
      <ChalkDust position={[0, 0, 0.1]} />
      
      {/* Eraser */}
      <mesh position={[width/2 - 0.2, height/2 - 0.1, 0.05]} castShadow>
        <boxGeometry args={[0.15, 0.05, 0.03]} />
        <meshStandardMaterial color="#654321" />
      </mesh>
      
      {/* Felt part of eraser */}
      <mesh position={[width/2 - 0.2, height/2 - 0.1, 0.065]} castShadow>
        <boxGeometry args={[0.14, 0.04, 0.01]} />
        <meshStandardMaterial color="#333333" />
      </mesh>
    </group>
  );
}
