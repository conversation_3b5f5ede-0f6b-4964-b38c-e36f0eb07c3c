{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,wOAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,wOAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,wOAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,wOAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,wOAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,wOAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mMAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,iRAAC;QAAI,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAEA,MAAM,SAAS,qMAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qMAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qMAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,wOAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,iRAAC,qMAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,iRAAC,qMAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,iRAAC,uPAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qMAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,wOAAA,CAAA,aAAgB,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC,qMAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,iRAAC,mPAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,qMAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,wOAAA,CAAA,aAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC,qMAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,iRAAC,uPAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,qMAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,wOAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,iRAAC,qMAAA,CAAA,SAAsB;kBACrB,cAAA,iRAAC,qMAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,iRAAC;;;;;8BACD,iRAAC,qMAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,iRAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qMAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,wOAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC,qMAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,qMAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,wOAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,iRAAC,qMAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,iRAAC;gBAAK,WAAU;0BACd,cAAA,iRAAC,qMAAA,CAAA,gBAA6B;8BAC5B,cAAA,iRAAC,uOAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,iRAAC,qMAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qMAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,wOAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,iRAAC,qMAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,qMAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/chat/MessageList.tsx"], "sourcesContent": ["'use client';\n\n// MessageList component for displaying chat messages with furigana and grammar breakdown\n\nimport { useEffect, useRef } from 'react';\nimport { ChatMessage } from '@/types';\nimport { Card } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  User,\n  Bot,\n  Volume2,\n  Copy,\n  RotateCcw,\n  BookOpen,\n  MessageSquare\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface MessageListProps {\n  messages: ChatMessage[];\n  isLoading?: boolean;\n  isStreaming?: boolean;\n  onRegenerateResponse?: () => void;\n  onPlayAudio?: (audioUrl: string) => void;\n}\n\ninterface FuriganaDisplayProps {\n  furigana: ChatMessage['furigana'];\n}\n\ninterface GrammarBreakdownProps {\n  grammar: ChatMessage['grammarBreakdown'];\n}\n\n// Component to display furigana text\nfunction FuriganaDisplay({ furigana }: FuriganaDisplayProps) {\n  if (!furigana || furigana.length === 0) return null;\n\n  return (\n    <div className=\"mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200\">\n      <div className=\"flex items-center space-x-2 mb-2\">\n        <BookOpen className=\"h-4 w-4 text-blue-600\" />\n        <span className=\"text-sm font-medium text-blue-800\">Furigana Reading</span>\n      </div>\n      <div className=\"space-y-2\">\n        {furigana.map((item, index) => (\n          <div key={index} className=\"inline-block mr-3 mb-2\">\n            <div className=\"text-center\">\n              <div className=\"text-xs text-blue-600 leading-none\">{item.reading}</div>\n              <div className=\"text-lg font-medium text-gray-900\">{item.kanji}</div>\n              {item.meaning && (\n                <div className=\"text-xs text-gray-600 mt-1\">{item.meaning}</div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n\n// Component to display grammar breakdown\nfunction GrammarBreakdown({ grammar }: GrammarBreakdownProps) {\n  if (!grammar || grammar.length === 0) return null;\n\n  return (\n    <div className=\"mt-3 p-3 bg-green-50 rounded-lg border border-green-200\">\n      <div className=\"flex items-center space-x-2 mb-2\">\n        <MessageSquare className=\"h-4 w-4 text-green-600\" />\n        <span className=\"text-sm font-medium text-green-800\">Grammar Analysis</span>\n      </div>\n      <div className=\"space-y-2\">\n        {grammar.map((item, index) => (\n          <div key={index} className=\"border-l-2 border-green-300 pl-3\">\n            <div className=\"flex items-center space-x-2 mb-1\">\n              <span className=\"font-medium text-gray-900\">{item.text}</span>\n              <Badge variant=\"outline\" className=\"text-xs\">\n                {item.partOfSpeech}\n              </Badge>\n            </div>\n            <p className=\"text-sm text-gray-700\">{item.explanation}</p>\n            {item.example && (\n              <p className=\"text-xs text-gray-600 mt-1 italic\">\n                Example: {item.example}\n              </p>\n            )}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n\n// Individual message component\nfunction MessageItem({ \n  message, \n  onPlayAudio, \n  onRegenerateResponse \n}: { \n  message: ChatMessage;\n  onPlayAudio?: (audioUrl: string) => void;\n  onRegenerateResponse?: () => void;\n}) {\n  const isUser = message.role === 'user';\n  const isAssistant = message.role === 'assistant';\n\n  const handleCopyMessage = () => {\n    navigator.clipboard.writeText(message.content);\n  };\n\n  const handlePlayAudio = () => {\n    if (message.audioUrl && onPlayAudio) {\n      onPlayAudio(message.audioUrl);\n    }\n  };\n\n  return (\n    <div className={cn(\n      \"flex gap-3 mb-4\",\n      isUser ? \"justify-end\" : \"justify-start\"\n    )}>\n      {!isUser && (\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n            <Bot className=\"h-4 w-4 text-white\" />\n          </div>\n        </div>\n      )}\n      \n      <div className={cn(\n        \"max-w-[80%] space-y-1\",\n        isUser ? \"items-end\" : \"items-start\"\n      )}>\n        <Card className={cn(\n          \"p-3\",\n          isUser \n            ? \"bg-blue-600 text-white\" \n            : \"bg-white border border-gray-200\"\n        )}>\n          <div className=\"space-y-2\">\n            <p className={cn(\n              \"text-sm leading-relaxed\",\n              isUser ? \"text-white\" : \"text-gray-900\"\n            )}>\n              {message.content}\n            </p>\n            \n            {/* Action buttons for assistant messages */}\n            {isAssistant && (\n              <div className=\"flex items-center space-x-2 pt-2 border-t border-gray-100\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleCopyMessage}\n                  className=\"h-6 px-2 text-xs\"\n                >\n                  <Copy className=\"h-3 w-3 mr-1\" />\n                  Copy\n                </Button>\n                \n                {message.hasAudio && message.audioUrl && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handlePlayAudio}\n                    className=\"h-6 px-2 text-xs\"\n                  >\n                    <Volume2 className=\"h-3 w-3 mr-1\" />\n                    Play\n                  </Button>\n                )}\n                \n                {onRegenerateResponse && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={onRegenerateResponse}\n                    className=\"h-6 px-2 text-xs\"\n                  >\n                    <RotateCcw className=\"h-3 w-3 mr-1\" />\n                    Regenerate\n                  </Button>\n                )}\n              </div>\n            )}\n          </div>\n        </Card>\n        \n        {/* Furigana display for assistant messages */}\n        {isAssistant && message.furigana && (\n          <FuriganaDisplay furigana={message.furigana} />\n        )}\n        \n        {/* Grammar breakdown for assistant messages */}\n        {isAssistant && message.grammarBreakdown && (\n          <GrammarBreakdown grammar={message.grammarBreakdown} />\n        )}\n        \n        {/* Timestamp */}\n        <div className={cn(\n          \"text-xs text-gray-500\",\n          isUser ? \"text-right\" : \"text-left\"\n        )}>\n          {message.timestamp.toLocaleTimeString([], { \n            hour: '2-digit', \n            minute: '2-digit' \n          })}\n        </div>\n      </div>\n      \n      {isUser && (\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center\">\n            <User className=\"h-4 w-4 text-white\" />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n// Loading indicator for streaming messages\nfunction LoadingIndicator() {\n  return (\n    <div className=\"flex gap-3 mb-4\">\n      <div className=\"flex-shrink-0\">\n        <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n          <Bot className=\"h-4 w-4 text-white\" />\n        </div>\n      </div>\n      <Card className=\"p-3 bg-white border border-gray-200\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"flex space-x-1\">\n            <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n            <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n            <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n          </div>\n          <span className=\"text-sm text-gray-600\">AI is thinking...</span>\n        </div>\n      </Card>\n    </div>\n  );\n}\n\n// Main MessageList component\nexport default function MessageList({\n  messages,\n  isLoading = false,\n  isStreaming = false,\n  onRegenerateResponse,\n  onPlayAudio\n}: MessageListProps) {\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages, isLoading, isStreaming]);\n\n  return (\n    <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n      {messages.length === 0 ? (\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"text-center\">\n            <Bot className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Start a conversation\n            </h3>\n            <p className=\"text-gray-600\">\n              Ask me anything about your subject. I&apos;m here to help you learn!\n            </p>\n          </div>\n        </div>\n      ) : (\n        <>\n          {messages.map((message) => (\n            <MessageItem\n              key={message.id}\n              message={message}\n              onPlayAudio={onPlayAudio}\n              onRegenerateResponse={\n                message.role === 'assistant' ? onRegenerateResponse : undefined\n              }\n            />\n          ))}\n          \n          {(isLoading || isStreaming) && <LoadingIndicator />}\n        </>\n      )}\n      \n      <div ref={messagesEndRef} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,yFAAyF;AAEzF;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAlBA;;;;;;;;AAoCA,qCAAqC;AACrC,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACzD,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;IAE/C,qBACE,iRAAC;QAAI,WAAU;;0BACb,iRAAC;gBAAI,WAAU;;kCACb,iRAAC,iPAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,iRAAC;wBAAK,WAAU;kCAAoC;;;;;;;;;;;;0BAEtD,iRAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,iRAAC;wBAAgB,WAAU;kCACzB,cAAA,iRAAC;4BAAI,WAAU;;8CACb,iRAAC;oCAAI,WAAU;8CAAsC,KAAK,OAAO;;;;;;8CACjE,iRAAC;oCAAI,WAAU;8CAAqC,KAAK,KAAK;;;;;;gCAC7D,KAAK,OAAO,kBACX,iRAAC;oCAAI,WAAU;8CAA8B,KAAK,OAAO;;;;;;;;;;;;uBALrD;;;;;;;;;;;;;;;;AAapB;AAEA,yCAAyC;AACzC,SAAS,iBAAiB,EAAE,OAAO,EAAyB;IAC1D,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG,OAAO;IAE7C,qBACE,iRAAC;QAAI,WAAU;;0BACb,iRAAC;gBAAI,WAAU;;kCACb,iRAAC,2PAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,iRAAC;wBAAK,WAAU;kCAAqC;;;;;;;;;;;;0BAEvD,iRAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,iRAAC;wBAAgB,WAAU;;0CACzB,iRAAC;gCAAI,WAAU;;kDACb,iRAAC;wCAAK,WAAU;kDAA6B,KAAK,IAAI;;;;;;kDACtD,iRAAC,oKAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAChC,KAAK,YAAY;;;;;;;;;;;;0CAGtB,iRAAC;gCAAE,WAAU;0CAAyB,KAAK,WAAW;;;;;;4BACrD,KAAK,OAAO,kBACX,iRAAC;gCAAE,WAAU;;oCAAoC;oCACrC,KAAK,OAAO;;;;;;;;uBAVlB;;;;;;;;;;;;;;;;AAkBpB;AAEA,+BAA+B;AAC/B,SAAS,YAAY,EACnB,OAAO,EACP,WAAW,EACX,oBAAoB,EAKrB;IACC,MAAM,SAAS,QAAQ,IAAI,KAAK;IAChC,MAAM,cAAc,QAAQ,IAAI,KAAK;IAErC,MAAM,oBAAoB;QACxB,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,OAAO;IAC/C;IAEA,MAAM,kBAAkB;QACtB,IAAI,QAAQ,QAAQ,IAAI,aAAa;YACnC,YAAY,QAAQ,QAAQ;QAC9B;IACF;IAEA,qBACE,iRAAC;QAAI,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACf,mBACA,SAAS,gBAAgB;;YAExB,CAAC,wBACA,iRAAC;gBAAI,WAAU;0BACb,cAAA,iRAAC;oBAAI,WAAU;8BACb,cAAA,iRAAC,mOAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAKrB,iRAAC;gBAAI,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACf,yBACA,SAAS,cAAc;;kCAEvB,iRAAC,mKAAA,CAAA,OAAI;wBAAC,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EAChB,OACA,SACI,2BACA;kCAEJ,cAAA,iRAAC;4BAAI,WAAU;;8CACb,iRAAC;oCAAE,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACb,2BACA,SAAS,eAAe;8CAEvB,QAAQ,OAAO;;;;;;gCAIjB,6BACC,iRAAC;oCAAI,WAAU;;sDACb,iRAAC,qKAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,iRAAC,qOAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAIlC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,kBACnC,iRAAC,qKAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,iRAAC,+OAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAKvC,sCACC,iRAAC,qKAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,iRAAC,mPAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;oBAUjD,eAAe,QAAQ,QAAQ,kBAC9B,iRAAC;wBAAgB,UAAU,QAAQ,QAAQ;;;;;;oBAI5C,eAAe,QAAQ,gBAAgB,kBACtC,iRAAC;wBAAiB,SAAS,QAAQ,gBAAgB;;;;;;kCAIrD,iRAAC;wBAAI,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACf,yBACA,SAAS,eAAe;kCAEvB,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4BACxC,MAAM;4BACN,QAAQ;wBACV;;;;;;;;;;;;YAIH,wBACC,iRAAC;gBAAI,WAAU;0BACb,cAAA,iRAAC;oBAAI,WAAU;8BACb,cAAA,iRAAC,qOAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAM5B;AAEA,2CAA2C;AAC3C,SAAS;IACP,qBACE,iRAAC;QAAI,WAAU;;0BACb,iRAAC;gBAAI,WAAU;0BACb,cAAA,iRAAC;oBAAI,WAAU;8BACb,cAAA,iRAAC,mOAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGnB,iRAAC,mKAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,iRAAC;oBAAI,WAAU;;sCACb,iRAAC;4BAAI,WAAU;;8CACb,iRAAC;oCAAI,WAAU;;;;;;8CACf,iRAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CACjG,iRAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;sCAEnG,iRAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAKlD;AAGe,SAAS,YAAY,EAClC,QAAQ,EACR,YAAY,KAAK,EACjB,cAAc,KAAK,EACnB,oBAAoB,EACpB,WAAW,EACM;IACjB,MAAM,iBAAiB,CAAA,GAAA,wOAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,iDAAiD;IACjD,CAAA,GAAA,wOAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D,GAAG;QAAC;QAAU;QAAW;KAAY;IAErC,qBACE,iRAAC;QAAI,WAAU;;YACZ,SAAS,MAAM,KAAK,kBACnB,iRAAC;gBAAI,WAAU;0BACb,cAAA,iRAAC;oBAAI,WAAU;;sCACb,iRAAC,mOAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,iRAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,iRAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;qCAMjC;;oBACG,SAAS,GAAG,CAAC,CAAC,wBACb,iRAAC;4BAEC,SAAS;4BACT,aAAa;4BACb,sBACE,QAAQ,IAAI,KAAK,cAAc,uBAAuB;2BAJnD,QAAQ,EAAE;;;;;oBASlB,CAAC,aAAa,WAAW,mBAAK,iRAAC;;;;;;;0BAIpC,iRAAC;gBAAI,KAAK;;;;;;;;;;;;AAGhB", "debugId": null}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.TextareaHTMLAttributes<HTMLTextAreaElement>>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,wOAAA,CAAA,aAAgB,CAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,iRAAC;QACC,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/chat/TypingBox.tsx"], "sourcesContent": ["'use client';\n\n// TypingBox component for sending messages with text and voice input\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Textarea } from '@/components/ui/textarea';\nimport { \n  Send, \n  Mic, \n  <PERSON>cOff, \n  <PERSON>ader2,\n  <PERSON>c<PERSON>,\n  <PERSON>\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface TypingBoxProps {\n  onSendMessage: (message: string) => void;\n  isLoading?: boolean;\n  isStreaming?: boolean;\n  placeholder?: string;\n  disabled?: boolean;\n  maxLength?: number;\n}\n\nexport default function TypingBox({\n  onSendMessage,\n  isLoading = false,\n  isStreaming = false,\n  placeholder = \"Type your message here...\",\n  disabled = false,\n  maxLength = 1000\n}: TypingBoxProps) {\n  const [message, setMessage] = useState('');\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Auto-resize textarea\n  useEffect(() => {\n    if (textareaRef.current) {\n      textareaRef.current.style.height = 'auto';\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\n    }\n  }, [message]);\n\n  // Handle sending message\n  const handleSendMessage = () => {\n    if (!message.trim() || isLoading || isStreaming || disabled) return;\n    \n    onSendMessage(message.trim());\n    setMessage('');\n    \n    // Reset textarea height\n    if (textareaRef.current) {\n      textareaRef.current.style.height = 'auto';\n    }\n  };\n\n  // Handle Enter key press\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Start voice recording\n  const startRecording = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n      const mediaRecorder = new MediaRecorder(stream);\n      mediaRecorderRef.current = mediaRecorder;\n      \n      const audioChunks: Blob[] = [];\n      \n      mediaRecorder.ondataavailable = (event) => {\n        audioChunks.push(event.data);\n      };\n      \n      mediaRecorder.onstop = async () => {\n        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });\n        console.log('Audio recorded, blob ready for processing:', audioBlob.size, 'bytes');\n\n        // TODO: Send audio to speech-to-text API\n        // For now, just add a placeholder message\n        const transcribedText = \"Voice message transcribed\"; // Replace with actual transcription\n        setMessage(transcribedText);\n        \n        // Stop all tracks\n        stream.getTracks().forEach(track => track.stop());\n      };\n      \n      mediaRecorder.start();\n      setIsRecording(true);\n      setRecordingTime(0);\n      \n      // Start recording timer\n      recordingIntervalRef.current = setInterval(() => {\n        setRecordingTime(prev => prev + 1);\n      }, 1000);\n      \n    } catch (error) {\n      console.error('Error starting recording:', error);\n      alert('Could not access microphone. Please check permissions.');\n    }\n  };\n\n  // Stop voice recording\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setRecordingTime(0);\n      \n      if (recordingIntervalRef.current) {\n        clearInterval(recordingIntervalRef.current);\n        recordingIntervalRef.current = null;\n      }\n    }\n  };\n\n  // Format recording time\n  const formatRecordingTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Check if send button should be disabled\n  const isSendDisabled = !message.trim() || isLoading || isStreaming || disabled;\n\n  return (\n    <div className=\"border-t bg-white p-4\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Recording indicator */}\n        {isRecording && (\n          <div className=\"mb-3 flex items-center justify-center space-x-2 text-red-600\">\n            <div className=\"w-3 h-3 bg-red-600 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium\">\n              Recording... {formatRecordingTime(recordingTime)}\n            </span>\n          </div>\n        )}\n        \n        <div className=\"flex items-end space-x-3\">\n          {/* Voice recording button */}\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            onClick={isRecording ? stopRecording : startRecording}\n            disabled={disabled || isLoading || isStreaming}\n            className={cn(\n              \"flex-shrink-0\",\n              isRecording && \"bg-red-50 border-red-200 text-red-600\"\n            )}\n          >\n            {isRecording ? (\n              <MicOff className=\"h-4 w-4\" />\n            ) : (\n              <Mic className=\"h-4 w-4\" />\n            )}\n          </Button>\n          \n          {/* Text input area */}\n          <div className=\"flex-1 relative\">\n            <Textarea\n              ref={textareaRef}\n              value={message}\n              onChange={(e) => setMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder={placeholder}\n              disabled={disabled || isRecording}\n              maxLength={maxLength}\n              className={cn(\n                \"min-h-[44px] max-h-32 resize-none pr-12\",\n                \"focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              )}\n              rows={1}\n            />\n            \n            {/* Character count */}\n            {message.length > maxLength * 0.8 && (\n              <div className=\"absolute bottom-2 right-2 text-xs text-gray-500\">\n                {message.length}/{maxLength}\n              </div>\n            )}\n          </div>\n          \n          {/* Additional action buttons */}\n          <div className=\"flex space-x-2\">\n            {/* Attachment button (placeholder) */}\n            <Button\n              variant=\"outline\"\n              size=\"icon\"\n              disabled={disabled || isLoading || isStreaming}\n              className=\"flex-shrink-0\"\n              onClick={() => {\n                // TODO: Implement file attachment\n                console.log('File attachment clicked');\n              }}\n            >\n              <Paperclip className=\"h-4 w-4\" />\n            </Button>\n            \n            {/* Emoji button (placeholder) */}\n            <Button\n              variant=\"outline\"\n              size=\"icon\"\n              disabled={disabled || isLoading || isStreaming}\n              className=\"flex-shrink-0\"\n              onClick={() => {\n                // TODO: Implement emoji picker\n                console.log('Emoji picker clicked');\n              }}\n            >\n              <Smile className=\"h-4 w-4\" />\n            </Button>\n          </div>\n          \n          {/* Send button */}\n          <Button\n            onClick={handleSendMessage}\n            disabled={isSendDisabled}\n            className=\"flex-shrink-0\"\n            size=\"icon\"\n          >\n            {isLoading || isStreaming ? (\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\n            ) : (\n              <Send className=\"h-4 w-4\" />\n            )}\n          </Button>\n        </div>\n        \n        {/* Quick suggestions (optional) */}\n        <div className=\"mt-3 flex flex-wrap gap-2\">\n          {[\n            \"Explain this concept\",\n            \"Give me an example\",\n            \"What does this mean?\",\n            \"How do I pronounce this?\"\n          ].map((suggestion) => (\n            <Button\n              key={suggestion}\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setMessage(suggestion)}\n              disabled={disabled || isLoading || isStreaming || isRecording}\n              className=\"text-xs\"\n            >\n              {suggestion}\n            </Button>\n          ))}\n        </div>\n        \n        {/* Help text */}\n        <div className=\"mt-2 text-xs text-gray-500 text-center\">\n          Press Enter to send, Shift+Enter for new line\n          {!disabled && \" • Click mic to record voice message\"}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,qEAAqE;AAErE;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAfA;;;;;;;AA0Be,SAAS,UAAU,EAChC,aAAa,EACb,YAAY,KAAK,EACjB,cAAc,KAAK,EACnB,cAAc,2BAA2B,EACzC,WAAW,KAAK,EAChB,YAAY,IAAI,EACD;IACf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,wOAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,wOAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,wOAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,cAAc,CAAA,GAAA,wOAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,mBAAmB,CAAA,GAAA,wOAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,uBAAuB,CAAA,GAAA,wOAAA,CAAA,SAAM,AAAD,EAAyB;IAE3D,uBAAuB;IACvB,CAAA,GAAA,wOAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QAC5E;IACF,GAAG;QAAC;KAAQ;IAEZ,yBAAyB;IACzB,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,IAAI,MAAM,aAAa,eAAe,UAAU;QAE7D,cAAc,QAAQ,IAAI;QAC1B,WAAW;QAEX,wBAAwB;QACxB,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;QACrC;IACF;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,wBAAwB;IACxB,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,MAAM,gBAAgB,IAAI,cAAc;YACxC,iBAAiB,OAAO,GAAG;YAE3B,MAAM,cAAsB,EAAE;YAE9B,cAAc,eAAe,GAAG,CAAC;gBAC/B,YAAY,IAAI,CAAC,MAAM,IAAI;YAC7B;YAEA,cAAc,MAAM,GAAG;gBACrB,MAAM,YAAY,IAAI,KAAK,aAAa;oBAAE,MAAM;gBAAY;gBAC5D,QAAQ,GAAG,CAAC,8CAA8C,UAAU,IAAI,EAAE;gBAE1E,yCAAyC;gBACzC,0CAA0C;gBAC1C,MAAM,kBAAkB,6BAA6B,oCAAoC;gBACzF,WAAW;gBAEX,kBAAkB;gBAClB,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAChD;YAEA,cAAc,KAAK;YACnB,eAAe;YACf,iBAAiB;YAEjB,wBAAwB;YACxB,qBAAqB,OAAO,GAAG,YAAY;gBACzC,iBAAiB,CAAA,OAAQ,OAAO;YAClC,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,gBAAgB;QACpB,IAAI,iBAAiB,OAAO,IAAI,aAAa;YAC3C,iBAAiB,OAAO,CAAC,IAAI;YAC7B,eAAe;YACf,iBAAiB;YAEjB,IAAI,qBAAqB,OAAO,EAAE;gBAChC,cAAc,qBAAqB,OAAO;gBAC1C,qBAAqB,OAAO,GAAG;YACjC;QACF;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,0CAA0C;IAC1C,MAAM,iBAAiB,CAAC,QAAQ,IAAI,MAAM,aAAa,eAAe;IAEtE,qBACE,iRAAC;QAAI,WAAU;kBACb,cAAA,iRAAC;YAAI,WAAU;;gBAEZ,6BACC,iRAAC;oBAAI,WAAU;;sCACb,iRAAC;4BAAI,WAAU;;;;;;sCACf,iRAAC;4BAAK,WAAU;;gCAAsB;gCACtB,oBAAoB;;;;;;;;;;;;;8BAKxC,iRAAC;oBAAI,WAAU;;sCAEb,iRAAC,qKAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,cAAc,gBAAgB;4BACvC,UAAU,YAAY,aAAa;4BACnC,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,iBACA,eAAe;sCAGhB,4BACC,iRAAC,6OAAA,CAAA,SAAM;gCAAC,WAAU;;;;;qDAElB,iRAAC,mOAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAKnB,iRAAC;4BAAI,WAAU;;8CACb,iRAAC,uKAAA,CAAA,WAAQ;oCACP,KAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,YAAY;oCACZ,aAAa;oCACb,UAAU,YAAY;oCACtB,WAAW;oCACX,WAAW,CAAA,GAAA,sJAAA,CAAA,KAAE,AAAD,EACV,2CACA;oCAEF,MAAM;;;;;;gCAIP,QAAQ,MAAM,GAAG,YAAY,qBAC5B,iRAAC;oCAAI,WAAU;;wCACZ,QAAQ,MAAM;wCAAC;wCAAE;;;;;;;;;;;;;sCAMxB,iRAAC;4BAAI,WAAU;;8CAEb,iRAAC,qKAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,UAAU,YAAY,aAAa;oCACnC,WAAU;oCACV,SAAS;wCACP,kCAAkC;wCAClC,QAAQ,GAAG,CAAC;oCACd;8CAEA,cAAA,iRAAC,+OAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAIvB,iRAAC,qKAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,UAAU,YAAY,aAAa;oCACnC,WAAU;oCACV,SAAS;wCACP,+BAA+B;wCAC/B,QAAQ,GAAG,CAAC;oCACd;8CAEA,cAAA,iRAAC,uOAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKrB,iRAAC,qKAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,MAAK;sCAEJ,aAAa,4BACZ,iRAAC,oPAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB,iRAAC,qOAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMtB,iRAAC;oBAAI,WAAU;8BACZ;wBACC;wBACA;wBACA;wBACA;qBACD,CAAC,GAAG,CAAC,CAAC,2BACL,iRAAC,qKAAA,CAAA,SAAM;4BAEL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,WAAW;4BAC1B,UAAU,YAAY,aAAa,eAAe;4BAClD,WAAU;sCAET;2BAPI;;;;;;;;;;8BAaX,iRAAC;oBAAI,WAAU;;wBAAyC;wBAErD,CAAC,YAAY;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/lib/api.ts"], "sourcesContent": ["// API utility functions for the AI Tutor Platform\n\nimport {\n  User,\n  Subject,\n  StudyMaterial,\n  ChatMessage,\n  TeacherAvatar,\n  Announcement,\n  Analytics,\n  ApiResponse,\n  PaginatedResponse,\n  S3SignedUrl,\n  StreamingResponse,\n  Viseme\n} from '@/types';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1';\n\n// Generic API request function\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<ApiResponse<T>> {\n  try {\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('API request failed:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred',\n    };\n  }\n}\n\n// Authentication APIs\nexport const authApi = {\n  // Get current user profile\n  getMe: (): Promise<ApiResponse<User>> => \n    apiRequest<User>('/me'),\n\n  // Update user profile\n  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> =>\n    apiRequest<User>('/me', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Subject APIs\nexport const subjectApi = {\n  // Get all subjects for current user\n  getSubjects: (): Promise<ApiResponse<Subject[]>> =>\n    apiRequest<Subject[]>('/subjects'),\n\n  // Get subject by ID\n  getSubject: (id: string): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>(`/subjects/${id}`),\n\n  // Create new subject (teacher/admin only)\n  createSubject: (data: Omit<Subject, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>('/subjects', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update subject (teacher/admin only)\n  updateSubject: (id: string, data: Partial<Subject>): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>(`/subjects/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete subject (teacher/admin only)\n  deleteSubject: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/subjects/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Study Material APIs\nexport const materialApi = {\n  // Get materials for a subject\n  getMaterials: (subjectId: string): Promise<ApiResponse<StudyMaterial[]>> =>\n    apiRequest<StudyMaterial[]>(`/materials?subjectId=${subjectId}`),\n\n  // Upload new material\n  uploadMaterial: (data: FormData): Promise<ApiResponse<StudyMaterial>> =>\n    fetch(`${API_BASE_URL}/materials`, {\n      method: 'POST',\n      body: data,\n    }).then(res => res.json()),\n\n  // Delete material\n  deleteMaterial: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/materials/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Chat APIs\nexport const chatApi = {\n  // Send message and get streaming response\n  sendMessage: async function* (\n    message: string,\n    subjectId?: string\n  ): AsyncGenerator<StreamingResponse, void, unknown> {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ message, subjectId }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new Error('No response body');\n    }\n\n    const decoder = new TextDecoder();\n    let buffer = '';\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') {\n              return;\n            }\n            try {\n              const parsed: StreamingResponse = JSON.parse(data);\n              yield parsed;\n            } catch (e) {\n              console.error('Failed to parse SSE data:', e);\n            }\n          }\n        }\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  },\n\n  // Get chat history\n  getChatHistory: (subjectId?: string): Promise<ApiResponse<ChatMessage[]>> =>\n    apiRequest<ChatMessage[]>(`/chat/history${subjectId ? `?subjectId=${subjectId}` : ''}`),\n};\n\n// Text-to-Speech APIs\nexport const ttsApi = {\n  // Get TTS audio and visemes\n  getTTS: (messageId: string): Promise<ApiResponse<{ audioUrl: string; visemes: Viseme[] }>> =>\n    apiRequest<{ audioUrl: string; visemes: Viseme[] }>(`/tts?id=${messageId}`),\n};\n\n// Avatar APIs\nexport const avatarApi = {\n  // Get teacher avatar configuration\n  getAvatar: (teacherId?: string): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>(`/avatar${teacherId ? `?teacherId=${teacherId}` : ''}`),\n\n  // Save teacher avatar configuration\n  saveAvatar: (data: Omit<TeacherAvatar, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>('/avatar', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update avatar configuration\n  updateAvatar: (id: string, data: Partial<TeacherAvatar>): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>(`/avatar/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n};\n\n// S3 Upload APIs\nexport const s3Api = {\n  // Get signed URL for file upload\n  getSignedUrl: (fileName: string, fileType: string): Promise<ApiResponse<S3SignedUrl>> =>\n    apiRequest<S3SignedUrl>('/s3/sign', {\n      method: 'POST',\n      body: JSON.stringify({ fileName, fileType }),\n    }),\n\n  // Upload file to S3 using signed URL\n  uploadFile: async (file: File, signedUrl: S3SignedUrl): Promise<boolean> => {\n    try {\n      const formData = new FormData();\n      Object.entries(signedUrl.fields).forEach(([key, value]) => {\n        formData.append(key, value);\n      });\n      formData.append('file', file);\n\n      const response = await fetch(signedUrl.url, {\n        method: 'POST',\n        body: formData,\n      });\n\n      return response.ok;\n    } catch (error) {\n      console.error('S3 upload failed:', error);\n      return false;\n    }\n  },\n};\n\n// Announcement APIs\nexport const announcementApi = {\n  // Get announcements\n  getAnnouncements: (): Promise<ApiResponse<Announcement[]>> =>\n    apiRequest<Announcement[]>('/announcements'),\n\n  // Create announcement (HOD/Admin only)\n  createAnnouncement: (data: Omit<Announcement, 'id' | 'authorId' | 'authorName' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Announcement>> =>\n    apiRequest<Announcement>('/announcements', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete announcement\n  deleteAnnouncement: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/announcements/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Analytics APIs\nexport const analyticsApi = {\n  // Get analytics data (HOD/Admin only)\n  getAnalytics: (): Promise<ApiResponse<Analytics>> =>\n    apiRequest<Analytics>('/analytics'),\n\n  // Get user analytics\n  getUserAnalytics: (userId?: string): Promise<ApiResponse<Record<string, unknown>>> =>\n    apiRequest<Record<string, unknown>>(`/analytics/users${userId ? `/${userId}` : ''}`),\n};\n\n// User Management APIs (Admin only)\nexport const userApi = {\n  // Get all users\n  getUsers: async (page = 1, limit = 20): Promise<PaginatedResponse<User>> => {\n    const response = await apiRequest<User[]>(`/users?page=${page}&limit=${limit}`);\n    // Transform to paginated response format\n    return {\n      success: response.success,\n      data: response.data || [],\n      pagination: {\n        page,\n        limit,\n        total: response.data?.length || 0,\n        totalPages: Math.ceil((response.data?.length || 0) / limit)\n      }\n    };\n  },\n\n  // Create user\n  createUser: (data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<User>> =>\n    apiRequest<User>('/users', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update user\n  updateUser: (id: string, data: Partial<User>): Promise<ApiResponse<User>> =>\n    apiRequest<User>(`/users/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete user\n  deleteUser: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/users/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Mock data for development (remove when backend is ready)\nexport const mockData = {\n  user: {\n    id: '1',\n    name: 'John Doe',\n    email: '<EMAIL>',\n    role: 'student' as const,\n    orgId: 'org1',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  subjects: [\n    {\n      id: '1',\n      name: 'Japanese Language',\n      description: 'Learn Japanese with AI assistance',\n      teacherId: 'teacher1',\n      teacherName: 'Tanaka Sensei',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n    {\n      id: '2',\n      name: 'Mathematics',\n      description: 'Advanced mathematics concepts',\n      teacherId: 'teacher2',\n      teacherName: 'Smith Sensei',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n  ],\n  messages: [\n    {\n      id: '1',\n      content: 'Hello! How can I help you today?',\n      role: 'assistant' as const,\n      timestamp: new Date(),\n      subjectId: '1',\n    },\n    {\n      id: '2',\n      content: 'Can you explain the difference between は and が?',\n      role: 'user' as const,\n      timestamp: new Date(),\n      subjectId: '1',\n    },\n  ],\n};\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;AAiBlD,MAAM,eAAe,6DAAmC;AAExD,+BAA+B;AAC/B,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,UAAU,EAAE;YACzD,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,UAAU;IACrB,2BAA2B;IAC3B,OAAO,IACL,WAAiB;IAEnB,sBAAsB;IACtB,eAAe,CAAC,OACd,WAAiB,OAAO;YACtB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,aAAa;IACxB,oCAAoC;IACpC,aAAa,IACX,WAAsB;IAExB,oBAAoB;IACpB,YAAY,CAAC,KACX,WAAoB,CAAC,UAAU,EAAE,IAAI;IAEvC,0CAA0C;IAC1C,eAAe,CAAC,OACd,WAAoB,aAAa;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sCAAsC;IACtC,eAAe,CAAC,IAAY,OAC1B,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sCAAsC;IACtC,eAAe,CAAC,KACd,WAAiB,CAAC,UAAU,EAAE,IAAI,EAAE;YAClC,QAAQ;QACV;AACJ;AAGO,MAAM,cAAc;IACzB,8BAA8B;IAC9B,cAAc,CAAC,YACb,WAA4B,CAAC,qBAAqB,EAAE,WAAW;IAEjE,sBAAsB;IACtB,gBAAgB,CAAC,OACf,MAAM,GAAG,aAAa,UAAU,CAAC,EAAE;YACjC,QAAQ;YACR,MAAM;QACR,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;IAEzB,kBAAkB;IAClB,gBAAgB,CAAC,KACf,WAAiB,CAAC,WAAW,EAAE,IAAI,EAAE;YACnC,QAAQ;QACV;AACJ;AAGO,MAAM,UAAU;IACrB,0CAA0C;IAC1C,aAAa,gBACX,OAAe,EACf,SAAkB;QAElB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,KAAK,CAAC,EAAE;YACnD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAU;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAAU,IAAI;QACpB,IAAI,SAAS;QAEb,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,UAAU,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBAC/C,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,SAAS,MAAM,GAAG,MAAM;gBAExB,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI,SAAS,UAAU;4BACrB;wBACF;wBACA,IAAI;4BACF,MAAM,SAA4B,KAAK,KAAK,CAAC;4BAC7C,MAAM;wBACR,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,6BAA6B;wBAC7C;oBACF;gBACF;YACF;QACF,SAAU;YACR,OAAO,WAAW;QACpB;IACF;IAEA,mBAAmB;IACnB,gBAAgB,CAAC,YACf,WAA0B,CAAC,aAAa,EAAE,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI;AAC1F;AAGO,MAAM,SAAS;IACpB,4BAA4B;IAC5B,QAAQ,CAAC,YACP,WAAoD,CAAC,QAAQ,EAAE,WAAW;AAC9E;AAGO,MAAM,YAAY;IACvB,mCAAmC;IACnC,WAAW,CAAC,YACV,WAA0B,CAAC,OAAO,EAAE,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI;IAElF,oCAAoC;IACpC,YAAY,CAAC,OACX,WAA0B,WAAW;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,8BAA8B;IAC9B,cAAc,CAAC,IAAY,OACzB,WAA0B,CAAC,QAAQ,EAAE,IAAI,EAAE;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,QAAQ;IACnB,iCAAiC;IACjC,cAAc,CAAC,UAAkB,WAC/B,WAAwB,YAAY;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAU;YAAS;QAC5C;IAEF,qCAAqC;IACrC,YAAY,OAAO,MAAY;QAC7B,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,OAAO,OAAO,CAAC,UAAU,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBACpD,SAAS,MAAM,CAAC,KAAK;YACvB;YACA,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,UAAU,GAAG,EAAE;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;QACT;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,oBAAoB;IACpB,kBAAkB,IAChB,WAA2B;IAE7B,uCAAuC;IACvC,oBAAoB,CAAC,OACnB,WAAyB,kBAAkB;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sBAAsB;IACtB,oBAAoB,CAAC,KACnB,WAAiB,CAAC,eAAe,EAAE,IAAI,EAAE;YACvC,QAAQ;QACV;AACJ;AAGO,MAAM,eAAe;IAC1B,sCAAsC;IACtC,cAAc,IACZ,WAAsB;IAExB,qBAAqB;IACrB,kBAAkB,CAAC,SACjB,WAAoC,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,IAAI;AACvF;AAGO,MAAM,UAAU;IACrB,gBAAgB;IAChB,UAAU,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE;QACnC,MAAM,WAAW,MAAM,WAAmB,CAAC,YAAY,EAAE,KAAK,OAAO,EAAE,OAAO;QAC9E,yCAAyC;QACzC,OAAO;YACL,SAAS,SAAS,OAAO;YACzB,MAAM,SAAS,IAAI,IAAI,EAAE;YACzB,YAAY;gBACV;gBACA;gBACA,OAAO,SAAS,IAAI,EAAE,UAAU;gBAChC,YAAY,KAAK,IAAI,CAAC,CAAC,SAAS,IAAI,EAAE,UAAU,CAAC,IAAI;YACvD;QACF;IACF;IAEA,cAAc;IACd,YAAY,CAAC,OACX,WAAiB,UAAU;YACzB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,cAAc;IACd,YAAY,CAAC,IAAY,OACvB,WAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,cAAc;IACd,YAAY,CAAC,KACX,WAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;YAC/B,QAAQ;QACV;AACJ;AAGO,MAAM,WAAW;IACtB,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW;YACX,aAAa;YACb,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW;YACX,aAAa;YACb,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;KACD;IACD,UAAU;QACR;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;KACD;AACH", "debugId": null}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/store/useChat.ts"], "sourcesContent": ["// Zustand store for Chat state management\n\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { ChatMessage, Subject, ChatStore } from '@/types';\nimport { chatApi } from '@/lib/api';\n\nexport const useChat = create<ChatStore>()(\n  devtools(\n    persist(\n      (set) => ({\n        // State\n        messages: [],\n        isLoading: false,\n        isStreaming: false,\n        currentSubject: undefined,\n\n        // Actions\n        addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {\n          const newMessage: ChatMessage = {\n            ...message,\n            id: crypto.randomUUID(),\n            timestamp: new Date(),\n          };\n\n          set(\n            (state) => ({\n              messages: [...state.messages, newMessage],\n            }),\n            false,\n            'addMessage'\n          );\n        },\n\n        updateLastMessage: (updates: Partial<ChatMessage>) => {\n          set(\n            (state) => {\n              const messages = [...state.messages];\n              const lastIndex = messages.length - 1;\n              \n              if (lastIndex >= 0) {\n                messages[lastIndex] = { ...messages[lastIndex], ...updates };\n              }\n              \n              return { messages };\n            },\n            false,\n            'updateLastMessage'\n          );\n        },\n\n        setLoading: (loading: boolean) => {\n          set({ isLoading: loading }, false, 'setLoading');\n        },\n\n        setStreaming: (streaming: boolean) => {\n          set({ isStreaming: streaming }, false, 'setStreaming');\n        },\n\n        setCurrentSubject: (subject: Subject | undefined) => {\n          set({ currentSubject: subject }, false, 'setCurrentSubject');\n        },\n\n        clearMessages: () => {\n          set({ messages: [] }, false, 'clearMessages');\n        },\n      }),\n      {\n        name: 'chat-store',\n        partialize: (state) => ({\n          messages: state.messages,\n          currentSubject: state.currentSubject,\n        }),\n      }\n    ),\n    {\n      name: 'chat-store',\n    }\n  )\n);\n\n// Selectors for optimized re-renders\nexport const useMessages = () => useChat((state) => state.messages);\nexport const useIsLoading = () => useChat((state) => state.isLoading);\nexport const useIsStreaming = () => useChat((state) => state.isStreaming);\nexport const useCurrentSubject = () => useChat((state) => state.currentSubject);\n\n// Individual action selectors - using individual selectors to avoid object recreation\nexport const useAddMessage = () => useChat((state) => state.addMessage);\nexport const useUpdateLastMessage = () => useChat((state) => state.updateLastMessage);\nexport const useSetLoading = () => useChat((state) => state.setLoading);\nexport const useSetStreaming = () => useChat((state) => state.setStreaming);\nexport const useSetCurrentSubject = () => useChat((state) => state.setCurrentSubject);\nexport const useClearMessages = () => useChat((state) => state.clearMessages);\n\n// Legacy hook for backward compatibility (but avoid using this)\nexport const useChatActions = () => useChat((state) => ({\n  addMessage: state.addMessage,\n  updateLastMessage: state.updateLastMessage,\n  setLoading: state.setLoading,\n  setStreaming: state.setStreaming,\n  setCurrentSubject: state.setCurrentSubject,\n  clearMessages: state.clearMessages,\n}));\n\n// Helper hooks for chat functionality\nexport const useChatHelpers = () => {\n  const addMessage = useAddMessage();\n  const updateLastMessage = useUpdateLastMessage();\n  const setLoading = useSetLoading();\n  const setStreaming = useSetStreaming();\n  const currentSubject = useCurrentSubject();\n\n  const sendMessage = async (content: string) => {\n    if (!content.trim()) return;\n\n    // Add user message\n    addMessage({\n      content: content.trim(),\n      role: 'user',\n      subjectId: currentSubject?.id,\n    });\n\n    // Add placeholder assistant message\n    addMessage({\n      content: '',\n      role: 'assistant',\n      subjectId: currentSubject?.id,\n    });\n\n    setLoading(true);\n    setStreaming(true);\n\n    try {\n      // Stream the response\n      let fullResponse = '';\n      \n      for await (const chunk of chatApi.sendMessage(content, currentSubject?.id)) {\n        if (chunk.type === 'text') {\n          fullResponse += chunk.content;\n          updateLastMessage({ content: fullResponse });\n        } else if (chunk.type === 'audio' && chunk.audioUrl) {\n          updateLastMessage({ \n            hasAudio: true, \n            audioUrl: chunk.audioUrl,\n            visemes: chunk.visemes \n          });\n        } else if (chunk.type === 'complete') {\n          // Final message update\n          updateLastMessage({ content: fullResponse + chunk.content });\n          break;\n        }\n      }\n    } catch (error) {\n      console.error('Failed to send message:', error);\n      updateLastMessage({ \n        content: 'Sorry, I encountered an error. Please try again.' \n      });\n    } finally {\n      setLoading(false);\n      setStreaming(false);\n    }\n  };\n\n  const regenerateLastResponse = async () => {\n    const messages = useChat.getState().messages;\n    const lastUserMessage = [...messages].reverse().find(m => m.role === 'user');\n    \n    if (!lastUserMessage) return;\n\n    // Remove the last assistant message if it exists\n    const filteredMessages = messages.filter((_, index) => \n      index !== messages.length - 1 || messages[messages.length - 1].role !== 'assistant'\n    );\n    \n    useChat.setState({ messages: filteredMessages });\n    \n    // Resend the last user message\n    await sendMessage(lastUserMessage.content);\n  };\n\n  const loadChatHistory = async (subjectId?: string) => {\n    setLoading(true);\n    \n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await chatApi.getChatHistory(subjectId);\n      console.log('Loading chat history for subject:', subjectId);\n      \n      // For now, use mock data or keep existing messages\n    } catch (error) {\n      console.error('Failed to load chat history:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    sendMessage,\n    regenerateLastResponse,\n    loadChatHistory,\n  };\n};\n\n// Message filtering and search\nexport const useMessageFilters = () => {\n  const messages = useMessages();\n\n  const getMessagesBySubject = (subjectId: string) => {\n    return messages.filter(message => message.subjectId === subjectId);\n  };\n\n  const searchMessages = (query: string) => {\n    const lowercaseQuery = query.toLowerCase();\n    return messages.filter(message => \n      message.content.toLowerCase().includes(lowercaseQuery)\n    );\n  };\n\n  const getMessagesWithAudio = () => {\n    return messages.filter(message => message.hasAudio);\n  };\n\n  const getRecentMessages = (limit: number = 10) => {\n    return messages.slice(-limit);\n  };\n\n  return {\n    getMessagesBySubject,\n    searchMessages,\n    getMessagesWithAudio,\n    getRecentMessages,\n  };\n};\n\n// Chat statistics\nexport const useChatStats = () => {\n  const messages = useMessages();\n\n  const getTotalMessages = () => messages.length;\n  \n  const getUserMessageCount = () => \n    messages.filter(m => m.role === 'user').length;\n  \n  const getAssistantMessageCount = () => \n    messages.filter(m => m.role === 'assistant').length;\n  \n  const getMessagesWithAudioCount = () => \n    messages.filter(m => m.hasAudio).length;\n  \n  const getAverageMessageLength = () => {\n    if (messages.length === 0) return 0;\n    const totalLength = messages.reduce((sum, m) => sum + m.content.length, 0);\n    return Math.round(totalLength / messages.length);\n  };\n\n  const getMessagesByDate = () => {\n    const messagesByDate: Record<string, number> = {};\n    \n    messages.forEach(message => {\n      const date = message.timestamp.toISOString().split('T')[0];\n      messagesByDate[date] = (messagesByDate[date] || 0) + 1;\n    });\n    \n    return messagesByDate;\n  };\n\n  return {\n    getTotalMessages,\n    getUserMessageCount,\n    getAssistantMessageCount,\n    getMessagesWithAudioCount,\n    getAverageMessageLength,\n    getMessagesByDate,\n  };\n};\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;;;;;;;;;;;;;;AAE1C;AACA;AAEA;;;;AAEO,MAAM,UAAU,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,QAAQ;QACR,UAAU,EAAE;QACZ,WAAW;QACX,aAAa;QACb,gBAAgB;QAEhB,UAAU;QACV,YAAY,CAAC;YACX,MAAM,aAA0B;gBAC9B,GAAG,OAAO;gBACV,IAAI,OAAO,UAAU;gBACrB,WAAW,IAAI;YACjB;YAEA,IACE,CAAC,QAAU,CAAC;oBACV,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAW;gBAC3C,CAAC,GACD,OACA;QAEJ;QAEA,mBAAmB,CAAC;YAClB,IACE,CAAC;gBACC,MAAM,WAAW;uBAAI,MAAM,QAAQ;iBAAC;gBACpC,MAAM,YAAY,SAAS,MAAM,GAAG;gBAEpC,IAAI,aAAa,GAAG;oBAClB,QAAQ,CAAC,UAAU,GAAG;wBAAE,GAAG,QAAQ,CAAC,UAAU;wBAAE,GAAG,OAAO;oBAAC;gBAC7D;gBAEA,OAAO;oBAAE;gBAAS;YACpB,GACA,OACA;QAEJ;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ,GAAG,OAAO;QACrC;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE,aAAa;YAAU,GAAG,OAAO;QACzC;QAEA,mBAAmB,CAAC;YAClB,IAAI;gBAAE,gBAAgB;YAAQ,GAAG,OAAO;QAC1C;QAEA,eAAe;YACb,IAAI;gBAAE,UAAU,EAAE;YAAC,GAAG,OAAO;QAC/B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;QACtC,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,cAAc,IAAM,QAAQ,CAAC,QAAU,MAAM,QAAQ;AAC3D,MAAM,eAAe,IAAM,QAAQ,CAAC,QAAU,MAAM,SAAS;AAC7D,MAAM,iBAAiB,IAAM,QAAQ,CAAC,QAAU,MAAM,WAAW;AACjE,MAAM,oBAAoB,IAAM,QAAQ,CAAC,QAAU,MAAM,cAAc;AAGvE,MAAM,gBAAgB,IAAM,QAAQ,CAAC,QAAU,MAAM,UAAU;AAC/D,MAAM,uBAAuB,IAAM,QAAQ,CAAC,QAAU,MAAM,iBAAiB;AAC7E,MAAM,gBAAgB,IAAM,QAAQ,CAAC,QAAU,MAAM,UAAU;AAC/D,MAAM,kBAAkB,IAAM,QAAQ,CAAC,QAAU,MAAM,YAAY;AACnE,MAAM,uBAAuB,IAAM,QAAQ,CAAC,QAAU,MAAM,iBAAiB;AAC7E,MAAM,mBAAmB,IAAM,QAAQ,CAAC,QAAU,MAAM,aAAa;AAGrE,MAAM,iBAAiB,IAAM,QAAQ,CAAC,QAAU,CAAC;YACtD,YAAY,MAAM,UAAU;YAC5B,mBAAmB,MAAM,iBAAiB;YAC1C,YAAY,MAAM,UAAU;YAC5B,cAAc,MAAM,YAAY;YAChC,mBAAmB,MAAM,iBAAiB;YAC1C,eAAe,MAAM,aAAa;QACpC,CAAC;AAGM,MAAM,iBAAiB;IAC5B,MAAM,aAAa;IACnB,MAAM,oBAAoB;IAC1B,MAAM,aAAa;IACnB,MAAM,eAAe;IACrB,MAAM,iBAAiB;IAEvB,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,mBAAmB;QACnB,WAAW;YACT,SAAS,QAAQ,IAAI;YACrB,MAAM;YACN,WAAW,gBAAgB;QAC7B;QAEA,oCAAoC;QACpC,WAAW;YACT,SAAS;YACT,MAAM;YACN,WAAW,gBAAgB;QAC7B;QAEA,WAAW;QACX,aAAa;QAEb,IAAI;YACF,sBAAsB;YACtB,IAAI,eAAe;YAEnB,WAAW,MAAM,SAAS,oJAAA,CAAA,UAAO,CAAC,WAAW,CAAC,SAAS,gBAAgB,IAAK;gBAC1E,IAAI,MAAM,IAAI,KAAK,QAAQ;oBACzB,gBAAgB,MAAM,OAAO;oBAC7B,kBAAkB;wBAAE,SAAS;oBAAa;gBAC5C,OAAO,IAAI,MAAM,IAAI,KAAK,WAAW,MAAM,QAAQ,EAAE;oBACnD,kBAAkB;wBAChB,UAAU;wBACV,UAAU,MAAM,QAAQ;wBACxB,SAAS,MAAM,OAAO;oBACxB;gBACF,OAAO,IAAI,MAAM,IAAI,KAAK,YAAY;oBACpC,uBAAuB;oBACvB,kBAAkB;wBAAE,SAAS,eAAe,MAAM,OAAO;oBAAC;oBAC1D;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,kBAAkB;gBAChB,SAAS;YACX;QACF,SAAU;YACR,WAAW;YACX,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,WAAW,QAAQ,QAAQ,GAAG,QAAQ;QAC5C,MAAM,kBAAkB;eAAI;SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAErE,IAAI,CAAC,iBAAiB;QAEtB,iDAAiD;QACjD,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,GAAG,QAC3C,UAAU,SAAS,MAAM,GAAG,KAAK,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK;QAG1E,QAAQ,QAAQ,CAAC;YAAE,UAAU;QAAiB;QAE9C,+BAA+B;QAC/B,MAAM,YAAY,gBAAgB,OAAO;IAC3C;IAEA,MAAM,kBAAkB,OAAO;QAC7B,WAAW;QAEX,IAAI;YACF,wDAAwD;YACxD,4DAA4D;YAC5D,QAAQ,GAAG,CAAC,qCAAqC;QAEjD,mDAAmD;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,MAAM,WAAW;IAEjB,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK;IAC1D;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,iBAAiB,MAAM,WAAW;QACxC,OAAO,SAAS,MAAM,CAAC,CAAA,UACrB,QAAQ,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;IAE3C;IAEA,MAAM,uBAAuB;QAC3B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpD;IAEA,MAAM,oBAAoB,CAAC,QAAgB,EAAE;QAC3C,OAAO,SAAS,KAAK,CAAC,CAAC;IACzB;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM,WAAW;IAEjB,MAAM,mBAAmB,IAAM,SAAS,MAAM;IAE9C,MAAM,sBAAsB,IAC1B,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;IAEhD,MAAM,2BAA2B,IAC/B,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;IAErD,MAAM,4BAA4B,IAChC,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;IAEzC,MAAM,0BAA0B;QAC9B,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAClC,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;QACxE,OAAO,KAAK,KAAK,CAAC,cAAc,SAAS,MAAM;IACjD;IAEA,MAAM,oBAAoB;QACxB,MAAM,iBAAyC,CAAC;QAEhD,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,OAAO,QAAQ,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,cAAc,CAAC,KAAK,GAAG,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,IAAI;QACvD;QAEA,OAAO;IACT;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1787, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/store/useUser.ts"], "sourcesContent": ["// Zustand store for User and App state management\n\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\n\nimport { User, Subject, StudyMaterial, Announcement, Analytics, UserStore } from '@/types';\nimport { mockData } from '@/lib/api';\n\nexport const useUser = create<UserStore>()(\n  devtools(\n    persist(\n      (set) => ({\n        // State\n        user: undefined,\n        subjects: [],\n        materials: [],\n        announcements: [],\n        analytics: undefined,\n\n        // Actions\n        setUser: (user: User | undefined) => {\n          set({ user }, false, 'setUser');\n        },\n\n        setSubjects: (subjects: Subject[]) => {\n          set({ subjects }, false, 'setSubjects');\n        },\n\n        setMaterials: (materials: StudyMaterial[]) => {\n          set({ materials }, false, 'setMaterials');\n        },\n\n        setAnnouncements: (announcements: Announcement[]) => {\n          set({ announcements }, false, 'setAnnouncements');\n        },\n\n        setAnalytics: (analytics: Analytics) => {\n          set({ analytics }, false, 'setAnalytics');\n        },\n      }),\n      {\n        name: 'user-store',\n        partialize: (state) => ({\n          user: state.user,\n          subjects: state.subjects,\n          announcements: state.announcements,\n        }),\n      }\n    ),\n    {\n      name: 'user-store',\n    }\n  )\n);\n\n// Selectors for optimized re-renders\nexport const useCurrentUser = () => useUser((state) => state.user);\nexport const useSubjects = () => useUser((state) => state.subjects);\nexport const useMaterials = () => useUser((state) => state.materials);\nexport const useAnnouncements = () => useUser((state) => state.announcements);\nexport const useAnalytics = () => useUser((state) => state.analytics);\n\n// Actions selectors - using individual selectors to avoid object recreation\nexport const useSetUser = () => useUser((state) => state.setUser);\nexport const useSetSubjects = () => useUser((state) => state.setSubjects);\nexport const useSetMaterials = () => useUser((state) => state.setMaterials);\nexport const useSetAnnouncements = () => useUser((state) => state.setAnnouncements);\nexport const useSetAnalytics = () => useUser((state) => state.setAnalytics);\n\n// Legacy hook for backward compatibility (but avoid using this)\nexport const useUserActions = () => useUser((state) => ({\n  setUser: state.setUser,\n  setSubjects: state.setSubjects,\n  setMaterials: state.setMaterials,\n  setAnnouncements: state.setAnnouncements,\n  setAnalytics: state.setAnalytics,\n}));\n\n// Authentication helpers\nexport const useAuth = () => {\n  const user = useUser((state) => state.user);\n  const setUser = useUser((state) => state.setUser);\n\n  const login = async (email: string, password: string) => {\n    try {\n      // TODO: Implement actual login when NextAuth is configured\n      console.log('Logging in:', email, 'with password length:', password.length);\n\n      // For now, use mock data\n      setUser(mockData.user);\n      return { success: true };\n    } catch (error) {\n      console.error('Login failed:', error);\n      return { success: false, error: 'Login failed' };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      // TODO: Implement actual logout when NextAuth is configured\n      setUser(undefined);\n      // Clear other stores if needed\n      return { success: true };\n    } catch (error) {\n      console.error('Logout failed:', error);\n      return { success: false, error: 'Logout failed' };\n    }\n  };\n\n  const loadUserProfile = async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await authApi.getMe();\n      console.log('Loading user profile');\n\n      // For now, use mock data\n      setUser(mockData.user);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load user profile:', error);\n      return { success: false, error: 'Failed to load profile' };\n    }\n  };\n\n  return {\n    user,\n    login,\n    logout,\n    loadUserProfile,\n    isAuthenticated: !!user,\n    isStudent: user?.role === 'student',\n    isTeacher: user?.role === 'teacher',\n    isHOD: user?.role === 'hod',\n    isAdmin: user?.role === 'admin',\n  };\n};\n\n// Stable data loading functions (singleton pattern)\n// These functions are created once and never change, preventing infinite loops\n\nconst loadSubjects = async (): Promise<{ success: boolean; error?: string }> => {\n  try {\n    // TODO: Implement actual API call when backend is ready\n    // const response = await subjectApi.getSubjects();\n    console.log('Loading subjects');\n\n    // For now, use mock data\n    useUser.getState().setSubjects(mockData.subjects);\n    return { success: true };\n  } catch (error) {\n    console.error('Failed to load subjects:', error);\n    return { success: false, error: 'Failed to load subjects' };\n  }\n};\n\nconst loadMaterials = async (subjectId?: string): Promise<{ success: boolean; error?: string }> => {\n  try {\n    // TODO: Implement actual API call when backend is ready\n    // const response = await materialApi.getMaterials(subjectId);\n    console.log('Loading materials for subject:', subjectId);\n\n    // For now, use empty array\n    useUser.getState().setMaterials([]);\n    return { success: true };\n  } catch (error) {\n    console.error('Failed to load materials:', error);\n    return { success: false, error: 'Failed to load materials' };\n  }\n};\n\nconst loadAnnouncements = async (): Promise<{ success: boolean; error?: string }> => {\n  try {\n    // TODO: Implement actual API call when backend is ready\n    // const response = await announcementApi.getAnnouncements();\n    console.log('Loading announcements');\n\n    // For now, use empty array\n    useUser.getState().setAnnouncements([]);\n    return { success: true };\n  } catch (error) {\n    console.error('Failed to load announcements:', error);\n    return { success: false, error: 'Failed to load announcements' };\n  }\n};\n\nconst loadAnalytics = async (userRole?: string): Promise<{ success: boolean; error?: string }> => {\n  if (!userRole || (userRole !== 'hod' && userRole !== 'admin')) {\n    return { success: false, error: 'Unauthorized' };\n  }\n\n  try {\n    // TODO: Implement actual API call when backend is ready\n    // const response = await analyticsApi.getAnalytics();\n    console.log('Loading analytics');\n\n    // For now, use mock analytics data\n    const mockAnalytics: Analytics = {\n      totalUsers: 150,\n      totalStudents: 120,\n      totalTeachers: 25,\n      totalSubjects: 15,\n      totalMaterials: 85,\n      totalChats: 1250,\n      activeUsersToday: 45,\n      activeUsersThisWeek: 98,\n      popularSubjects: [\n        { subjectId: '1', subjectName: 'Japanese Language', chatCount: 450 },\n        { subjectId: '2', subjectName: 'Mathematics', chatCount: 320 },\n      ],\n      userGrowth: [\n        { date: '2024-01-01', count: 100 },\n        { date: '2024-02-01', count: 120 },\n        { date: '2024-03-01', count: 150 },\n      ],\n    };\n\n    useUser.getState().setAnalytics(mockAnalytics);\n    return { success: true };\n  } catch (error) {\n    console.error('Failed to load analytics:', error);\n    return { success: false, error: 'Failed to load analytics' };\n  }\n};\n\nconst loadAllData = async (userRole?: string): Promise<{ success: boolean }> => {\n  const results = await Promise.allSettled([\n    loadSubjects(),\n    loadAnnouncements(),\n    ...(userRole === 'hod' || userRole === 'admin' ? [loadAnalytics(userRole)] : []),\n  ]);\n\n  const failures = results.filter(result => result.status === 'rejected');\n  if (failures.length > 0) {\n    console.error('Some data failed to load:', failures);\n  }\n\n  return { success: failures.length === 0 };\n};\n\n// Export stable data loaders - these never change reference\nexport const dataLoaders = {\n  loadSubjects,\n  loadMaterials,\n  loadAnnouncements,\n  loadAnalytics,\n  loadAllData,\n} as const;\n\n// Hook for accessing data loaders (returns stable references)\nexport const useDataLoaders = () => {\n  return dataLoaders;\n};\n\n// Subject helpers\nexport const useSubjectHelpers = () => {\n  const subjects = useSubjects();\n  const user = useCurrentUser(); // Use direct selector instead of destructuring\n\n  const getSubjectById = (id: string) => {\n    return subjects.find(subject => subject.id === id);\n  };\n\n  const getSubjectsByTeacher = (teacherId: string) => {\n    return subjects.filter(subject => subject.teacherId === teacherId);\n  };\n\n  const getUserSubjects = () => {\n    if (!user) return [];\n\n    if (user.role === 'student') {\n      // Students see all subjects in their org\n      return subjects.filter(subject => subject.orgId === user.orgId);\n    } else if (user.role === 'teacher') {\n      // Teachers see only their subjects\n      return subjects.filter(subject => subject.teacherId === user.id);\n    } else {\n      // HOD and Admin see all subjects in their org\n      return subjects.filter(subject => subject.orgId === user.orgId);\n    }\n  };\n\n  return {\n    getSubjectById,\n    getSubjectsByTeacher,\n    getUserSubjects,\n  };\n};\n\n// Role-based permissions\nexport const usePermissions = () => {\n  const user = useCurrentUser(); // Use direct selector instead of destructuring\n\n  const canCreateSubject = () => {\n    return user?.role === 'teacher' || user?.role === 'admin';\n  };\n\n  const canEditSubject = (subjectId: string) => {\n    if (!user) return false;\n    if (user.role === 'admin') return true;\n    \n    const subject = useUser.getState().subjects.find(s => s.id === subjectId);\n    return user.role === 'teacher' && subject?.teacherId === user.id;\n  };\n\n  const canUploadMaterial = (subjectId: string) => {\n    return canEditSubject(subjectId);\n  };\n\n  const canCreateAnnouncement = () => {\n    return user?.role === 'hod' || user?.role === 'admin';\n  };\n\n  const canViewAnalytics = () => {\n    return user?.role === 'hod' || user?.role === 'admin';\n  };\n\n  const canManageUsers = () => {\n    return user?.role === 'admin';\n  };\n\n  return {\n    canCreateSubject,\n    canEditSubject,\n    canUploadMaterial,\n    canCreateAnnouncement,\n    canViewAnalytics,\n    canManageUsers,\n  };\n};"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;;;;;;;AAElD;AACA;AAGA;;;;AAEO,MAAM,UAAU,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,QAAQ;QACR,MAAM;QACN,UAAU,EAAE;QACZ,WAAW,EAAE;QACb,eAAe,EAAE;QACjB,WAAW;QAEX,UAAU;QACV,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK,GAAG,OAAO;QACvB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS,GAAG,OAAO;QAC3B;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;YAAc,GAAG,OAAO;QAChC;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,UAAU,MAAM,QAAQ;YACxB,eAAe,MAAM,aAAa;QACpC,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,iBAAiB,IAAM,QAAQ,CAAC,QAAU,MAAM,IAAI;AAC1D,MAAM,cAAc,IAAM,QAAQ,CAAC,QAAU,MAAM,QAAQ;AAC3D,MAAM,eAAe,IAAM,QAAQ,CAAC,QAAU,MAAM,SAAS;AAC7D,MAAM,mBAAmB,IAAM,QAAQ,CAAC,QAAU,MAAM,aAAa;AACrE,MAAM,eAAe,IAAM,QAAQ,CAAC,QAAU,MAAM,SAAS;AAG7D,MAAM,aAAa,IAAM,QAAQ,CAAC,QAAU,MAAM,OAAO;AACzD,MAAM,iBAAiB,IAAM,QAAQ,CAAC,QAAU,MAAM,WAAW;AACjE,MAAM,kBAAkB,IAAM,QAAQ,CAAC,QAAU,MAAM,YAAY;AACnE,MAAM,sBAAsB,IAAM,QAAQ,CAAC,QAAU,MAAM,gBAAgB;AAC3E,MAAM,kBAAkB,IAAM,QAAQ,CAAC,QAAU,MAAM,YAAY;AAGnE,MAAM,iBAAiB,IAAM,QAAQ,CAAC,QAAU,CAAC;YACtD,SAAS,MAAM,OAAO;YACtB,aAAa,MAAM,WAAW;YAC9B,cAAc,MAAM,YAAY;YAChC,kBAAkB,MAAM,gBAAgB;YACxC,cAAc,MAAM,YAAY;QAClC,CAAC;AAGM,MAAM,UAAU;IACrB,MAAM,OAAO,QAAQ,CAAC,QAAU,MAAM,IAAI;IAC1C,MAAM,UAAU,QAAQ,CAAC,QAAU,MAAM,OAAO;IAEhD,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,2DAA2D;YAC3D,QAAQ,GAAG,CAAC,eAAe,OAAO,yBAAyB,SAAS,MAAM;YAE1E,yBAAyB;YACzB,QAAQ,oJAAA,CAAA,WAAQ,CAAC,IAAI;YACrB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,4DAA4D;YAC5D,QAAQ;YACR,+BAA+B;YAC/B,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgB;QAClD;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,wDAAwD;YACxD,0CAA0C;YAC1C,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,QAAQ,oJAAA,CAAA,WAAQ,CAAC,IAAI;YACrB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB,WAAW,MAAM,SAAS;QAC1B,WAAW,MAAM,SAAS;QAC1B,OAAO,MAAM,SAAS;QACtB,SAAS,MAAM,SAAS;IAC1B;AACF;AAEA,oDAAoD;AACpD,+EAA+E;AAE/E,MAAM,eAAe;IACnB,IAAI;QACF,wDAAwD;QACxD,mDAAmD;QACnD,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,QAAQ,QAAQ,GAAG,WAAW,CAAC,oJAAA,CAAA,WAAQ,CAAC,QAAQ;QAChD,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;AACF;AAEA,MAAM,gBAAgB,OAAO;IAC3B,IAAI;QACF,wDAAwD;QACxD,8DAA8D;QAC9D,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,2BAA2B;QAC3B,QAAQ,QAAQ,GAAG,YAAY,CAAC,EAAE;QAClC,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA2B;IAC7D;AACF;AAEA,MAAM,oBAAoB;IACxB,IAAI;QACF,wDAAwD;QACxD,6DAA6D;QAC7D,QAAQ,GAAG,CAAC;QAEZ,2BAA2B;QAC3B,QAAQ,QAAQ,GAAG,gBAAgB,CAAC,EAAE;QACtC,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA+B;IACjE;AACF;AAEA,MAAM,gBAAgB,OAAO;IAC3B,IAAI,CAAC,YAAa,aAAa,SAAS,aAAa,SAAU;QAC7D,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;IAEA,IAAI;QACF,wDAAwD;QACxD,sDAAsD;QACtD,QAAQ,GAAG,CAAC;QAEZ,mCAAmC;QACnC,MAAM,gBAA2B;YAC/B,YAAY;YACZ,eAAe;YACf,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,kBAAkB;YAClB,qBAAqB;YACrB,iBAAiB;gBACf;oBAAE,WAAW;oBAAK,aAAa;oBAAqB,WAAW;gBAAI;gBACnE;oBAAE,WAAW;oBAAK,aAAa;oBAAe,WAAW;gBAAI;aAC9D;YACD,YAAY;gBACV;oBAAE,MAAM;oBAAc,OAAO;gBAAI;gBACjC;oBAAE,MAAM;oBAAc,OAAO;gBAAI;gBACjC;oBAAE,MAAM;oBAAc,OAAO;gBAAI;aAClC;QACH;QAEA,QAAQ,QAAQ,GAAG,YAAY,CAAC;QAChC,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA2B;IAC7D;AACF;AAEA,MAAM,cAAc,OAAO;IACzB,MAAM,UAAU,MAAM,QAAQ,UAAU,CAAC;QACvC;QACA;WACI,aAAa,SAAS,aAAa,UAAU;YAAC,cAAc;SAAU,GAAG,EAAE;KAChF;IAED,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;IAC5D,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,OAAO;QAAE,SAAS,SAAS,MAAM,KAAK;IAAE;AAC1C;AAGO,MAAM,cAAc;IACzB;IACA;IACA;IACA;IACA;AACF;AAGO,MAAM,iBAAiB;IAC5B,OAAO;AACT;AAGO,MAAM,oBAAoB;IAC/B,MAAM,WAAW;IACjB,MAAM,OAAO,kBAAkB,+CAA+C;IAE9E,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK;IAC1D;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,yCAAyC;YACzC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;QAChE,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;YAClC,mCAAmC;YACnC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE;QACjE,OAAO;YACL,8CAA8C;YAC9C,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;QAChE;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,OAAO,kBAAkB,+CAA+C;IAE9E,MAAM,mBAAmB;QACvB,OAAO,MAAM,SAAS,aAAa,MAAM,SAAS;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,KAAK,IAAI,KAAK,SAAS,OAAO;QAElC,MAAM,UAAU,QAAQ,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/D,OAAO,KAAK,IAAI,KAAK,aAAa,SAAS,cAAc,KAAK,EAAE;IAClE;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,eAAe;IACxB;IAEA,MAAM,wBAAwB;QAC5B,OAAO,MAAM,SAAS,SAAS,MAAM,SAAS;IAChD;IAEA,MAAM,mBAAmB;QACvB,OAAO,MAAM,SAAS,SAAS,MAAM,SAAS;IAChD;IAEA,MAAM,iBAAiB;QACrB,OAAO,MAAM,SAAS;IACxB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/%28dash%29/student/chat/page.tsx"], "sourcesContent": ["'use client';\n\n// Student Chat Page - AI-powered tutoring interface\n\nimport { useEffect, useState } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { \n  MessageSquare, \n  BookOpen, \n  ArrowLeft,\n  Settings,\n  Volume2,\n  Bot\n} from 'lucide-react';\nimport MessageList from '@/components/chat/MessageList';\nimport TypingBox from '@/components/chat/TypingBox';\nimport { useMessages, useIsLoading, useIsStreaming, useCurrentSubject, useSetCurrentSubject, useChatHelpers } from '@/store/useChat';\nimport { useSubjects } from '@/store/useUser';\nimport { mockData } from '@/lib/api';\n\nexport default function StudentChatPage() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const subjectId = searchParams.get('subject');\n  \n  const messages = useMessages();\n  const isLoading = useIsLoading();\n  const isStreaming = useIsStreaming();\n  const currentSubject = useCurrentSubject();\n  const subjects = useSubjects();\n  const setCurrentSubject = useSetCurrentSubject();\n  const { sendMessage, regenerateLastResponse } = useChatHelpers();\n  \n  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);\n\n  // Initialize audio element\n  useEffect(() => {\n    setAudioElement(new Audio());\n  }, []);\n\n  // Set current subject based on URL parameter\n  useEffect(() => {\n    if (subjectId) {\n      const subject = subjects.find(s => s.id === subjectId) || \n                     mockData.subjects.find(s => s.id === subjectId);\n      if (subject) {\n        setCurrentSubject(subject);\n      }\n    }\n  }, [subjectId, subjects, setCurrentSubject]);\n\n  // Handle subject change\n  const handleSubjectChange = (newSubjectId: string) => {\n    const subject = subjects.find(s => s.id === newSubjectId) || \n                   mockData.subjects.find(s => s.id === newSubjectId);\n    if (subject) {\n      setCurrentSubject(subject);\n      router.push(`/student/chat?subject=${newSubjectId}`);\n    }\n  };\n\n  // Handle audio playback\n  const handlePlayAudio = (audioUrl: string) => {\n    if (audioElement) {\n      audioElement.src = audioUrl;\n      audioElement.play().catch(console.error);\n    }\n  };\n\n  // Handle sending message\n  const handleSendMessage = async (message: string) => {\n    await sendMessage(message);\n  };\n\n  // Handle regenerating response\n  const handleRegenerateResponse = async () => {\n    await regenerateLastResponse();\n  };\n\n  const availableSubjects = subjects.length > 0 ? subjects : mockData.subjects;\n\n  return (\n    <div className=\"h-[calc(100vh-4rem)] flex flex-col\">\n      {/* Header */}\n      <div className=\"border-b bg-white p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => router.push('/student')}\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Dashboard\n            </Button>\n            \n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <Bot className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-semibold\">AI Tutor Chat</h1>\n                {currentSubject && (\n                  <p className=\"text-sm text-gray-600\">\n                    {currentSubject.name} with {currentSubject.teacherName}\n                  </p>\n                )}\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-3\">\n            {/* Subject selector */}\n            <Select\n              value={currentSubject?.id || ''}\n              onValueChange={handleSubjectChange}\n            >\n              <SelectTrigger className=\"w-48\">\n                <SelectValue placeholder=\"Select a subject\" />\n              </SelectTrigger>\n              <SelectContent>\n                {availableSubjects.map((subject) => (\n                  <SelectItem key={subject.id} value={subject.id}>\n                    <div className=\"flex items-center space-x-2\">\n                      <BookOpen className=\"h-4 w-4\" />\n                      <span>{subject.name}</span>\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            \n            {/* Chat settings */}\n            <Button variant=\"outline\" size=\"sm\">\n              <Settings className=\"h-4 w-4 mr-2\" />\n              Settings\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Subject info banner */}\n      {currentSubject && (\n        <div className=\"bg-blue-50 border-b border-blue-200 p-3\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <Badge variant=\"secondary\">{currentSubject.name}</Badge>\n              <span className=\"text-sm text-gray-600\">\n                Ask questions, get explanations, and practice with your AI tutor\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n              <MessageSquare className=\"h-4 w-4\" />\n              <span>{messages.length} messages</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* No subject selected state */}\n      {!currentSubject && (\n        <div className=\"flex-1 flex items-center justify-center\">\n          <Card className=\"w-96\">\n            <CardHeader className=\"text-center\">\n              <CardTitle className=\"flex items-center justify-center space-x-2\">\n                <BookOpen className=\"h-6 w-6\" />\n                <span>Select a Subject</span>\n              </CardTitle>\n              <CardDescription>\n                Choose a subject to start chatting with your AI tutor\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {availableSubjects.map((subject) => (\n                  <Button\n                    key={subject.id}\n                    variant=\"outline\"\n                    className=\"w-full justify-start\"\n                    onClick={() => handleSubjectChange(subject.id)}\n                  >\n                    <BookOpen className=\"h-4 w-4 mr-3\" />\n                    <div className=\"text-left\">\n                      <div className=\"font-medium\">{subject.name}</div>\n                      <div className=\"text-sm text-gray-600\">{subject.teacherName}</div>\n                    </div>\n                  </Button>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Chat interface */}\n      {currentSubject && (\n        <>\n          {/* Messages area */}\n          <div className=\"flex-1 overflow-hidden\">\n            <MessageList\n              messages={messages}\n              isLoading={isLoading}\n              isStreaming={isStreaming}\n              onRegenerateResponse={handleRegenerateResponse}\n              onPlayAudio={handlePlayAudio}\n            />\n          </div>\n\n          {/* Input area */}\n          <TypingBox\n            onSendMessage={handleSendMessage}\n            isLoading={isLoading}\n            isStreaming={isStreaming}\n            placeholder={`Ask ${currentSubject.teacherName} anything about ${currentSubject.name}...`}\n          />\n        </>\n      )}\n\n      {/* Chat features info */}\n      {currentSubject && messages.length === 0 && (\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center max-w-md\">\n          <Bot className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Welcome to AI Tutoring!\n          </h3>\n          <p className=\"text-gray-600 mb-6\">\n            I&apos;m your AI tutor for {currentSubject.name}. I can help you with:\n          </p>\n          <div className=\"grid grid-cols-2 gap-3 text-sm\">\n            <div className=\"flex items-center space-x-2 text-gray-700\">\n              <MessageSquare className=\"h-4 w-4 text-blue-600\" />\n              <span>Explanations</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-gray-700\">\n              <BookOpen className=\"h-4 w-4 text-green-600\" />\n              <span>Grammar help</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-gray-700\">\n              <Volume2 className=\"h-4 w-4 text-purple-600\" />\n              <span>Pronunciation</span>\n            </div>\n            <div className=\"flex items-center space-x-2 text-gray-700\">\n              <Bot className=\"h-4 w-4 text-orange-600\" />\n              <span>Practice</span>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA,oDAAoD;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AACA;AAtBA;;;;;;;;;;;;;;AAwBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,aAAa,GAAG,CAAC;IAEnC,MAAM,WAAW,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,YAAY,CAAA,GAAA,0JAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,cAAc,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,iBAAiB,CAAA,GAAA,0JAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,WAAW,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,oBAAoB,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD;IAC7C,MAAM,EAAE,WAAW,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,iBAAc,AAAD;IAE7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,wOAAA,CAAA,WAAQ,AAAD,EAA2B;IAE1E,2BAA2B;IAC3B,CAAA,GAAA,wOAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,IAAI;IACtB,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,wOAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAC7B,oJAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpD,IAAI,SAAS;gBACX,kBAAkB;YACpB;QACF;IACF,GAAG;QAAC;QAAW;QAAU;KAAkB;IAE3C,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAC7B,oJAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,SAAS;YACX,kBAAkB;YAClB,OAAO,IAAI,CAAC,CAAC,sBAAsB,EAAE,cAAc;QACrD;IACF;IAEA,wBAAwB;IACxB,MAAM,kBAAkB,CAAC;QACvB,IAAI,cAAc;YAChB,aAAa,GAAG,GAAG;YACnB,aAAa,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;QACzC;IACF;IAEA,yBAAyB;IACzB,MAAM,oBAAoB,OAAO;QAC/B,MAAM,YAAY;IACpB;IAEA,+BAA+B;IAC/B,MAAM,2BAA2B;QAC/B,MAAM;IACR;IAEA,MAAM,oBAAoB,SAAS,MAAM,GAAG,IAAI,WAAW,oJAAA,CAAA,WAAQ,CAAC,QAAQ;IAE5E,qBACE,iRAAC;QAAI,WAAU;;0BAEb,iRAAC;gBAAI,WAAU;0BACb,cAAA,iRAAC;oBAAI,WAAU;;sCACb,iRAAC;4BAAI,WAAU;;8CACb,iRAAC,qKAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;;sDAE3B,iRAAC,mPAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIxC,iRAAC;oCAAI,WAAU;;sDACb,iRAAC;4CAAI,WAAU;sDACb,cAAA,iRAAC,mOAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,iRAAC;;8DACC,iRAAC;oDAAG,WAAU;8DAAwB;;;;;;gDACrC,gCACC,iRAAC;oDAAE,WAAU;;wDACV,eAAe,IAAI;wDAAC;wDAAO,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhE,iRAAC;4BAAI,WAAU;;8CAEb,iRAAC,qKAAA,CAAA,SAAM;oCACL,OAAO,gBAAgB,MAAM;oCAC7B,eAAe;;sDAEf,iRAAC,qKAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,iRAAC,qKAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,iRAAC,qKAAA,CAAA,gBAAa;sDACX,kBAAkB,GAAG,CAAC,CAAC,wBACtB,iRAAC,qKAAA,CAAA,aAAU;oDAAkB,OAAO,QAAQ,EAAE;8DAC5C,cAAA,iRAAC;wDAAI,WAAU;;0EACb,iRAAC,iPAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,iRAAC;0EAAM,QAAQ,IAAI;;;;;;;;;;;;mDAHN,QAAQ,EAAE;;;;;;;;;;;;;;;;8CAWjC,iRAAC,qKAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,iRAAC,6OAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAQ5C,gCACC,iRAAC;gBAAI,WAAU;0BACb,cAAA,iRAAC;oBAAI,WAAU;;sCACb,iRAAC;4BAAI,WAAU;;8CACb,iRAAC,oKAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAa,eAAe,IAAI;;;;;;8CAC/C,iRAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAI1C,iRAAC;4BAAI,WAAU;;8CACb,iRAAC,2PAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,iRAAC;;wCAAM,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;YAO9B,CAAC,gCACA,iRAAC;gBAAI,WAAU;0BACb,cAAA,iRAAC,mKAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,iRAAC,mKAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,iRAAC,mKAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,iRAAC,iPAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,iRAAC;sDAAK;;;;;;;;;;;;8CAER,iRAAC,mKAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,iRAAC,mKAAA,CAAA,cAAW;sCACV,cAAA,iRAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,iRAAC,qKAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,oBAAoB,QAAQ,EAAE;;0DAE7C,iRAAC,iPAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,iRAAC;gDAAI,WAAU;;kEACb,iRAAC;wDAAI,WAAU;kEAAe,QAAQ,IAAI;;;;;;kEAC1C,iRAAC;wDAAI,WAAU;kEAAyB,QAAQ,WAAW;;;;;;;;;;;;;uCARxD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YAmB5B,gCACC;;kCAEE,iRAAC;wBAAI,WAAU;kCACb,cAAA,iRAAC,4KAAA,CAAA,UAAW;4BACV,UAAU;4BACV,WAAW;4BACX,aAAa;4BACb,sBAAsB;4BACtB,aAAa;;;;;;;;;;;kCAKjB,iRAAC,0KAAA,CAAA,UAAS;wBACR,eAAe;wBACf,WAAW;wBACX,aAAa;wBACb,aAAa,CAAC,IAAI,EAAE,eAAe,WAAW,CAAC,gBAAgB,EAAE,eAAe,IAAI,CAAC,GAAG,CAAC;;;;;;;;YAM9F,kBAAkB,SAAS,MAAM,KAAK,mBACrC,iRAAC;gBAAI,WAAU;;kCACb,iRAAC,mOAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;kCACf,iRAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,iRAAC;wBAAE,WAAU;;4BAAqB;4BACJ,eAAe,IAAI;4BAAC;;;;;;;kCAElD,iRAAC;wBAAI,WAAU;;0CACb,iRAAC;gCAAI,WAAU;;kDACb,iRAAC,2PAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,iRAAC;kDAAK;;;;;;;;;;;;0CAER,iRAAC;gCAAI,WAAU;;kDACb,iRAAC,iPAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,iRAAC;kDAAK;;;;;;;;;;;;0CAER,iRAAC;gCAAI,WAAU;;kDACb,iRAAC,+OAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,iRAAC;kDAAK;;;;;;;;;;;;0CAER,iRAAC;gCAAI,WAAU;;kDACb,iRAAC,mOAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,iRAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}]}