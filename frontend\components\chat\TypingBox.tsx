'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { 
  Send, 
  Mic, 
  MicOff, 
  Paperclip, 
  Smile,
  Languages,
  Volume2
} from 'lucide-react';
import { useIsLoading, useIsStreaming, useChatHelpers } from '@/store/useChat';
import { useCurrentSubject } from '@/store/useChat';

interface TypingBoxProps {
  className?: string;
  placeholder?: string;
  maxLength?: number;
  onSendMessage?: (message: string, options?: MessageOptions) => void;
  disabled?: boolean;
}

interface MessageOptions {
  hasAudio?: boolean;
  language?: string;
  attachments?: File[];
}

export function TypingBox({
  className = "",
  placeholder = "Type your question here...",
  maxLength = 1000,
  onSendMessage,
  disabled = false
}: TypingBoxProps) {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [attachments, setAttachments] = useState<File[]>([]);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  
  const isLoading = useIsLoading();
  const isStreaming = useIsStreaming();
  const currentSubject = useCurrentSubject();
  const { sendMessage } = useChatHelpers();

  const isDisabled = disabled || isLoading || isStreaming;

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  const handleSendMessage = async () => {
    if (!message.trim() || isDisabled) return;

    const messageOptions: MessageOptions = {
      hasAudio: isRecording,
      language: selectedLanguage,
      attachments: attachments.length > 0 ? attachments : undefined
    };

    try {
      // Use the chat helper to send message
      await sendMessage(message.trim());
      
      // Call custom handler if provided
      onSendMessage?.(message.trim(), messageOptions);
      
      // Reset form
      setMessage('');
      setAttachments([]);
      
      // Focus back to textarea
      textareaRef.current?.focus();
    } catch (error) {
      console.error('Failed to send message:', error);
      // TODO: Show error toast
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVoiceRecording = async () => {
    if (!isRecording) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const mediaRecorder = new MediaRecorder(stream);
        mediaRecorderRef.current = mediaRecorder;
        
        const audioChunks: BlobPart[] = [];
        
        mediaRecorder.ondataavailable = (event) => {
          audioChunks.push(event.data);
        };
        
        mediaRecorder.onstop = () => {
          const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
          // TODO: Convert audio to text using speech recognition API
          console.log('Audio recorded:', audioBlob);
          
          // Mock transcription for now
          setMessage(prev => prev + ' [Voice message transcribed]');
        };
        
        mediaRecorder.start();
        setIsRecording(true);
      } catch (error) {
        console.error('Failed to start recording:', error);
        // TODO: Show error toast
      }
    } else {
      mediaRecorderRef.current?.stop();
      mediaRecorderRef.current?.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
    }
  };

  const handleFileAttachment = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    // TODO: Update UI language or translation settings
  };

  const insertEmoji = (emoji: string) => {
    setMessage(prev => prev + emoji);
    textareaRef.current?.focus();
  };

  const commonEmojis = ['😊', '👍', '❤️', '😂', '🤔', '👏', '🙏', '✨'];
  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
  ];

  return (
    <Card className={`p-4 ${className}`}>
      {/* Subject Context */}
      {currentSubject && (
        <div className="mb-3 p-2 bg-blue-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm text-blue-700">
              Chatting about: {currentSubject.name}
            </span>
          </div>
        </div>
      )}

      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="mb-3 space-y-2">
          {attachments.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div className="flex items-center space-x-2">
                <Paperclip className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-700">{file.name}</span>
                <span className="text-xs text-gray-500">
                  ({(file.size / 1024).toFixed(1)} KB)
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeAttachment(index)}
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Main Input Area */}
      <div className="space-y-3">
        <div className="relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            maxLength={maxLength}
            disabled={isDisabled}
            className="min-h-[60px] max-h-[200px] resize-none pr-12"
            rows={1}
          />
          
          {/* Character Count */}
          <div className="absolute bottom-2 right-2 text-xs text-gray-400">
            {message.length}/{maxLength}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* File Attachment */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              disabled={isDisabled}
              className="h-8 w-8 p-0"
            >
              <Paperclip className="w-4 h-4" />
            </Button>
            
            {/* Voice Recording */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleVoiceRecording}
              disabled={isDisabled}
              className={`h-8 w-8 p-0 ${isRecording ? 'text-red-500' : ''}`}
            >
              {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
            </Button>

            {/* Emoji Picker */}
            <div className="relative group">
              <Button
                variant="ghost"
                size="sm"
                disabled={isDisabled}
                className="h-8 w-8 p-0"
              >
                <Smile className="w-4 h-4" />
              </Button>
              
              {/* Emoji Dropdown */}
              <div className="absolute bottom-full left-0 mb-2 p-2 bg-white border rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
                <div className="grid grid-cols-4 gap-1">
                  {commonEmojis.map((emoji, index) => (
                    <button
                      key={index}
                      onClick={() => insertEmoji(emoji)}
                      className="p-1 hover:bg-gray-100 rounded text-lg"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Language Selector */}
            <div className="relative group">
              <Button
                variant="ghost"
                size="sm"
                disabled={isDisabled}
                className="h-8 w-8 p-0"
              >
                <Languages className="w-4 h-4" />
              </Button>
              
              {/* Language Dropdown */}
              <div className="absolute bottom-full left-0 mb-2 p-2 bg-white border rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all min-w-[120px]">
                {languages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => handleLanguageChange(lang.code)}
                    className={`w-full text-left p-2 hover:bg-gray-100 rounded flex items-center space-x-2 ${
                      selectedLanguage === lang.code ? 'bg-blue-50' : ''
                    }`}
                  >
                    <span>{lang.flag}</span>
                    <span className="text-sm">{lang.name}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Send Button */}
          <Button
            onClick={handleSendMessage}
            disabled={!message.trim() || isDisabled}
            size="sm"
            className="h-8"
          >
            {isLoading || isStreaming ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
            <span className="ml-2">Send</span>
          </Button>
        </div>

        {/* Recording Indicator */}
        {isRecording && (
          <div className="flex items-center justify-center space-x-2 p-2 bg-red-50 rounded-lg">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-red-700">Recording... Click mic to stop</span>
          </div>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.mp3,.mp4"
        onChange={handleFileAttachment}
        className="hidden"
      />
    </Card>
  );
}
