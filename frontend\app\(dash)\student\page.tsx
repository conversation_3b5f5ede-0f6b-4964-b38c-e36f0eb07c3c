import { Suspense } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BookOpen, 
  MessageSquare, 
  Clock, 
  TrendingUp,
  Play,
  Download,
  Star,
  Users,
  Calendar,
  Target
} from 'lucide-react';
import Link from 'next/link';

// This would be fetched from API in production
const mockStudentData = {
  user: {
    name: '<PERSON>',
    level: 'Intermediate',
    streak: 7,
    totalPoints: 1250,
    rank: 15
  },
  subjects: [
    {
      id: '1',
      name: 'Japanese Language',
      teacher: '<PERSON> Sense<PERSON>',
      progress: 75,
      nextLesson: '<PERSON><PERSON> (Honorific Language)',
      totalLessons: 24,
      completedLessons: 18,
      lastActivity: '2 hours ago'
    },
    {
      id: '2', 
      name: 'Mathematics',
      teacher: 'Dr<PERSON> <PERSON>',
      progress: 60,
      nextLesson: 'Calculus Integration',
      totalLessons: 30,
      completedLessons: 18,
      lastActivity: '1 day ago'
    }
  ],
  recentMaterials: [
    {
      id: '1',
      title: 'Hiragana Practice Sheet',
      subject: 'Japanese Language',
      type: 'PDF',
      uploadedAt: '2024-01-15',
      downloaded: false
    },
    {
      id: '2',
      title: 'Grammar Patterns N3',
      subject: 'Japanese Language', 
      type: 'Video',
      uploadedAt: '2024-01-14',
      downloaded: true
    }
  ],
  upcomingEvents: [
    {
      id: '1',
      title: 'Japanese Conversation Practice',
      date: '2024-01-20',
      time: '14:00',
      type: 'Live Session'
    },
    {
      id: '2',
      title: 'Math Quiz - Derivatives',
      date: '2024-01-22',
      time: '10:00', 
      type: 'Assessment'
    }
  ],
  achievements: [
    { id: '1', title: '7-Day Streak', icon: '🔥', earned: true },
    { id: '2', title: 'Quick Learner', icon: '⚡', earned: true },
    { id: '3', title: 'Perfect Score', icon: '💯', earned: false },
    { id: '4', title: 'Helper', icon: '🤝', earned: false }
  ]
};

export default function StudentDashboard() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {mockStudentData.user.name}! 👋
          </h1>
          <p className="text-gray-600 mt-1">
            Ready to continue your learning journey?
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Badge variant="secondary" className="px-3 py-1">
            <Star className="w-4 h-4 mr-1" />
            {mockStudentData.user.level}
          </Badge>
          <Badge variant="outline" className="px-3 py-1">
            🔥 {mockStudentData.user.streak} day streak
          </Badge>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Points</p>
                <p className="text-xl font-bold">{mockStudentData.user.totalPoints}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Class Rank</p>
                <p className="text-xl font-bold">#{mockStudentData.user.rank}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Active Subjects</p>
                <p className="text-xl font-bold">{mockStudentData.subjects.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Study Streak</p>
                <p className="text-xl font-bold">{mockStudentData.user.streak} days</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* My Subjects */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BookOpen className="w-5 h-5" />
                <span>My Subjects</span>
              </CardTitle>
              <CardDescription>
                Continue learning with your enrolled subjects
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockStudentData.subjects.map((subject) => (
                <div key={subject.id} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-lg">{subject.name}</h3>
                      <p className="text-sm text-gray-600">by {subject.teacher}</p>
                    </div>
                    <Badge variant="outline">
                      {subject.completedLessons}/{subject.totalLessons} lessons
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Progress</span>
                      <span>{subject.progress}%</span>
                    </div>
                    <Progress value={subject.progress} className="h-2" />
                  </div>
                  
                  <div className="flex items-center justify-between mt-3">
                    <div>
                      <p className="text-sm font-medium">Next: {subject.nextLesson}</p>
                      <p className="text-xs text-gray-500">Last activity: {subject.lastActivity}</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/student/subjects/${subject.id}`}>
                          <Play className="w-4 h-4 mr-1" />
                          Continue
                        </Link>
                      </Button>
                      <Button size="sm" asChild>
                        <Link href={`/student/chat?subject=${subject.id}`}>
                          <MessageSquare className="w-4 h-4 mr-1" />
                          Chat
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Recent Materials */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Materials</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {mockStudentData.recentMaterials.map((material) => (
                <div key={material.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{material.title}</h4>
                    <p className="text-xs text-gray-500">{material.subject}</p>
                    <p className="text-xs text-gray-400">{material.uploadedAt}</p>
                  </div>
                  <Button size="sm" variant="ghost">
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              ))}
              
              <Button variant="outline" className="w-full" asChild>
                <Link href="/student/materials">View All Materials</Link>
              </Button>
            </CardContent>
          </Card>

          {/* Upcoming Events */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>Upcoming</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {mockStudentData.upcomingEvents.map((event) => (
                <div key={event.id} className="p-3 border rounded-lg">
                  <h4 className="font-medium text-sm">{event.title}</h4>
                  <p className="text-xs text-gray-500">{event.type}</p>
                  <p className="text-xs text-gray-600">{event.date} at {event.time}</p>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Achievements */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Achievements</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                {mockStudentData.achievements.map((achievement) => (
                  <div 
                    key={achievement.id}
                    className={`p-3 border rounded-lg text-center ${
                      achievement.earned 
                        ? 'bg-yellow-50 border-yellow-200' 
                        : 'bg-gray-50 border-gray-200 opacity-50'
                    }`}
                  >
                    <div className="text-2xl mb-1">{achievement.icon}</div>
                    <p className="text-xs font-medium">{achievement.title}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Jump into your learning activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button className="h-20 flex-col space-y-2" asChild>
              <Link href="/student/chat">
                <MessageSquare className="w-6 h-6" />
                <span>Start AI Chat</span>
              </Link>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col space-y-2" asChild>
              <Link href="/student/materials">
                <BookOpen className="w-6 h-6" />
                <span>Browse Materials</span>
              </Link>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col space-y-2" asChild>
              <Link href="/student/practice">
                <Target className="w-6 h-6" />
                <span>Practice Tests</span>
              </Link>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col space-y-2" asChild>
              <Link href="/student/progress">
                <TrendingUp className="w-6 h-6" />
                <span>View Progress</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
