// Zustand store for Chat state management

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { ChatMessage, Subject, ChatStore } from '@/types';
import { chatApi } from '@/lib/api';

export const useChat = create<ChatStore>()(
  devtools(
    persist(
      (set) => ({
        // State
        messages: [],
        isLoading: false,
        isStreaming: false,
        currentSubject: undefined,

        // Actions
        addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
          const newMessage: ChatMessage = {
            ...message,
            id: crypto.randomUUID(),
            timestamp: new Date(),
          };

          set(
            (state) => ({
              messages: [...state.messages, newMessage],
            }),
            false,
            'addMessage'
          );
        },

        updateLastMessage: (updates: Partial<ChatMessage>) => {
          set(
            (state) => {
              const messages = [...state.messages];
              const lastIndex = messages.length - 1;
              
              if (lastIndex >= 0) {
                messages[lastIndex] = { ...messages[lastIndex], ...updates };
              }
              
              return { messages };
            },
            false,
            'updateLastMessage'
          );
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading }, false, 'setLoading');
        },

        setStreaming: (streaming: boolean) => {
          set({ isStreaming: streaming }, false, 'setStreaming');
        },

        setCurrentSubject: (subject: Subject | undefined) => {
          set({ currentSubject: subject }, false, 'setCurrentSubject');
        },

        clearMessages: () => {
          set({ messages: [] }, false, 'clearMessages');
        },
      }),
      {
        name: 'chat-store',
        partialize: (state) => ({
          messages: state.messages,
          currentSubject: state.currentSubject,
        }),
      }
    ),
    {
      name: 'chat-store',
    }
  )
);

// Selectors for optimized re-renders
export const useMessages = () => useChat((state) => state.messages);
export const useIsLoading = () => useChat((state) => state.isLoading);
export const useIsStreaming = () => useChat((state) => state.isStreaming);
export const useCurrentSubject = () => useChat((state) => state.currentSubject);

// Actions selectors
export const useChatActions = () => useChat((state) => ({
  addMessage: state.addMessage,
  updateLastMessage: state.updateLastMessage,
  setLoading: state.setLoading,
  setStreaming: state.setStreaming,
  setCurrentSubject: state.setCurrentSubject,
  clearMessages: state.clearMessages,
}));

// Helper hooks for chat functionality
export const useChatHelpers = () => {
  const {
    addMessage,
    updateLastMessage,
    setLoading,
    setStreaming,
  } = useChatActions();
  const currentSubject = useCurrentSubject();

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Add user message
    addMessage({
      content: content.trim(),
      role: 'user',
      subjectId: currentSubject?.id,
    });

    // Add placeholder assistant message
    addMessage({
      content: '',
      role: 'assistant',
      subjectId: currentSubject?.id,
    });

    setLoading(true);
    setStreaming(true);

    try {
      // Stream the response
      let fullResponse = '';
      
      for await (const chunk of chatApi.sendMessage(content, currentSubject?.id)) {
        if (chunk.type === 'text') {
          fullResponse += chunk.content;
          updateLastMessage({ content: fullResponse });
        } else if (chunk.type === 'audio' && chunk.audioUrl) {
          updateLastMessage({ 
            hasAudio: true, 
            audioUrl: chunk.audioUrl,
            visemes: chunk.visemes 
          });
        } else if (chunk.type === 'complete') {
          // Final message update
          updateLastMessage({ content: fullResponse + chunk.content });
          break;
        }
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      updateLastMessage({ 
        content: 'Sorry, I encountered an error. Please try again.' 
      });
    } finally {
      setLoading(false);
      setStreaming(false);
    }
  };

  const regenerateLastResponse = async () => {
    const messages = useChat.getState().messages;
    const lastUserMessage = [...messages].reverse().find(m => m.role === 'user');
    
    if (!lastUserMessage) return;

    // Remove the last assistant message if it exists
    const filteredMessages = messages.filter((_, index) => 
      index !== messages.length - 1 || messages[messages.length - 1].role !== 'assistant'
    );
    
    useChat.setState({ messages: filteredMessages });
    
    // Resend the last user message
    await sendMessage(lastUserMessage.content);
  };

  const loadChatHistory = async (subjectId?: string) => {
    setLoading(true);
    
    try {
      // TODO: Implement actual API call when backend is ready
      // const response = await chatApi.getChatHistory(subjectId);
      console.log('Loading chat history for subject:', subjectId);
      
      // For now, use mock data or keep existing messages
    } catch (error) {
      console.error('Failed to load chat history:', error);
    } finally {
      setLoading(false);
    }
  };

  return {
    sendMessage,
    regenerateLastResponse,
    loadChatHistory,
  };
};

// Message filtering and search
export const useMessageFilters = () => {
  const messages = useMessages();

  const getMessagesBySubject = (subjectId: string) => {
    return messages.filter(message => message.subjectId === subjectId);
  };

  const searchMessages = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    return messages.filter(message => 
      message.content.toLowerCase().includes(lowercaseQuery)
    );
  };

  const getMessagesWithAudio = () => {
    return messages.filter(message => message.hasAudio);
  };

  const getRecentMessages = (limit: number = 10) => {
    return messages.slice(-limit);
  };

  return {
    getMessagesBySubject,
    searchMessages,
    getMessagesWithAudio,
    getRecentMessages,
  };
};

// Chat statistics
export const useChatStats = () => {
  const messages = useMessages();

  const getTotalMessages = () => messages.length;
  
  const getUserMessageCount = () => 
    messages.filter(m => m.role === 'user').length;
  
  const getAssistantMessageCount = () => 
    messages.filter(m => m.role === 'assistant').length;
  
  const getMessagesWithAudioCount = () => 
    messages.filter(m => m.hasAudio).length;
  
  const getAverageMessageLength = () => {
    if (messages.length === 0) return 0;
    const totalLength = messages.reduce((sum, m) => sum + m.content.length, 0);
    return Math.round(totalLength / messages.length);
  };

  const getMessagesByDate = () => {
    const messagesByDate: Record<string, number> = {};
    
    messages.forEach(message => {
      const date = message.timestamp.toISOString().split('T')[0];
      messagesByDate[date] = (messagesByDate[date] || 0) + 1;
    });
    
    return messagesByDate;
  };

  return {
    getTotalMessages,
    getUserMessageCount,
    getAssistantMessageCount,
    getMessagesWithAudioCount,
    getAverageMessageLength,
    getMessagesByDate,
  };
};
