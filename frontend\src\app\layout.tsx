import type { Metada<PERSON> } from "next";
import "./globals.css";
import AuthProvider from "@/components/auth/AuthProvider";
import { fontClassNames } from "@/lib/fonts";

export const metadata: Metadata = {
  title: "AI Tutor Platform",
  description: "Personalized AI-powered learning experience",
  icons: {
    icon: "/icon.svg",
    shortcut: "/icon.svg",
    apple: "/icon.svg",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" data-scroll-behavior="smooth">
      <body
        className={`${fontClassNames} antialiased`}
      >
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
