'use client';

import React from 'react';

interface FontErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface FontErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export class FontErrorBoundary extends React.Component<
  FontErrorBoundaryProps,
  FontErrorBoundaryState
> {
  constructor(props: FontErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): FontErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log font loading errors
    console.warn('Font loading error caught:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI with system fonts
      return (
        <div style={{ fontFamily: 'system-ui, sans-serif' }}>
          {this.props.fallback || this.props.children}
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for detecting font loading issues
export function useFontLoadingStatus() {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    // Check if fonts are loaded
    if (typeof document !== 'undefined' && 'fonts' in document) {
      document.fonts.ready
        .then(() => {
          setIsLoaded(true);
        })
        .catch((error) => {
          console.warn('Font loading failed:', error);
          setHasError(true);
          setIsLoaded(true); // Still set loaded to continue with fallbacks
        });
    } else {
      // Fallback for browsers without font loading API
      setTimeout(() => setIsLoaded(true), 100);
    }
  }, []);

  return { isLoaded, hasError };
}
