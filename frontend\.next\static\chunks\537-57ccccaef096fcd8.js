"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[537],{228:(e,t,r)=>{r.d(t,{DY:()=>o,IU:()=>l,uv:()=>s});let n=[];function i(e,t,r=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let n=e.length;if(t.length!==n)return!1;for(let i=0;i<n;i++)if(!r(e[i],t[i]))return!1;return!0}function a(e,t=null,r=!1,o={}){for(let a of(null===t&&(t=[e]),n))if(i(t,a.keys,a.equal)){if(r)return;if(Object.prototype.hasOwnProperty.call(a,"error"))throw a.error;if(Object.prototype.hasOwnProperty.call(a,"response"))return o.lifespan&&o.lifespan>0&&(a.timeout&&clearTimeout(a.timeout),a.timeout=setTimeout(a.remove,o.lifespan)),a.response;if(!r)throw a.promise}let s={keys:t,equal:o.equal,remove:()=>{let e=n.indexOf(s);-1!==e&&n.splice(e,1)},promise:("object"==typeof e&&"function"==typeof e.then?e:e(...t)).then(e=>{s.response=e,o.lifespan&&o.lifespan>0&&(s.timeout=setTimeout(s.remove,o.lifespan))}).catch(e=>s.error=e)};if(n.push(s),!r)throw s.promise}let o=(e,t,r)=>a(e,t,!1,r),s=(e,t,r)=>void a(e,t,!0,r),l=e=>{if(void 0===e||0===e.length)n.splice(0,n.length);else{let t=n.find(t=>i(e,t.keys,t.equal));t&&t.remove()}}},461:(e,t,r)=>{let n,i,a,o,s;r.d(t,{B:()=>D,C:()=>ee,D:()=>et,E:()=>T,G:()=>ei,a:()=>R,b:()=>I,c:()=>eM,d:()=>ex,e:()=>ec,f:()=>eJ,i:()=>w,o:()=>eI,s:()=>Q,u:()=>F});var l=r(2115),c=r.t(l,2),u=r(1933),d=r(3264),f=r(7431),h=r(5643),p=r(2421);let{useSyncExternalStoreWithSelector:A}=h,m=(e,t)=>{let r=(0,p.y)(e),n=(e,n=t)=>(function(e,t=e=>e,r){let n=A(e.subscribe,e.getState,e.getInitialState,t,r);return l.useDebugValue(n),n})(r,e,n);return Object.assign(n,r),n},v=(e,t)=>e?m(e,t):m;var g=r(228),B=r(5220),C=r.n(B),b=r(4342),y=r(5155),E=r(6354);function M(e){let t=e.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}r(4561),c.act;let w=e=>e&&e.hasOwnProperty("current"),x=e=>null!=e&&("string"==typeof e||"number"==typeof e||e.isColor),I=((e,t)=>"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative"))()?l.useLayoutEffect:l.useEffect;function R(e){let t=l.useRef(e);return I(()=>void(t.current=e),[e]),t}function F(){let e=(0,E.u5)(),t=(0,E.y3)();return l.useMemo(()=>({children:r})=>{let n=(0,E.Nz)(e,!0,e=>e.type===l.StrictMode)?l.StrictMode:l.Fragment;return(0,y.jsx)(n,{children:(0,y.jsx)(t,{children:r})})},[e,t])}function D({set:e}){return I(()=>(e(new Promise(()=>null)),()=>e(!1)),[e]),null}let T=(e=>((e=class extends l.Component{constructor(...e){super(...e),this.state={error:!1}}componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}}).getDerivedStateFromError=()=>({error:!0}),e))();function G(e){var t;let r="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],r),e[1]):e}function S(e){var t;return null==(t=e.__r3f)?void 0:t.root.getState()}let P={obj:e=>e===Object(e)&&!P.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,nul:e=>null===e,arr:e=>Array.isArray(e),equ(e,t,{arrays:r="shallow",objects:n="reference",strict:i=!0}={}){let a;if(typeof e!=typeof t||!!e!=!!t)return!1;if(P.str(e)||P.num(e)||P.boo(e))return e===t;let o=P.obj(e);if(o&&"reference"===n)return e===t;let s=P.arr(e);if(s&&"reference"===r)return e===t;if((s||o)&&e===t)return!0;for(a in e)if(!(a in t))return!1;if(o&&"shallow"===r&&"shallow"===n){for(a in i?t:e)if(!P.equ(e[a],t[a],{strict:i,objects:"reference"}))return!1}else for(a in i?t:e)if(e[a]!==t[a])return!1;if(P.und(a)){if(s&&0===e.length&&0===t.length||o&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}},_=["children","key","ref"];function H(e,t,r,n){let i=null==e?void 0:e.__r3f;return!i&&(i={root:t,type:r,parent:null,children:[],props:function(e){let t={};for(let r in e)_.includes(r)||(t[r]=e[r]);return t}(n),object:e,eventCount:0,handlers:{},isHidden:!1},e&&(e.__r3f=i)),i}function O(e,t){let r=e[t];if(!t.includes("-"))return{root:e,key:t,target:r};for(let i of(r=e,t.split("-"))){var n;t=i,e=r,r=null==(n=r)?void 0:n[t]}return{root:e,key:t,target:r}}let L=/-\d+$/;function U(e,t){if(P.str(t.props.attach)){if(L.test(t.props.attach)){let r=t.props.attach.replace(L,""),{root:n,key:i}=O(e.object,r);Array.isArray(n[i])||(n[i]=[])}let{root:r,key:n}=O(e.object,t.props.attach);t.previousAttach=r[n],r[n]=t.object}else P.fun(t.props.attach)&&(t.previousAttach=t.props.attach(e.object,t.object))}function k(e,t){if(P.str(t.props.attach)){let{root:r,key:n}=O(e.object,t.props.attach),i=t.previousAttach;void 0===i?delete r[n]:r[n]=i}else null==t.previousAttach||t.previousAttach(e.object,t.object);delete t.previousAttach}let J=[..._,"args","dispose","attach","object","onUpdate","dispose"],j=new Map,N=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],K=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function Q(e,t){var r,n;let i=e.__r3f,a=i&&M(i).getState(),o=null==i?void 0:i.eventCount;for(let r in t){let o=t[r];if(J.includes(r))continue;if(i&&K.test(r)){"function"==typeof o?i.handlers[r]=o:delete i.handlers[r],i.eventCount=Object.keys(i.handlers).length;continue}if(void 0===o)continue;let{root:s,key:l,target:c}=O(e,r);c instanceof d.zgK&&o instanceof d.zgK?c.mask=o.mask:c instanceof d.Q1f&&x(o)?c.set(o):null!==c&&"object"==typeof c&&"function"==typeof c.set&&"function"==typeof c.copy&&null!=o&&o.constructor&&c.constructor===o.constructor?c.copy(o):null!==c&&"object"==typeof c&&"function"==typeof c.set&&Array.isArray(o)?"function"==typeof c.fromArray?c.fromArray(o):c.set(...o):null!==c&&"object"==typeof c&&"function"==typeof c.set&&"number"==typeof o?"function"==typeof c.setScalar?c.setScalar(o):c.set(o):(s[l]=o,a&&!a.linear&&N.includes(l)&&null!=(n=s[l])&&n.isTexture&&s[l].format===d.GWd&&s[l].type===d.OUM&&(s[l].colorSpace=d.er$))}if(null!=i&&i.parent&&null!=a&&a.internal&&null!=(r=i.object)&&r.isObject3D&&o!==i.eventCount){let e=i.object,t=a.internal.interaction.indexOf(e);t>-1&&a.internal.interaction.splice(t,1),i.eventCount&&null!==e.raycast&&a.internal.interaction.push(e)}return i&&void 0===i.props.attach&&(i.object.isBufferGeometry?i.props.attach="geometry":i.object.isMaterial&&(i.props.attach="material")),i&&X(i),e}function X(e){var t;if(!e.parent)return;null==e.props.onUpdate||e.props.onUpdate(e.object);let r=null==(t=e.root)||null==t.getState?void 0:t.getState();r&&0===r.internal.frames&&r.invalidate()}function Y(e,t){!e.manual&&(e&&e.isOrthographicCamera?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix())}let W=e=>null==e?void 0:e.isObject3D;function z(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function Z(e,t,r,n){let i=r.get(t);i&&(r.delete(t),0===r.size&&(e.delete(n),i.target.releasePointerCapture(n)))}let q=e=>!!(null!=e&&e.render),V=l.createContext(null);function $(){let e=l.useContext(V);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function ee(e=e=>e,t){return $()(e,t)}function et(e,t=0){let r=$(),n=r.getState().internal.subscribe,i=R(e);return I(()=>n(i,t,r),[t,n,r]),null}let er=new WeakMap;function en(e,t){return function(r,...n){var i;let a;return"function"==typeof r&&(null==r||null==(i=r.prototype)?void 0:i.constructor)===r?(a=er.get(r))||(a=new r,er.set(r,a)):a=r,e&&e(a),Promise.all(n.map(e=>new Promise((r,n)=>a.load(e,e=>{W(null==e?void 0:e.scene)&&Object.assign(e,function(e){let t={nodes:{},materials:{},meshes:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material),e.isMesh&&!t.meshes[e.name]&&(t.meshes[e.name]=e)}),t}(e.scene)),r(e)},t,t=>n(Error(`Could not load ${e}: ${null==t?void 0:t.message}`))))))}}function ei(e,t,r,n){let i=Array.isArray(t)?t:[t],a=(0,g.DY)(en(r,n),[e,...i],{equal:P.equ});return Array.isArray(t)?a:a[0]}ei.preload=function(e,t,r){let n=Array.isArray(t)?t:[t];return(0,g.uv)(en(r),[e,...n])},ei.clear=function(e,t){let r=Array.isArray(t)?t:[t];return(0,g.IU)([e,...r])};let ea={},eo=/^three(?=[A-Z])/,es=e=>`${e[0].toUpperCase()}${e.slice(1)}`,el=0;function ec(e){if("function"==typeof e){let t=`${el++}`;return ea[t]=e,t}Object.assign(ea,e)}function eu(e,t){let r=es(e),n=ea[r];if("primitive"!==e&&!n)throw Error(`R3F: ${r} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if("primitive"===e&&!t.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==t.args&&!Array.isArray(t.args))throw Error("R3F: The args prop must be an array!")}function ed(e){if(e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?U(e.parent,e):W(e.object)&&!1!==e.props.visible&&(e.object.visible=!0),e.isHidden=!1,X(e)}}function ef(e,t,r){let n=t.root.getState();if(e.parent||e.object===n.scene){if(!t.object){var i,a;let e=ea[es(t.type)];t.object=null!=(i=t.props.object)?i:new e(...null!=(a=t.props.args)?a:[]),t.object.__r3f=t}if(Q(t.object,t.props),t.props.attach)U(e,t);else if(W(t.object)&&W(e.object)){let n=e.object.children.indexOf(null==r?void 0:r.object);if(r&&-1!==n){let r=e.object.children.indexOf(t.object);-1!==r?(e.object.children.splice(r,1),e.object.children.splice(r<n?n-1:n,0,t.object)):(t.object.parent=e.object,e.object.children.splice(n,0,t.object),t.object.dispatchEvent({type:"added"}),e.object.dispatchEvent({type:"childadded",child:t.object}))}else e.object.add(t.object)}for(let e of t.children)ef(t,e);X(t)}}function eh(e,t){t&&(t.parent=e,e.children.push(t),ef(e,t))}function ep(e,t,r){if(!t||!r)return;t.parent=e;let n=e.children.indexOf(r);-1!==n?e.children.splice(n,0,t):e.children.push(t),ef(e,t,r)}function eA(e){if("function"==typeof e.dispose){let t=()=>{try{e.dispose()}catch{}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?t():(0,b.unstable_scheduleCallback)(b.unstable_IdlePriority,t)}}function em(e,t,r){if(!t)return;t.parent=null;let n=e.children.indexOf(t);-1!==n&&e.children.splice(n,1),t.props.attach?k(e,t):W(t.object)&&W(e.object)&&(e.object.remove(t.object),function(e,t){let{internal:r}=e.getState();r.interaction=r.interaction.filter(e=>e!==t),r.initialHits=r.initialHits.filter(e=>e!==t),r.hovered.forEach((e,n)=>{(e.eventObject===t||e.object===t)&&r.hovered.delete(n)}),r.capturedMap.forEach((e,n)=>{Z(r.capturedMap,t,e,n)})}(M(t),t.object));let i=null!==t.props.dispose&&!1!==r;for(let e=t.children.length-1;e>=0;e--){let r=t.children[e];em(t,r,i)}t.children.length=0,delete t.object.__r3f,i&&"primitive"!==t.type&&"Scene"!==t.object.type&&eA(t.object),void 0===r&&X(t)}let ev=[],eg=()=>{},eB={},eC=0,eb=function(e){let t=C()(e);return t.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:l.version}),t}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(e,t,r){var n;return eu(e=es(e)in ea?e:e.replace(eo,""),t),"primitive"===e&&null!=(n=t.object)&&n.__r3f&&delete t.object.__r3f,H(t.object,r,e,t)},removeChild:em,appendChild:eh,appendInitialChild:eh,insertBefore:ep,appendChildToContainer(e,t){let r=e.getState().scene.__r3f;t&&r&&eh(r,t)},removeChildFromContainer(e,t){let r=e.getState().scene.__r3f;t&&r&&em(r,t)},insertInContainerBefore(e,t,r){let n=e.getState().scene.__r3f;t&&r&&n&&ep(n,t,r)},getRootHostContext:()=>eB,getChildHostContext:()=>eB,commitUpdate(e,t,r,n,i){var a,o,s;eu(t,n);let l=!1;if("primitive"===e.type&&r.object!==n.object||(null==(a=n.args)?void 0:a.length)!==(null==(o=r.args)?void 0:o.length)?l=!0:null!=(s=n.args)&&s.some((e,t)=>{var n;return e!==(null==(n=r.args)?void 0:n[t])})&&(l=!0),l)ev.push([e,{...n},i]);else{let t=function(e,t){let r={};for(let n in t)if(!J.includes(n)&&!P.equ(t[n],e.props[n]))for(let e in r[n]=t[n],t)e.startsWith(`${n}-`)&&(r[e]=t[e]);for(let n in e.props){if(J.includes(n)||t.hasOwnProperty(n))continue;let{root:i,key:a}=O(e.object,n);if(i.constructor&&0===i.constructor.length){let e=function(e){let t=j.get(e.constructor);try{t||(t=new e.constructor,j.set(e.constructor,t))}catch(e){}return t}(i);P.und(e)||(r[a]=e[a])}else r[a]=0}return r}(e,n);Object.keys(t).length&&(Object.assign(e.props,t),Q(e.object,t))}(null===i.sibling||(4&i.flags)==0)&&function(){for(let[e]of ev){let t=e.parent;if(t)for(let r of(e.props.attach?k(t,e):W(e.object)&&W(t.object)&&t.object.remove(e.object),e.children))r.props.attach?k(e,r):W(r.object)&&W(e.object)&&e.object.remove(r.object);e.isHidden&&ed(e),e.object.__r3f&&delete e.object.__r3f,"primitive"!==e.type&&eA(e.object)}for(let[n,i,a]of ev){n.props=i;let o=n.parent;if(o){let i=ea[es(n.type)];n.object=null!=(e=n.props.object)?e:new i(...null!=(t=n.props.args)?t:[]),n.object.__r3f=n;var e,t,r=n.object;for(let e of[a,a.alternate])if(null!==e)if("function"==typeof e.ref){null==e.refCleanup||e.refCleanup();let t=e.ref(r);"function"==typeof t&&(e.refCleanup=t)}else e.ref&&(e.ref.current=r);for(let e of(Q(n.object,n.props),n.props.attach?U(o,n):W(n.object)&&W(o.object)&&o.object.add(n.object),n.children))e.props.attach?U(n,e):W(e.object)&&W(n.object)&&n.object.add(e.object);X(n)}}ev.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:e=>null==e?void 0:e.object,prepareForCommit:()=>null,preparePortalMount:e=>H(e.getState().scene,e,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(e){if(!e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?k(e.parent,e):W(e.object)&&(e.object.visible=!1),e.isHidden=!0,X(e)}},unhideInstance:ed,createTextInstance:eg,hideTextInstance:eg,unhideTextInstance:eg,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:l.createContext(null),setCurrentUpdatePriority(e){eC=e},getCurrentUpdatePriority:()=>eC,resolveUpdatePriority(){var e;if(0!==eC)return eC;switch("undefined"!=typeof window&&(null==(e=window.event)?void 0:e.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return u.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return u.ContinuousEventPriority;default:return u.DefaultEventPriority}},resetFormInstance(){}}),ey=new Map,eE={objects:"shallow",strict:!1};function eM(e){let t,r,n=ey.get(e),i=null==n?void 0:n.fiber,a=null==n?void 0:n.store;n&&console.warn("R3F.createRoot should only be called once!");let o="function"==typeof reportError?reportError:console.error,s=a||((e,t)=>{let r=v((r,n)=>{let i,a=new d.Pq0,o=new d.Pq0,s=new d.Pq0;function c(e=n().camera,t=o,r=n().size){let{width:i,height:l,top:u,left:d}=r,f=i/l;t.isVector3?s.copy(t):s.set(...t);let h=e.getWorldPosition(a).distanceTo(s);if(e&&e.isOrthographicCamera)return{width:i/e.zoom,height:l/e.zoom,top:u,left:d,factor:1,distance:h,aspect:f};{let t=2*Math.tan(e.fov*Math.PI/180/2)*h,r=i/l*t;return{width:r,height:t,top:u,left:d,factor:i/r,distance:h,aspect:f}}}let u=e=>r(t=>({performance:{...t.performance,current:e}})),f=new d.I9Y;return{set:r,get:n,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},scene:null,xr:null,invalidate:(t=1)=>e(n(),t),advance:(e,r)=>t(e,r,n()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new d.zD7,pointer:f,mouse:f,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=n();i&&clearTimeout(i),e.performance.current!==e.performance.min&&u(e.performance.min),i=setTimeout(()=>u(n().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:c},setEvents:e=>r(t=>({...t,events:{...t.events,...e}})),setSize:(e,t,i=0,a=0)=>{let s=n().camera,l={width:e,height:t,top:i,left:a};r(e=>({size:l,viewport:{...e.viewport,...c(s,o,l)}}))},setDpr:e=>r(t=>{let r=G(e);return{viewport:{...t.viewport,dpr:r,initialDpr:t.viewport.initialDpr||r}}}),setFrameloop:(e="always")=>{let t=n().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),r(()=>({frameloop:e}))},previousRoot:void 0,internal:{interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,lastEvent:l.createRef(),active:!1,frames:0,priority:0,subscribe:(e,t,r)=>{let i=n().internal;return i.priority=i.priority+ +(t>0),i.subscribers.push({ref:e,priority:t,store:r}),i.subscribers=i.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let r=n().internal;null!=r&&r.subscribers&&(r.priority=r.priority-(t>0),r.subscribers=r.subscribers.filter(t=>t.ref!==e))}}}}}),n=r.getState(),i=n.size,a=n.viewport.dpr,o=n.camera;return r.subscribe(()=>{let{camera:e,size:t,viewport:n,gl:s,set:l}=r.getState();if(t.width!==i.width||t.height!==i.height||n.dpr!==a){i=t,a=n.dpr,Y(e,t),n.dpr>0&&s.setPixelRatio(n.dpr);let r="undefined"!=typeof HTMLCanvasElement&&s.domElement instanceof HTMLCanvasElement;s.setSize(t.width,t.height,r)}e!==o&&(o=e,l(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),r.subscribe(t=>e(t)),r})(eL,eU),c=i||eb.createContainer(s,u.ConcurrentRoot,null,!1,null,"",o,o,o,null);n||ey.set(e,{fiber:c,store:s});let h=!1,p=null;return{async configure(n={}){var i,a;let o;p=new Promise(e=>o=e);let{gl:l,size:c,scene:u,events:A,onCreated:m,shadows:v=!1,linear:g=!1,flat:B=!1,legacy:C=!1,orthographic:b=!1,frameloop:y="always",dpr:E=[1,2],performance:M,raycaster:w,camera:x,onPointerMissed:I}=n,R=s.getState(),F=R.gl;if(!R.gl){let t={canvas:e,powerPreference:"high-performance",antialias:!0,alpha:!0},r="function"==typeof l?await l(t):l;F=q(r)?r:new f.WebGLRenderer({...t,...l}),R.set({gl:F})}let D=R.raycaster;D||R.set({raycaster:D=new d.tBo});let{params:T,...S}=w||{};if(P.equ(S,D,eE)||Q(D,{...S}),P.equ(T,D.params,eE)||Q(D,{params:{...D.params,...T}}),!R.camera||R.camera===r&&!P.equ(r,x,eE)){r=x;let e=null==x?void 0:x.isCamera,t=e?x:b?new d.qUd(0,0,0,0,.1,1e3):new d.ubm(75,0,.1,1e3);!e&&(t.position.z=5,x&&(Q(t,x),!t.manual&&("aspect"in x||"left"in x||"right"in x||"bottom"in x||"top"in x)&&(t.manual=!0,t.updateProjectionMatrix())),R.camera||null!=x&&x.rotation||t.lookAt(0,0,0)),R.set({camera:t}),D.camera=t}if(!R.scene){let e;null!=u&&u.isScene?H(e=u,s,"",{}):(H(e=new d.Z58,s,"",{}),u&&Q(e,u)),R.set({scene:e})}A&&!R.events.handlers&&R.set({events:A(s)});let _=function(e,t){if(!t&&"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:r,top:n,left:i}=e.parentElement.getBoundingClientRect();return{width:t,height:r,top:n,left:i}}return!t&&"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0}:{width:0,height:0,top:0,left:0,...t}}(e,c);if(P.equ(_,R.size,eE)||R.setSize(_.width,_.height,_.top,_.left),E&&R.viewport.dpr!==G(E)&&R.setDpr(E),R.frameloop!==y&&R.setFrameloop(y),R.onPointerMissed||R.set({onPointerMissed:I}),M&&!P.equ(M,R.performance,eE)&&R.set(e=>({performance:{...e.performance,...M}})),!R.xr){let e=(e,t)=>{let r=s.getState();"never"!==r.frameloop&&eU(e,!0,r,t)},t=()=>{let t=s.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||eL(t)},r={connect(){let e=s.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=s.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};"function"==typeof(null==(i=F.xr)?void 0:i.addEventListener)&&r.connect(),R.set({xr:r})}if(F.shadowMap){let e=F.shadowMap.enabled,t=F.shadowMap.type;if(F.shadowMap.enabled=!!v,P.boo(v))F.shadowMap.type=d.Wk7;else if(P.str(v)){let e={basic:d.bTm,percentage:d.QP0,soft:d.Wk7,variance:d.RyA};F.shadowMap.type=null!=(a=e[v])?a:d.Wk7}else P.obj(v)&&Object.assign(F.shadowMap,v);(e!==F.shadowMap.enabled||t!==F.shadowMap.type)&&(F.shadowMap.needsUpdate=!0)}return d.ppV.enabled=!C,h||(F.outputColorSpace=g?d.Zr2:d.er$,F.toneMapping=B?d.y_p:d.FV),R.legacy!==C&&R.set(()=>({legacy:C})),R.linear!==g&&R.set(()=>({linear:g})),R.flat!==B&&R.set(()=>({flat:B})),!l||P.fun(l)||q(l)||P.equ(l,F,eE)||Q(F,l),t=m,h=!0,o(),this},render(r){return h||p||this.configure(),p.then(()=>{eb.updateContainer((0,y.jsx)(ew,{store:s,children:r,onCreated:t,rootElement:e}),c,null,()=>void 0)}),s},unmount(){ex(e)}}}function ew({store:e,children:t,onCreated:r,rootElement:n}){return I(()=>{let t=e.getState();t.set(e=>({internal:{...e.internal,active:!0}})),r&&r(t),e.getState().events.connected||null==t.events.connect||t.events.connect(n)},[]),(0,y.jsx)(V.Provider,{value:e,children:t})}function ex(e,t){let r=ey.get(e),n=null==r?void 0:r.fiber;if(n){let i=null==r?void 0:r.store.getState();i&&(i.internal.active=!1),eb.updateContainer(null,n,null,()=>{i&&setTimeout(()=>{try{null==i.events.disconnect||i.events.disconnect(),null==(r=i.gl)||null==(n=r.renderLists)||null==n.dispose||n.dispose(),null==(a=i.gl)||null==a.forceContextLoss||a.forceContextLoss(),null!=(o=i.gl)&&o.xr&&i.xr.disconnect();var r,n,a,o,s=i.scene;for(let e in"Scene"!==s.type&&(null==s.dispose||s.dispose()),s){let t=s[e];(null==t?void 0:t.type)!=="Scene"&&(null==t||null==t.dispose||t.dispose())}ey.delete(e),t&&t(e)}catch(e){}},500)})}}function eI(e,t,r){return(0,y.jsx)(eR,{children:e,container:t,state:r})}function eR({state:e={},children:t,container:r}){let{events:n,size:i,...a}=e,o=$(),[s]=l.useState(()=>new d.tBo),[c]=l.useState(()=>new d.I9Y),u=R((e,t)=>{let a;if(t.camera&&i){let r=t.camera;a=e.viewport.getCurrentViewport(r,new d.Pq0,i),r!==e.camera&&Y(r,i)}return{...e,...t,scene:r,raycaster:s,pointer:c,mouse:c,previousRoot:o,events:{...e.events,...t.events,...n},size:{...e.size,...i},viewport:{...e.viewport,...a},setEvents:e=>t.set(t=>({...t,events:{...t.events,...e}}))}}),f=l.useMemo(()=>{let e=v((e,t)=>({...a,set:e,get:t})),t=t=>e.setState(e=>u.current(t,e));return t(o.getState()),o.subscribe(t),e},[o,r]);return(0,y.jsx)(y.Fragment,{children:eb.createPortal((0,y.jsx)(V.Provider,{value:f,children:t}),f,null)})}let eF=new Set,eD=new Set,eT=new Set;function eG(e,t){if(e.size)for(let{callback:r}of e.values())r(t)}function eS(e,t){switch(e){case"before":return eG(eF,t);case"after":return eG(eD,t);case"tail":return eG(eT,t)}}function eP(e,t,r){let a=t.clock.getDelta();"never"===t.frameloop&&"number"==typeof e&&(a=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),n=t.internal.subscribers;for(let e=0;e<n.length;e++)(i=n[e]).ref.current(i.store.getState(),a,r);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}let e_=!1,eH=!1;function eO(e){for(let r of(o=requestAnimationFrame(eO),e_=!0,a=0,eS("before",e),eH=!0,ey.values())){var t;(s=r.store.getState()).internal.active&&("always"===s.frameloop||s.internal.frames>0)&&!(null!=(t=s.gl.xr)&&t.isPresenting)&&(a+=eP(e,s))}if(eH=!1,eS("after",e),0===a)return eS("tail",e),e_=!1,cancelAnimationFrame(o)}function eL(e,t=1){var r;if(!e)return ey.forEach(e=>eL(e.store.getState(),t));(null==(r=e.gl.xr)||!r.isPresenting)&&e.internal.active&&"never"!==e.frameloop&&(t>1?e.internal.frames=Math.min(60,e.internal.frames+t):eH?e.internal.frames=2:e.internal.frames=1,e_||(e_=!0,requestAnimationFrame(eO)))}function eU(e,t=!0,r,n){if(t&&eS("before",e),r)eP(e,r,n);else for(let t of ey.values())eP(e,t.store.getState());t&&eS("after",e)}let ek={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function eJ(e){let{handlePointer:t}=function(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var r;return null==(r=e.__r3f)?void 0:r.handlers["onPointer"+t]}))}function r(t){let{internal:r}=e.getState();for(let e of r.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let n=e.eventObject.__r3f;if(r.hovered.delete(z(e)),null!=n&&n.eventCount){let r=n.handlers,i={...e,intersections:t};null==r.onPointerOut||r.onPointerOut(i),null==r.onPointerLeave||r.onPointerLeave(i)}}}function n(e,t){for(let r=0;r<t.length;r++){let n=t[r].__r3f;null==n||null==n.handlers.onPointerMissed||n.handlers.onPointerMissed(e)}}return{handlePointer:function(i){switch(i){case"onPointerLeave":case"onPointerCancel":return()=>r([]);case"onLostPointerCapture":return t=>{let{internal:n}=e.getState();"pointerId"in t&&n.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{n.capturedMap.has(t.pointerId)&&(n.capturedMap.delete(t.pointerId),r([]))})}}return function(a){let{onPointerMissed:o,internal:s}=e.getState();s.lastEvent.current=a;let l="onPointerMove"===i,c="onClick"===i||"onContextMenu"===i||"onDoubleClick"===i,u=function(t,r){let n=e.getState(),i=new Set,a=[],o=r?r(n.internal.interaction):n.internal.interaction;for(let e=0;e<o.length;e++){let t=S(o[e]);t&&(t.raycaster.camera=void 0)}n.previousRoot||null==n.events.compute||n.events.compute(t,n);let s=o.flatMap(function(e){let r=S(e);if(!r||!r.events.enabled||null===r.raycaster.camera)return[];if(void 0===r.raycaster.camera){var n;null==r.events.compute||r.events.compute(t,r,null==(n=r.previousRoot)?void 0:n.getState()),void 0===r.raycaster.camera&&(r.raycaster.camera=null)}return r.raycaster.camera?r.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let r=S(e.object),n=S(t.object);return r&&n&&n.events.priority-r.events.priority||e.distance-t.distance}).filter(e=>{let t=z(e);return!i.has(t)&&(i.add(t),!0)});for(let e of(n.events.filter&&(s=n.events.filter(s,n)),s)){let t=e.object;for(;t;){var l;null!=(l=t.__r3f)&&l.eventCount&&a.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&n.internal.capturedMap.has(t.pointerId))for(let e of n.internal.capturedMap.get(t.pointerId).values())i.has(z(e.intersection))||a.push(e.intersection);return a}(a,l?t:void 0),f=c?function(t){let{internal:r}=e.getState(),n=t.offsetX-r.initialClick[0],i=t.offsetY-r.initialClick[1];return Math.round(Math.sqrt(n*n+i*i))}(a):0;"onPointerDown"===i&&(s.initialClick=[a.offsetX,a.offsetY],s.initialHits=u.map(e=>e.eventObject)),c&&!u.length&&f<=2&&(n(a,s.interaction),o&&o(a)),l&&r(u),!function(e,t,n,i){if(e.length){let a={stopped:!1};for(let o of e){let s=S(o.object);if(s||o.object.traverseAncestors(e=>{let t=S(e);if(t)return s=t,!1}),s){let{raycaster:l,pointer:c,camera:u,internal:f}=s,h=new d.Pq0(c.x,c.y,0).unproject(u),p=e=>{var t,r;return null!=(t=null==(r=f.capturedMap.get(e))?void 0:r.has(o.eventObject))&&t},A=e=>{let r={intersection:o,target:t.target};f.capturedMap.has(e)?f.capturedMap.get(e).set(o.eventObject,r):f.capturedMap.set(e,new Map([[o.eventObject,r]])),t.target.setPointerCapture(e)},m=e=>{let t=f.capturedMap.get(e);t&&Z(f.capturedMap,o.eventObject,t,e)},v={};for(let e in t){let r=t[e];"function"!=typeof r&&(v[e]=r)}let g={...o,...v,pointer:c,intersections:e,stopped:a.stopped,delta:n,unprojectedPoint:h,ray:l.ray,camera:u,stopPropagation(){let n="pointerId"in t&&f.capturedMap.get(t.pointerId);(!n||n.has(o.eventObject))&&(g.stopped=a.stopped=!0,f.hovered.size&&Array.from(f.hovered.values()).find(e=>e.eventObject===o.eventObject)&&r([...e.slice(0,e.indexOf(o)),o]))},target:{hasPointerCapture:p,setPointerCapture:A,releasePointerCapture:m},currentTarget:{hasPointerCapture:p,setPointerCapture:A,releasePointerCapture:m},nativeEvent:t};if(i(g),!0===a.stopped)break}}}}(u,a,f,function(e){let t=e.eventObject,r=t.__r3f;if(!(null!=r&&r.eventCount))return;let o=r.handlers;if(l){if(o.onPointerOver||o.onPointerEnter||o.onPointerOut||o.onPointerLeave){let t=z(e),r=s.hovered.get(t);r?r.stopped&&e.stopPropagation():(s.hovered.set(t,e),null==o.onPointerOver||o.onPointerOver(e),null==o.onPointerEnter||o.onPointerEnter(e))}null==o.onPointerMove||o.onPointerMove(e)}else{let r=o[i];r?(!c||s.initialHits.includes(t))&&(n(a,s.interaction.filter(e=>!s.initialHits.includes(e))),r(e)):c&&s.initialHits.includes(t)&&n(a,s.interaction.filter(e=>!s.initialHits.includes(e)))}})}}}}(e);return{priority:1,enabled:!0,compute(e,t,r){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(ek).reduce((e,r)=>({...e,[r]:t(r)}),{}),update:()=>{var t;let{events:r,internal:n}=e.getState();null!=(t=n.lastEvent)&&t.current&&r.handlers&&r.handlers.onPointerMove(n.lastEvent.current)},connect:t=>{let{set:r,events:n}=e.getState();if(null==n.disconnect||n.disconnect(),r(e=>({events:{...e.events,connected:t}})),n.handlers)for(let e in n.handlers){let r=n.handlers[e],[i,a]=ek[e];t.addEventListener(i,r,{passive:a})}},disconnect:()=>{let{set:t,events:r}=e.getState();if(r.connected){if(r.handlers)for(let e in r.handlers){let t=r.handlers[e],[n]=ek[e];r.connected.removeEventListener(n,t)}t(e=>({events:{...e.events,connected:void 0}}))}}}}},620:(e,t)=>{function r(e,t){var r=e.length;for(e.push(t);0<r;){var n=r-1>>>1,i=e[n];if(0<a(i,t))e[n]=t,e[r]=i,r=n;else break}}function n(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;for(var n=0,i=e.length,o=i>>>1;n<o;){var s=2*(n+1)-1,l=e[s],c=s+1,u=e[c];if(0>a(l,r))c<i&&0>a(u,l)?(e[n]=u,e[c]=r,n=c):(e[n]=l,e[s]=r,n=s);else if(c<i&&0>a(u,r))e[n]=u,e[c]=r,n=c;else break}}return t}function a(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var o,s=performance;t.unstable_now=function(){return s.now()}}else{var l=Date,c=l.now();t.unstable_now=function(){return l.now()-c}}var u=[],d=[],f=1,h=null,p=3,A=!1,m=!1,v=!1,g="function"==typeof setTimeout?setTimeout:null,B="function"==typeof clearTimeout?clearTimeout:null,C="undefined"!=typeof setImmediate?setImmediate:null;function b(e){for(var t=n(d);null!==t;){if(null===t.callback)i(d);else if(t.startTime<=e)i(d),t.sortIndex=t.expirationTime,r(u,t);else break;t=n(d)}}function y(e){if(v=!1,b(e),!m)if(null!==n(u))m=!0,T();else{var t=n(d);null!==t&&G(y,t.startTime-e)}}var E=!1,M=-1,w=5,x=-1;function I(){return!(t.unstable_now()-x<w)}function R(){if(E){var e=t.unstable_now();x=e;var r=!0;try{e:{m=!1,v&&(v=!1,B(M),M=-1),A=!0;var a=p;try{t:{for(b(e),h=n(u);null!==h&&!(h.expirationTime>e&&I());){var s=h.callback;if("function"==typeof s){h.callback=null,p=h.priorityLevel;var l=s(h.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof l){h.callback=l,b(e),r=!0;break t}h===n(u)&&i(u),b(e)}else i(u);h=n(u)}if(null!==h)r=!0;else{var c=n(d);null!==c&&G(y,c.startTime-e),r=!1}}break e}finally{h=null,p=a,A=!1}}}finally{r?o():E=!1}}}if("function"==typeof C)o=function(){C(R)};else if("undefined"!=typeof MessageChannel){var F=new MessageChannel,D=F.port2;F.port1.onmessage=R,o=function(){D.postMessage(null)}}else o=function(){g(R,0)};function T(){E||(E=!0,o())}function G(e,r){M=g(function(){e(t.unstable_now())},r)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||A||(m=!0,T())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):w=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return n(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var r=p;p=t;try{return e()}finally{p=r}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=p;p=e;try{return t()}finally{p=r}},t.unstable_scheduleCallback=function(e,i,a){var o=t.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?o+a:o,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=0x3fffffff;break;case 4:s=1e4;break;default:s=5e3}return s=a+s,e={id:f++,callback:i,priorityLevel:e,startTime:a,expirationTime:s,sortIndex:-1},a>o?(e.sortIndex=a,r(d,e),null===n(u)&&e===n(d)&&(v?(B(M),M=-1):v=!0,G(y,a-o))):(e.sortIndex=s,r(u,e),m||A||(m=!0,T())),e},t.unstable_shouldYield=I,t.unstable_wrapCallback=function(e){var t=p;return function(){var r=p;p=t;try{return e.apply(this,arguments)}finally{p=r}}}},968:(e,t,r)=>{r.d(t,{b:()=>s});var n=r(2115),i=r(3655),a=r(5155),o=n.forwardRef((e,t)=>(0,a.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var s=o},1007:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1414:(e,t,r)=>{e.exports=r(2436)},1933:(e,t,r)=>{e.exports=r(6500)},2436:(e,t,r)=>{var n=r(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,s=n.useLayoutEffect,l=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,u=n[1];return s(function(){i.value=r,i.getSnapshot=t,c(i)&&u({inst:i})},[e,r,t]),o(function(){return c(i)&&u({inst:i}),e(function(){c(i)&&u({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},2473:(e,t,r)=>{r.d(t,{UC:()=>q,B8:()=>z,bL:()=>W,l9:()=>Z});var n=r(2115),i=r(5185),a=r(6081),o=r(7328),s=r(6101),l=r(1285),c=r(3655),u=r(9033),d=r(5845),f=r(4315),h=r(5155),p="rovingFocusGroup.onEntryFocus",A={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[v,g,B]=(0,o.N)(m),[C,b]=(0,a.A)(m,[B]),[y,E]=C(m),M=n.forwardRef((e,t)=>(0,h.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(w,{...e,ref:t})})}));M.displayName=m;var w=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:o=!1,dir:l,currentTabStopId:v,defaultCurrentTabStopId:B,onCurrentTabStopIdChange:C,onEntryFocus:b,preventScrollOnEntryFocus:E=!1,...M}=e,w=n.useRef(null),x=(0,s.s)(t,w),I=(0,f.jH)(l),[R,D]=(0,d.i)({prop:v,defaultProp:null!=B?B:null,onChange:C,caller:m}),[T,G]=n.useState(!1),S=(0,u.c)(b),P=g(r),_=n.useRef(!1),[H,O]=n.useState(0);return n.useEffect(()=>{let e=w.current;if(e)return e.addEventListener(p,S),()=>e.removeEventListener(p,S)},[S]),(0,h.jsx)(y,{scope:r,orientation:a,dir:I,loop:o,currentTabStopId:R,onItemFocus:n.useCallback(e=>D(e),[D]),onItemShiftTab:n.useCallback(()=>G(!0),[]),onFocusableItemAdd:n.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>O(e=>e-1),[]),children:(0,h.jsx)(c.sG.div,{tabIndex:T||0===H?-1:0,"data-orientation":a,...M,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,i.mK)(e.onMouseDown,()=>{_.current=!0}),onFocus:(0,i.mK)(e.onFocus,e=>{let t=!_.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(p,A);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),E)}}_.current=!1}),onBlur:(0,i.mK)(e.onBlur,()=>G(!1))})})}),x="RovingFocusGroupItem",I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:o=!1,tabStopId:s,children:u,...d}=e,f=(0,l.B)(),p=s||f,A=E(x,r),m=A.currentTabStopId===p,B=g(r),{onFocusableItemAdd:C,onFocusableItemRemove:b,currentTabStopId:y}=A;return n.useEffect(()=>{if(a)return C(),()=>b()},[a,C,b]),(0,h.jsx)(v.ItemSlot,{scope:r,id:p,focusable:a,active:o,children:(0,h.jsx)(c.sG.span,{tabIndex:m?0:-1,"data-orientation":A.orientation,...d,ref:t,onMouseDown:(0,i.mK)(e.onMouseDown,e=>{a?A.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.mK)(e.onFocus,()=>A.onItemFocus(p)),onKeyDown:(0,i.mK)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void A.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return R[i]}(e,A.orientation,A.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=B().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=A.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>F(r))}}),children:"function"==typeof u?u({isCurrentTabStop:m,hasTabStop:null!=y}):u})})});I.displayName=x;var R={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var D=r(2712),T=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[i,a]=n.useState(),o=n.useRef(null),s=n.useRef(e),l=n.useRef("none"),[c,u]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=G(o.current);l.current="mounted"===c?e:"none"},[c]),(0,D.N)(()=>{let t=o.current,r=s.current;if(r!==e){let n=l.current,i=G(t);e?u("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?u("UNMOUNT"):r&&n!==i?u("ANIMATION_OUT"):u("UNMOUNT"),s.current=e}},[e,u]),(0,D.N)(()=>{if(i){var e;let t,r=null!=(e=i.ownerDocument.defaultView)?e:window,n=e=>{let n=G(o.current).includes(CSS.escape(e.animationName));if(e.target===i&&n&&(u("ANIMATION_END"),!s.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},a=e=>{e.target===i&&(l.current=G(o.current))};return i.addEventListener("animationstart",a),i.addEventListener("animationcancel",n),i.addEventListener("animationend",n),()=>{r.clearTimeout(t),i.removeEventListener("animationstart",a),i.removeEventListener("animationcancel",n),i.removeEventListener("animationend",n)}}u("ANIMATION_END")},[i,u]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{o.current=e?getComputedStyle(e):null,a(e)},[])}}(t),a="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),o=(0,s.s)(i.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=n&&"isReactWarning"in n&&n.isReactWarning;return i?e.ref:(i=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||i.isPresent?n.cloneElement(a,{ref:o}):null};function G(e){return(null==e?void 0:e.animationName)||"none"}T.displayName="Presence";var S="Tabs",[P,_]=(0,a.A)(S,[b]),H=b(),[O,L]=P(S),U=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:i,defaultValue:a,orientation:o="horizontal",dir:s,activationMode:u="automatic",...p}=e,A=(0,f.jH)(s),[m,v]=(0,d.i)({prop:n,onChange:i,defaultProp:null!=a?a:"",caller:S});return(0,h.jsx)(O,{scope:r,baseId:(0,l.B)(),value:m,onValueChange:v,orientation:o,dir:A,activationMode:u,children:(0,h.jsx)(c.sG.div,{dir:A,"data-orientation":o,...p,ref:t})})});U.displayName=S;var k="TabsList",J=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...i}=e,a=L(k,r),o=H(r);return(0,h.jsx)(M,{asChild:!0,...o,orientation:a.orientation,dir:a.dir,loop:n,children:(0,h.jsx)(c.sG.div,{role:"tablist","aria-orientation":a.orientation,...i,ref:t})})});J.displayName=k;var j="TabsTrigger",N=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...o}=e,s=L(j,r),l=H(r),u=X(s.baseId,n),d=Y(s.baseId,n),f=n===s.value;return(0,h.jsx)(I,{asChild:!0,...l,focusable:!a,active:f,children:(0,h.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:u,...o,ref:t,onMouseDown:(0,i.mK)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(n)}),onKeyDown:(0,i.mK)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(n)}),onFocus:(0,i.mK)(e.onFocus,()=>{let e="manual"!==s.activationMode;f||a||!e||s.onValueChange(n)})})})});N.displayName=j;var K="TabsContent",Q=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:a,children:o,...s}=e,l=L(K,r),u=X(l.baseId,i),d=Y(l.baseId,i),f=i===l.value,p=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(T,{present:a||f,children:r=>{let{present:n}=r;return(0,h.jsx)(c.sG.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":u,hidden:!n,id:d,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&o})}})});function X(e,t){return"".concat(e,"-trigger-").concat(t)}function Y(e,t){return"".concat(e,"-content-").concat(t)}Q.displayName=K;var W=U,z=J,Z=N,q=Q},2515:(e,t,r)=>{let n;r.d(t,{OH:()=>eg});var i=r(9630),a=r(2115),o=r(461),s=r(3264),l=r(3625);class c extends s.eaF{constructor(e,t){var r,n;let i=(e=>e&&e.isCubeTexture)(e),a=Math.floor(Math.log2((null!=(n=i?null==(r=e.image[0])?void 0:r.width:e.image.width)?n:1024)/4)),o=Math.pow(2,a),c=3*Math.max(o,112),u=`
        varying vec3 vWorldPosition;
        void main() 
        {
            vec4 worldPosition = ( modelMatrix * vec4( position, 1.0 ) );
            vWorldPosition = worldPosition.xyz;
            
            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
        }
        `,d=[i?"#define ENVMAP_TYPE_CUBE":"",`#define CUBEUV_TEXEL_WIDTH ${1/c}`,`#define CUBEUV_TEXEL_HEIGHT ${1/(4*o)}`,`#define CUBEUV_MAX_MIP ${a}.0`].join("\n")+`
        #define ENVMAP_TYPE_CUBE_UV
        varying vec3 vWorldPosition;
        uniform float radius;
        uniform float height;
        uniform float angle;
        #ifdef ENVMAP_TYPE_CUBE
            uniform samplerCube map;
        #else
            uniform sampler2D map;
        #endif
        // From: https://www.shadertoy.com/view/4tsBD7
        float diskIntersectWithBackFaceCulling( vec3 ro, vec3 rd, vec3 c, vec3 n, float r ) 
        {
            float d = dot ( rd, n );
            
            if( d > 0.0 ) { return 1e6; }
            
            vec3  o = ro - c;
            float t = - dot( n, o ) / d;
            vec3  q = o + rd * t;
            
            return ( dot( q, q ) < r * r ) ? t : 1e6;
        }
        // From: https://www.iquilezles.org/www/articles/intersectors/intersectors.htm
        float sphereIntersect( vec3 ro, vec3 rd, vec3 ce, float ra ) 
        {
            vec3 oc = ro - ce;
            float b = dot( oc, rd );
            float c = dot( oc, oc ) - ra * ra;
            float h = b * b - c;
            
            if( h < 0.0 ) { return -1.0; }
            
            h = sqrt( h );
            
            return - b + h;
        }
        vec3 project() 
        {
            vec3 p = normalize( vWorldPosition );
            vec3 camPos = cameraPosition;
            camPos.y -= height;
            float intersection = sphereIntersect( camPos, p, vec3( 0.0 ), radius );
            if( intersection > 0.0 ) {
                
                vec3 h = vec3( 0.0, - height, 0.0 );
                float intersection2 = diskIntersectWithBackFaceCulling( camPos, p, h, vec3( 0.0, 1.0, 0.0 ), radius );
                p = ( camPos + min( intersection, intersection2 ) * p ) / radius;
            } else {
                p = vec3( 0.0, 1.0, 0.0 );
            }
            return p;
        }
        #include <common>
        #include <cube_uv_reflection_fragment>
        void main() 
        {
            vec3 projectedWorldPosition = project();
            
            #ifdef ENVMAP_TYPE_CUBE
                vec3 outcolor = textureCube( map, projectedWorldPosition ).rgb;
            #else
                vec3 direction = normalize( projectedWorldPosition );
                vec2 uv = equirectUv( direction );
                vec3 outcolor = texture2D( map, uv ).rgb;
            #endif
            gl_FragColor = vec4( outcolor, 1.0 );
            #include <tonemapping_fragment>
            #include <${l.r>=154?"colorspace_fragment":"encodings_fragment"}>
        }
        `,f={map:{value:e},height:{value:(null==t?void 0:t.height)||15},radius:{value:(null==t?void 0:t.radius)||100}};super(new s.WBB(1,16),new s.BKk({uniforms:f,fragmentShader:d,vertexShader:u,side:s.$EB}))}set radius(e){this.material.uniforms.radius.value=e}get radius(){return this.material.uniforms.radius.value}set height(e){this.material.uniforms.height.value=e}get height(){return this.material.uniforms.height.value}}class u extends s.BRH{constructor(e){super(e),this.type=s.ix0}parse(e){let t,r,n,i=function(e,t){switch(e){case 1:throw Error("THREE.RGBELoader: Read Error: "+(t||""));case 2:throw Error("THREE.RGBELoader: Write Error: "+(t||""));case 3:throw Error("THREE.RGBELoader: Bad File Format: "+(t||""));default:throw Error("THREE.RGBELoader: Memory Error: "+(t||""))}},a=function(e,t,r){t=t||1024;let n=e.pos,i=-1,a=0,o="",s=String.fromCharCode.apply(null,new Uint16Array(e.subarray(n,n+128)));for(;0>(i=s.indexOf("\n"))&&a<t&&n<e.byteLength;)o+=s,a+=s.length,n+=128,s+=String.fromCharCode.apply(null,new Uint16Array(e.subarray(n,n+128)));return -1<i&&(!1!==r&&(e.pos+=a+i+1),o+s.slice(0,i))},o=new Uint8Array(e);o.pos=0;let l=function(e){let t,r,n=/^\s*GAMMA\s*=\s*(\d+(\.\d+)?)\s*$/,o=/^\s*EXPOSURE\s*=\s*(\d+(\.\d+)?)\s*$/,s=/^\s*FORMAT=(\S+)\s*$/,l=/^\s*\-Y\s+(\d+)\s+\+X\s+(\d+)\s*$/,c={valid:0,string:"",comments:"",programtype:"RGBE",format:"",gamma:1,exposure:1,width:0,height:0};for(!(e.pos>=e.byteLength)&&(t=a(e))||i(1,"no header found"),(r=t.match(/^#\?(\S+)/))||i(3,"bad initial token"),c.valid|=1,c.programtype=r[1],c.string+=t+"\n";!1!==(t=a(e));){if(c.string+=t+"\n","#"===t.charAt(0)){c.comments+=t+"\n";continue}if((r=t.match(n))&&(c.gamma=parseFloat(r[1])),(r=t.match(o))&&(c.exposure=parseFloat(r[1])),(r=t.match(s))&&(c.valid|=2,c.format=r[1]),(r=t.match(l))&&(c.valid|=4,c.height=parseInt(r[1],10),c.width=parseInt(r[2],10)),2&c.valid&&4&c.valid)break}return 2&c.valid||i(3,"missing format specifier"),4&c.valid||i(3,"missing image size specifier"),c}(o),c=l.width,u=l.height,d=function(e,t,r){if(t<8||t>32767||2!==e[0]||2!==e[1]||128&e[2])return new Uint8Array(e);t!==(e[2]<<8|e[3])&&i(3,"wrong scanline width");let n=new Uint8Array(4*t*r);n.length||i(4,"unable to allocate buffer space");let a=0,o=0,s=4*t,l=new Uint8Array(4),c=new Uint8Array(s),u=r;for(;u>0&&o<e.byteLength;){o+4>e.byteLength&&i(1),l[0]=e[o++],l[1]=e[o++],l[2]=e[o++],l[3]=e[o++],(2!=l[0]||2!=l[1]||(l[2]<<8|l[3])!=t)&&i(3,"bad rgbe scanline format");let r=0,d;for(;r<s&&o<e.byteLength;){let t=(d=e[o++])>128;if(t&&(d-=128),(0===d||r+d>s)&&i(3,"bad scanline data"),t){let t=e[o++];for(let e=0;e<d;e++)c[r++]=t}else c.set(e.subarray(o,o+d),r),r+=d,o+=d}for(let e=0;e<t;e++){let r=0;n[a]=c[e+r],r+=t,n[a+1]=c[e+r],r+=t,n[a+2]=c[e+r],r+=t,n[a+3]=c[e+r],a+=4}u--}return n}(o.subarray(o.pos),c,u);switch(this.type){case s.RQf:let f=new Float32Array(4*(n=d.length/4));for(let e=0;e<n;e++)!function(e,t,r,n){let i=Math.pow(2,e[t+3]-128)/255;r[n+0]=e[t+0]*i,r[n+1]=e[t+1]*i,r[n+2]=e[t+2]*i,r[n+3]=1}(d,4*e,f,4*e);t=f,r=s.RQf;break;case s.ix0:let h=new Uint16Array(4*(n=d.length/4));for(let e=0;e<n;e++)!function(e,t,r,n){let i=Math.pow(2,e[t+3]-128)/255;r[n+0]=s.GxU.toHalfFloat(Math.min(e[t+0]*i,65504)),r[n+1]=s.GxU.toHalfFloat(Math.min(e[t+1]*i,65504)),r[n+2]=s.GxU.toHalfFloat(Math.min(e[t+2]*i,65504)),r[n+3]=s.GxU.toHalfFloat(1)}(d,4*e,h,4*e);t=h,r=s.ix0;break;default:throw Error("THREE.RGBELoader: Unsupported type: "+this.type)}return{width:c,height:u,data:t,header:l.string,gamma:l.gamma,exposure:l.exposure,type:r}}setDataType(e){return this.type=e,this}load(e,t,r,n){return super.load(e,function(e,r){switch(e.type){case s.RQf:case s.ix0:"colorSpace"in e?e.colorSpace="srgb-linear":e.encoding=3e3,e.minFilter=s.k6q,e.magFilter=s.k6q,e.generateMipmaps=!1,e.flipY=!0}t&&t(e,r)},r,n)}}var d=Uint8Array,f=Uint16Array,h=Uint32Array,p=new d([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),A=new d([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),m=new d([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),v=function(e,t){for(var r=new f(31),n=0;n<31;++n)r[n]=t+=1<<e[n-1];for(var i=new h(r[30]),n=1;n<30;++n)for(var a=r[n];a<r[n+1];++a)i[a]=a-r[n]<<5|n;return[r,i]},g=v(p,2),B=g[0],C=g[1];B[28]=258,C[258]=28;var b=v(A,0),y=b[0];b[1];for(var E=new f(32768),M=0;M<32768;++M){var w=(43690&M)>>>1|(21845&M)<<1;w=(61680&(w=(52428&w)>>>2|(13107&w)<<2))>>>4|(3855&w)<<4,E[M]=((65280&w)>>>8|(255&w)<<8)>>>1}for(var x=function(e,t,r){for(var n,i=e.length,a=0,o=new f(t);a<i;++a)++o[e[a]-1];var s=new f(t);for(a=0;a<t;++a)s[a]=s[a-1]+o[a-1]<<1;if(r){n=new f(1<<t);var l=15-t;for(a=0;a<i;++a)if(e[a])for(var c=a<<4|e[a],u=t-e[a],d=s[e[a]-1]++<<u,h=d|(1<<u)-1;d<=h;++d)n[E[d]>>>l]=c}else for(a=0,n=new f(i);a<i;++a)e[a]&&(n[a]=E[s[e[a]-1]++]>>>15-e[a]);return n},I=new d(288),M=0;M<144;++M)I[M]=8;for(var M=144;M<256;++M)I[M]=9;for(var M=256;M<280;++M)I[M]=7;for(var M=280;M<288;++M)I[M]=8;for(var R=new d(32),M=0;M<32;++M)R[M]=5;var F=x(I,9,1),D=x(R,5,1),T=function(e){for(var t=e[0],r=1;r<e.length;++r)e[r]>t&&(t=e[r]);return t},G=function(e,t,r){var n=t/8|0;return(e[n]|e[n+1]<<8)>>(7&t)&r},S=function(e,t){var r=t/8|0;return(e[r]|e[r+1]<<8|e[r+2]<<16)>>(7&t)},P=function(e,t,r){(null==t||t<0)&&(t=0),(null==r||r>e.length)&&(r=e.length);var n=new(e instanceof f?f:e instanceof h?h:d)(r-t);return n.set(e.subarray(t,r)),n},_=function(e,t,r){var n=e.length;if(!n||r&&!r.l&&n<5)return t||new d(0);var i=!t||r,a=!r||r.i;r||(r={}),t||(t=new d(3*n));var o=function(e){var r=t.length;if(e>r){var n=new d(Math.max(2*r,e));n.set(t),t=n}},s=r.f||0,l=r.p||0,c=r.b||0,u=r.l,f=r.d,h=r.m,v=r.n,g=8*n;do{if(!u){r.f=s=G(e,l,1);var C=G(e,l+1,3);if(l+=3,C)if(1==C)u=F,f=D,h=9,v=5;else if(2==C){var b=G(e,l,31)+257,E=G(e,l+10,15)+4,M=b+G(e,l+5,31)+1;l+=14;for(var w=new d(M),I=new d(19),R=0;R<E;++R)I[m[R]]=G(e,l+3*R,7);l+=3*E;for(var _=T(I),H=(1<<_)-1,O=x(I,_,1),R=0;R<M;){var L=O[G(e,l,H)];l+=15&L;var U=L>>>4;if(U<16)w[R++]=U;else{var k=0,J=0;for(16==U?(J=3+G(e,l,3),l+=2,k=w[R-1]):17==U?(J=3+G(e,l,7),l+=3):18==U&&(J=11+G(e,l,127),l+=7);J--;)w[R++]=k}}var j=w.subarray(0,b),N=w.subarray(b);h=T(j),v=T(N),u=x(j,h,1),f=x(N,v,1)}else throw"invalid block type";else{var K,U=((K=l)/8|0)+(7&K&&1)+4,Q=e[U-4]|e[U-3]<<8,X=U+Q;if(X>n){if(a)throw"unexpected EOF";break}i&&o(c+Q),t.set(e.subarray(U,X),c),r.b=c+=Q,r.p=l=8*X;continue}if(l>g){if(a)throw"unexpected EOF";break}}i&&o(c+131072);for(var Y=(1<<h)-1,W=(1<<v)-1,z=l;;z=l){var k=u[S(e,l)&Y],Z=k>>>4;if((l+=15&k)>g){if(a)throw"unexpected EOF";break}if(!k)throw"invalid length/literal";if(Z<256)t[c++]=Z;else if(256==Z){z=l,u=null;break}else{var q=Z-254;if(Z>264){var R=Z-257,V=p[R];q=G(e,l,(1<<V)-1)+B[R],l+=V}var $=f[S(e,l)&W],ee=$>>>4;if(!$)throw"invalid distance";l+=15&$;var N=y[ee];if(ee>3){var V=A[ee];N+=S(e,l)&(1<<V)-1,l+=V}if(l>g){if(a)throw"unexpected EOF";break}i&&o(c+131072);for(var et=c+q;c<et;c+=4)t[c]=t[c-N],t[c+1]=t[c+1-N],t[c+2]=t[c+2-N],t[c+3]=t[c+3-N];c=et}}r.l=u,r.p=z,r.b=c,u&&(s=1,r.m=h,r.d=f,r.n=v)}while(!s);return c==t.length?t:P(t,0,c)},H=new d(0),O=function(e){if((15&e[0])!=8||e[0]>>>4>7||(e[0]<<8|e[1])%31)throw"invalid zlib data";if(32&e[1])throw"invalid zlib data: preset dictionaries not supported"};function L(e,t){return _((O(e),e.subarray(2,-4)),t)}var U="undefined"!=typeof TextDecoder&&new TextDecoder;try{U.decode(H,{stream:!0})}catch(e){}let k=l.r>=152;class J extends s.BRH{constructor(e){super(e),this.type=s.ix0}parse(e){let t={l:0,c:0,lc:0};function r(e,r,n,i,a){for(;n<e;)r=r<<8|x(i,a),n+=8;t.l=r>>(n-=e)&(1<<e)-1,t.c=r,t.lc=n}let n=Array(59),i={c:0,lc:0};function a(e,t,r,n){e=e<<8|x(r,n),t+=8,i.c=e,i.lc=t}let o={c:0,lc:0};function l(e,t,r,n,s,l,c,u,d,f){if(e==t){n<8&&(a(r,n,s,c),r=i.c,n=i.lc);var h=r>>(n-=8),h=new Uint8Array([h])[0];if(d.value+h>f)return!1;for(var p=u[d.value-1];h-- >0;)u[d.value++]=p}else{if(!(d.value<f))return!1;u[d.value++]=e}o.c=r,o.lc=n}function c(e){var t=65535&e;return t>32767?t-65536:t}let u={a:0,b:0};function d(e,t){var r=c(e),n=c(t),i=r+(1&n)+(n>>1),a=i-n;u.a=i,u.b=a}function f(e,t){var r=65535&t,n=(65535&e)-(r>>1)&65535;u.a=r+n-32768&65535,u.b=n}function h(e,s,c,u,d,f){var h=c.value,p=w(s,c),A=w(s,c);c.value+=4;var m=w(s,c);if(c.value+=4,p<0||p>=65537||A<0||A>=65537)throw"Something wrong with HUF_ENCSIZE";for(var v=Array(65537),g=Array(16384),B=0;B<16384;B++)g[B]={},g[B].len=0,g[B].lit=0,g[B].p=null;var C=u-(c.value-h);if(!function(e,i,a,o,s,l,c){for(var u=0,d=0;s<=l;s++){if(a.value-a.value>o)return!1;r(6,u,d,e,a);var f=t.l;if(u=t.c,d=t.lc,c[s]=f,63==f){if(a.value-a.value>o)throw"Something wrong with hufUnpackEncTable";r(8,u,d,e,a);var h=t.l+6;if(u=t.c,d=t.lc,s+h>l+1)throw"Something wrong with hufUnpackEncTable";for(;h--;)c[s++]=0;s--}else if(f>=59){var h=f-59+2;if(s+h>l+1)throw"Something wrong with hufUnpackEncTable";for(;h--;)c[s++]=0;s--}}!function(e){for(var t=0;t<=58;++t)n[t]=0;for(var t=0;t<65537;++t)n[e[t]]+=1;for(var r=0,t=58;t>0;--t){var i=r+n[t]>>1;n[t]=r,r=i}for(var t=0;t<65537;++t){var a=e[t];a>0&&(e[t]=a|n[a]++<<6)}}(c)}(e,0,c,C,p,A,v),m>8*(u-(c.value-h)))throw"Something wrong with hufUncompress";!function(e,t,r,n){for(;t<=r;t++){var i=e[t]>>6,a=63&e[t];if(i>>a)throw"Invalid table entry";if(a>14){var o=n[i>>a-14];if(o.len)throw"Invalid table entry";if(o.lit++,o.p){var s=o.p;o.p=Array(o.lit);for(var l=0;l<o.lit-1;++l)o.p[l]=s[l]}else o.p=[,];o.p[o.lit-1]=t}else if(a)for(var c=0,l=1<<14-a;l>0;l--){var o=n[(i<<14-a)+c];if(o.len||o.p)throw"Invalid table entry";o.len=a,o.lit=t,c++}}}(v,p,A,g),function(e,t,r,n,s,c,u,d,f,h){for(var p=0,A=0,m=Math.trunc(s.value+(c+7)/8);s.value<m;)for(a(p,A,r,s),p=i.c,A=i.lc;A>=14;){var v=t[p>>A-14&16383];if(v.len)A-=v.len,l(v.lit,u,p,A,r,n,s,f,h,d),p=o.c,A=o.lc;else{if(!v.p)throw"hufDecode issues";for(g=0;g<v.lit;g++){for(var g,B=63&e[v.p[g]];A<B&&s.value<m;)a(p,A,r,s),p=i.c,A=i.lc;if(A>=B&&e[v.p[g]]>>6==(p>>A-B&(1<<B)-1)){A-=B,l(v.p[g],u,p,A,r,n,s,f,h,d),p=o.c,A=o.lc;break}}if(g==v.lit)throw"hufDecode issues"}}var C=8-c&7;for(p>>=C,A-=C;A>0;){var v=t[p<<14-A&16383];if(v.len)A-=v.len,l(v.lit,u,p,A,r,n,s,f,h,d),p=o.c,A=o.lc;else throw"hufDecode issues"}}(v,g,e,s,c,m,A,f,d,{value:0})}function p(e){for(var t=1;t<e.length;t++){var r=e[t-1]+e[t]-128;e[t]=r}}function A(e,t){for(var r=0,n=Math.floor((e.length+1)/2),i=0,a=e.length-1;!(i>a)&&(t[i++]=e[r++],!(i>a));){;t[i++]=e[n++]}}function m(e){for(var t=e.byteLength,r=[],n=0,i=new DataView(e);t>0;){var a=i.getInt8(n++);if(a<0){var o=-a;t-=o+1;for(var s=0;s<o;s++)r.push(i.getUint8(n++))}else{var o=a;t-=2;for(var l=i.getUint8(n++),s=0;s<o+1;s++)r.push(l)}}return r}function v(e){return new DataView(e.array.buffer,e.offset.value,e.size)}function g(e){var t=new Uint8Array(m(e.viewer.buffer.slice(e.offset.value,e.offset.value+e.size))),r=new Uint8Array(t.length);return p(t),A(t,r),new DataView(r.buffer)}function B(e){var t=L(e.array.slice(e.offset.value,e.offset.value+e.size)),r=new Uint8Array(t.length);return p(t),A(t,r),new DataView(r.buffer)}function C(e){for(var t=e.viewer,r={value:e.offset.value},n=new Uint16Array(e.width*e.scanlineBlockSize*(e.channels*e.type)),i=new Uint8Array(8192),a=0,o=Array(e.channels),s=0;s<e.channels;s++)o[s]={},o[s].start=a,o[s].end=o[s].start,o[s].nx=e.width,o[s].ny=e.lines,o[s].size=e.type,a+=o[s].nx*o[s].ny*o[s].size;var l=G(t,r),c=G(t,r);if(c>=8192)throw"Something is wrong with PIZ_COMPRESSION BITMAP_SIZE";if(l<=c)for(var s=0;s<c-l+1;s++)i[s+l]=I(t,r);var p=new Uint16Array(65536),A=function(e,t){for(var r=0,n=0;n<65536;++n)(0==n||e[n>>3]&1<<(7&n))&&(t[r++]=n);for(var i=r-1;r<65536;)t[r++]=0;return i}(i,p),m=w(t,r);h(e.array,t,r,m,n,a);for(var s=0;s<e.channels;++s)for(var v=o[s],g=0;g<o[s].size;++g)!function(e,t,r,n,i,a,o){for(var s=o<16384,l=r>i?i:r,c=1;c<=l;)c<<=1;for(c>>=1,h=c,c>>=1;c>=1;){for(var h,p,A,m,v,g=0,B=0+a*(i-h),C=a*c,b=a*h,y=n*c,E=n*h;g<=B;g+=b){for(var M=g,w=g+n*(r-h);M<=w;M+=E){var x=M+y,I=M+C,R=I+y;s?(d(e[M+t],e[I+t]),p=u.a,m=u.b,d(e[x+t],e[R+t]),A=u.a,v=u.b,d(p,A),e[M+t]=u.a,e[x+t]=u.b,d(m,v)):(f(e[M+t],e[I+t]),p=u.a,m=u.b,f(e[x+t],e[R+t]),A=u.a,v=u.b,f(p,A),e[M+t]=u.a,e[x+t]=u.b,f(m,v)),e[I+t]=u.a,e[R+t]=u.b}if(r&c){var I=M+C;s?d(e[M+t],e[I+t]):f(e[M+t],e[I+t]),p=u.a,e[I+t]=u.b,e[M+t]=p}}if(i&c)for(var M=g,w=g+n*(r-h);M<=w;M+=E){var x=M+y;s?d(e[M+t],e[x+t]):f(e[M+t],e[x+t]),p=u.a,e[x+t]=u.b,e[M+t]=p}h=c,c>>=1}}(n,v.start+g,v.nx,v.size,v.ny,v.nx*v.size,A);for(var B=a,C=0;C<B;++C)n[C]=p[n[C]];for(var b=0,y=new Uint8Array(n.buffer.byteLength),E=0;E<e.lines;E++)for(var M=0;M<e.channels;M++){var v=o[M],x=v.nx*v.size,R=new Uint8Array(n.buffer,2*v.end,2*x);y.set(R,b),b+=2*x,v.end+=x}return new DataView(y.buffer)}function b(e){var t=L(e.array.slice(e.offset.value,e.offset.value+e.size));let r=e.lines*e.channels*e.width,n=1==e.type?new Uint16Array(r):new Uint32Array(r),i=0,a=0,o=[,,,,];for(let r=0;r<e.lines;r++)for(let r=0;r<e.channels;r++){let r=0;switch(e.type){case 1:o[0]=i,o[1]=o[0]+e.width,i=o[1]+e.width;for(let i=0;i<e.width;++i)r+=t[o[0]++]<<8|t[o[1]++],n[a]=r,a++;break;case 2:o[0]=i,o[1]=o[0]+e.width,o[2]=o[1]+e.width,i=o[2]+e.width;for(let i=0;i<e.width;++i)r+=t[o[0]++]<<24|t[o[1]++]<<16|t[o[2]++]<<8,n[a]=r,a++}}return new DataView(n.buffer)}function y(e){var t=e.viewer,r={value:e.offset.value},n=new Uint8Array(e.width*e.lines*(e.channels*e.type*2)),i={version:R(t,r),unknownUncompressedSize:R(t,r),unknownCompressedSize:R(t,r),acCompressedSize:R(t,r),dcCompressedSize:R(t,r),rleCompressedSize:R(t,r),rleUncompressedSize:R(t,r),rleRawSize:R(t,r),totalAcUncompressedCount:R(t,r),totalDcUncompressedCount:R(t,r),acCompression:R(t,r)};if(i.version<2)throw"EXRLoader.parse: "+O.compression+" version "+i.version+" is unsupported";for(var a=[],o=G(t,r)-2;o>0;){var l=E(t.buffer,r),c=I(t,r),u=c>>2&3,d=new Int8Array([(c>>4)-1])[0],f=I(t,r);a.push({name:l,index:d,type:f,compression:u}),o-=l.length+3}for(var p=O.channels,A=Array(e.channels),v=0;v<e.channels;++v){var g=A[v]={},C=p[v];g.name=C.name,g.compression=0,g.decoded=!1,g.type=C.pixelType,g.pLinear=C.pLinear,g.width=e.width,g.height=e.lines}for(var b={idx:[,,,]},y=0;y<e.channels;++y)for(var g=A[y],v=0;v<a.length;++v){var M=a[v];g.name==M.name&&(g.compression=M.compression,M.index>=0&&(b.idx[M.index]=y),g.offset=y)}if(i.acCompressedSize>0)switch(i.acCompression){case 0:var w=new Uint16Array(i.totalAcUncompressedCount);h(e.array,t,r,i.acCompressedSize,w,i.totalAcUncompressedCount);break;case 1:var x=e.array.slice(r.value,r.value+i.totalAcUncompressedCount),F=L(x),w=new Uint16Array(F.buffer);r.value+=i.totalAcUncompressedCount}if(i.dcCompressedSize>0){var D=new Uint16Array(B({array:e.array,offset:r,size:i.dcCompressedSize}).buffer);r.value+=i.dcCompressedSize}if(i.rleRawSize>0){var x=e.array.slice(r.value,r.value+i.rleCompressedSize),F=L(x),S=m(F.buffer);r.value+=i.rleCompressedSize}for(var P=0,_=Array(A.length),v=0;v<_.length;++v)_[v]=[];for(var H=0;H<e.lines;++H)for(var U=0;U<A.length;++U)_[U].push(P),P+=A[U].width*e.type*2;!function(e,t,r,n,i,a){var o=new DataView(a.buffer),l=r[e.idx[0]].width,c=r[e.idx[0]].height,u=Math.floor(l/8),d=Math.ceil(l/8),f=Math.ceil(c/8),h=l-(d-1)*8,p=c-(f-1)*8,A={value:0},m=[,,,],v=[,,,],g=[,,,],B=[,,,],C=[,,,];for(let r=0;r<3;++r)C[r]=t[e.idx[r]],m[r]=r<1?0:m[r-1]+d*f,v[r]=new Float32Array(64),g[r]=new Uint16Array(64),B[r]=new Uint16Array(64*d);for(let t=0;t<f;++t){var b,y,E=8;t==f-1&&(E=p);var M=8;for(let e=0;e<d;++e){e==d-1&&(M=h);for(let e=0;e<3;++e){g[e].fill(0),g[e][0]=i[m[e]++],function(e,t,r){for(var n,i=1;i<64;)65280==(n=t[e.value])?i=64:n>>8==255?i+=255&n:(r[i]=n,i++),e.value++}(A,n,g[e]),b=g[e],(y=v[e])[0]=T(b[0]),y[1]=T(b[1]),y[2]=T(b[5]),y[3]=T(b[6]),y[4]=T(b[14]),y[5]=T(b[15]),y[6]=T(b[27]),y[7]=T(b[28]),y[8]=T(b[2]),y[9]=T(b[4]),y[10]=T(b[7]),y[11]=T(b[13]),y[12]=T(b[16]),y[13]=T(b[26]),y[14]=T(b[29]),y[15]=T(b[42]),y[16]=T(b[3]),y[17]=T(b[8]),y[18]=T(b[12]),y[19]=T(b[17]),y[20]=T(b[25]),y[21]=T(b[30]),y[22]=T(b[41]),y[23]=T(b[43]),y[24]=T(b[9]),y[25]=T(b[11]),y[26]=T(b[18]),y[27]=T(b[24]),y[28]=T(b[31]),y[29]=T(b[40]),y[30]=T(b[44]),y[31]=T(b[53]),y[32]=T(b[10]),y[33]=T(b[19]),y[34]=T(b[23]),y[35]=T(b[32]),y[36]=T(b[39]),y[37]=T(b[45]),y[38]=T(b[52]),y[39]=T(b[54]),y[40]=T(b[20]),y[41]=T(b[22]),y[42]=T(b[33]),y[43]=T(b[38]),y[44]=T(b[46]),y[45]=T(b[51]),y[46]=T(b[55]),y[47]=T(b[60]),y[48]=T(b[21]),y[49]=T(b[34]),y[50]=T(b[37]),y[51]=T(b[47]),y[52]=T(b[50]),y[53]=T(b[56]),y[54]=T(b[59]),y[55]=T(b[61]),y[56]=T(b[35]),y[57]=T(b[36]),y[58]=T(b[48]),y[59]=T(b[49]),y[60]=T(b[57]),y[61]=T(b[58]),y[62]=T(b[62]),y[63]=T(b[63]),function(e){let t=.5*Math.cos(3.14159/16),r=.5*Math.cos(3.14159/8),n=.5*Math.cos(3*3.14159/16),i=.5*Math.cos(3*3.14159/8);for(var a=[,,,,],o=[,,,,],s=[,,,,],l=[,,,,],c=0;c<8;++c){var u=8*c;a[0]=r*e[u+2],a[1]=i*e[u+2],a[2]=r*e[u+6],a[3]=i*e[u+6],o[0]=t*e[u+1]+n*e[u+3]+.2777854612564676*e[u+5]+.09754573032714427*e[u+7],o[1]=n*e[u+1]-.09754573032714427*e[u+3]-t*e[u+5]-.2777854612564676*e[u+7],o[2]=.2777854612564676*e[u+1]-t*e[u+3]+.09754573032714427*e[u+5]+n*e[u+7],o[3]=.09754573032714427*e[u+1]-.2777854612564676*e[u+3]+n*e[u+5]-t*e[u+7],s[0]=.35355362513961314*(e[u+0]+e[u+4]),s[3]=.35355362513961314*(e[u+0]-e[u+4]),s[1]=a[0]+a[3],s[2]=a[1]-a[2],l[0]=s[0]+s[1],l[1]=s[3]+s[2],l[2]=s[3]-s[2],l[3]=s[0]-s[1],e[u+0]=l[0]+o[0],e[u+1]=l[1]+o[1],e[u+2]=l[2]+o[2],e[u+3]=l[3]+o[3],e[u+4]=l[3]-o[3],e[u+5]=l[2]-o[2],e[u+6]=l[1]-o[1],e[u+7]=l[0]-o[0]}for(var d=0;d<8;++d)a[0]=r*e[16+d],a[1]=i*e[16+d],a[2]=r*e[48+d],a[3]=i*e[48+d],o[0]=t*e[8+d]+n*e[24+d]+.2777854612564676*e[40+d]+.09754573032714427*e[56+d],o[1]=n*e[8+d]-.09754573032714427*e[24+d]-t*e[40+d]-.2777854612564676*e[56+d],o[2]=.2777854612564676*e[8+d]-t*e[24+d]+.09754573032714427*e[40+d]+n*e[56+d],o[3]=.09754573032714427*e[8+d]-.2777854612564676*e[24+d]+n*e[40+d]-t*e[56+d],s[0]=.35355362513961314*(e[d]+e[32+d]),s[3]=.35355362513961314*(e[d]-e[32+d]),s[1]=a[0]+a[3],s[2]=a[1]-a[2],l[0]=s[0]+s[1],l[1]=s[3]+s[2],l[2]=s[3]-s[2],l[3]=s[0]-s[1],e[0+d]=l[0]+o[0],e[8+d]=l[1]+o[1],e[16+d]=l[2]+o[2],e[24+d]=l[3]+o[3],e[32+d]=l[3]-o[3],e[40+d]=l[2]-o[2],e[48+d]=l[1]-o[1],e[56+d]=l[0]-o[0]}(v[e])}for(var w=v,x=0;x<64;++x){var I=w[0][x],R=w[1][x],F=w[2][x];w[0][x]=I+1.5747*F,w[1][x]=I-.1873*R-.4682*F,w[2][x]=I+1.8556*R}for(let t=0;t<3;++t)!function(e,t,r){for(var n,i=0;i<64;++i){t[r+i]=s.GxU.toHalfFloat((n=e[i])<=1?Math.sign(n)*Math.pow(Math.abs(n),2.2):Math.sign(n)*Math.pow(9.025013291561939,Math.abs(n)-1))}}(v[t],B[t],64*e)}let a=0;for(let n=0;n<3;++n){let i=r[e.idx[n]].type;for(let e=8*t;e<8*t+E;++e){a=C[n][e];for(let t=0;t<u;++t){let r=64*t+(7&e)*8;o.setUint16(a+0*i,B[n][r+0],!0),o.setUint16(a+2*i,B[n][r+1],!0),o.setUint16(a+4*i,B[n][r+2],!0),o.setUint16(a+6*i,B[n][r+3],!0),o.setUint16(a+8*i,B[n][r+4],!0),o.setUint16(a+10*i,B[n][r+5],!0),o.setUint16(a+12*i,B[n][r+6],!0),o.setUint16(a+14*i,B[n][r+7],!0),a+=16*i}}if(u!=d)for(let e=8*t;e<8*t+E;++e){let t=C[n][e]+8*u*2*i,r=64*u+(7&e)*8;for(let e=0;e<M;++e)o.setUint16(t+2*e*i,B[n][r+e],!0)}}}for(var D=new Uint16Array(l),o=new DataView(a.buffer),G=0;G<3;++G){r[e.idx[G]].decoded=!0;var S=r[e.idx[G]].type;if(2==r[G].type)for(var P=0;P<c;++P){let e=C[G][P];for(var _=0;_<l;++_)D[_]=o.getUint16(e+2*_*S,!0);for(var _=0;_<l;++_)o.setFloat32(e+2*_*S,T(D[_]),!0)}}}(b,_,A,w,D,n);for(var v=0;v<A.length;++v){var g=A[v];if(!g.decoded)if(2===g.compression)for(var k=0,J=0,H=0;H<e.lines;++H){for(var j=_[v][k],N=0;N<g.width;++N){for(var K=0;K<2*g.type;++K)n[j++]=S[J+K*g.width*g.height];J++}k++}else throw"EXRLoader.parse: unsupported channel compression"}return new DataView(n.buffer)}function E(e,t){for(var r=new Uint8Array(e),n=0;0!=r[t.value+n];)n+=1;var i=new TextDecoder().decode(r.slice(t.value,t.value+n));return t.value=t.value+n+1,i}function M(e,t){var r=e.getInt32(t.value,!0);return t.value=t.value+4,r}function w(e,t){var r=e.getUint32(t.value,!0);return t.value=t.value+4,r}function x(e,t){var r=e[t.value];return t.value=t.value+1,r}function I(e,t){var r=e.getUint8(t.value);return t.value=t.value+1,r}let R=function(e,t){let r;return r="getBigInt64"in DataView.prototype?Number(e.getBigInt64(t.value,!0)):e.getUint32(t.value+4,!0)+Number(e.getUint32(t.value,!0)<<32),t.value+=8,r};function F(e,t){var r=e.getFloat32(t.value,!0);return t.value+=4,r}function D(e,t){return s.GxU.toHalfFloat(F(e,t))}function T(e){var t=(31744&e)>>10,r=1023&e;return(e>>15?-1:1)*(t?31===t?r?NaN:1/0:Math.pow(2,t-15)*(1+r/1024):r/1024*6103515625e-14)}function G(e,t){var r=e.getUint16(t.value,!0);return t.value+=2,r}function S(e,t){return T(G(e,t))}let P=new DataView(e),_=new Uint8Array(e),H={value:0},O=function(e,t,r){let n={};if(0x1312f76!=e.getUint32(0,!0))throw"THREE.EXRLoader: provided file doesn't appear to be in OpenEXR format.";n.version=e.getUint8(4);let i=e.getUint8(5);n.spec={singleTile:!!(2&i),longName:!!(4&i),deepFormat:!!(8&i),multiPart:!!(16&i)},r.value=8;for(var a=!0;a;){var o=E(t,r);if(0==o)a=!1;else{var s=E(t,r),l=w(e,r),c=function(e,t,r,n,i){var a,o,s,l,c,u,d;if("string"===n||"stringvector"===n||"iccProfile"===n)return a=new TextDecoder().decode(new Uint8Array(t).slice(r.value,r.value+i)),r.value=r.value+i,a;if("chlist"===n)return function(e,t,r,n){for(var i=r.value,a=[];r.value<i+n-1;){var o=E(t,r),s=M(e,r),l=I(e,r);r.value+=3;var c=M(e,r),u=M(e,r);a.push({name:o,pixelType:s,pLinear:l,xSampling:c,ySampling:u})}return r.value+=1,a}(e,t,r,i);if("chromaticities"===n)return o=F(e,r),s=F(e,r),l=F(e,r),c=F(e,r),u=F(e,r),{redX:o,redY:s,greenX:l,greenY:c,blueX:u,blueY:F(e,r),whiteX:F(e,r),whiteY:F(e,r)};if("compression"===n)return["NO_COMPRESSION","RLE_COMPRESSION","ZIPS_COMPRESSION","ZIP_COMPRESSION","PIZ_COMPRESSION","PXR24_COMPRESSION","B44_COMPRESSION","B44A_COMPRESSION","DWAA_COMPRESSION","DWAB_COMPRESSION"][I(e,r)];if("box2i"===n)return d=w(e,r),{xMin:d,yMin:w(e,r),xMax:w(e,r),yMax:w(e,r)};else if("lineOrder"===n)return["INCREASING_Y"][I(e,r)];else if("float"===n)return F(e,r);else if("v2f"===n)return[F(e,r),F(e,r)];else if("v3f"===n)return[F(e,r),F(e,r),F(e,r)];else if("int"===n)return M(e,r);else if("rational"===n)return[M(e,r),w(e,r)];else if("timecode"===n)return[w(e,r),w(e,r)];else return"preview"===n?(r.value+=i,"skipped"):(r.value+=i,void 0)}(e,t,r,s,l);void 0===c?console.warn(`EXRLoader.parse: skipped unknown header attribute type '${s}'.`):n[o]=c}}if((-5&i)!=0)throw console.error("EXRHeader:",n),"THREE.EXRLoader: provided file is currently unsupported.";return n}(P,e,H),U=function(e,t,r,n,i){let a={size:0,viewer:t,array:r,offset:n,width:e.dataWindow.xMax-e.dataWindow.xMin+1,height:e.dataWindow.yMax-e.dataWindow.yMin+1,channels:e.channels.length,bytesPerLine:null,lines:null,inputSize:null,type:e.channels[0].pixelType,uncompress:null,getter:null,format:null,[k?"colorSpace":"encoding"]:null};switch(e.compression){case"NO_COMPRESSION":a.lines=1,a.uncompress=v;break;case"RLE_COMPRESSION":a.lines=1,a.uncompress=g;break;case"ZIPS_COMPRESSION":a.lines=1,a.uncompress=B;break;case"ZIP_COMPRESSION":a.lines=16,a.uncompress=B;break;case"PIZ_COMPRESSION":a.lines=32,a.uncompress=C;break;case"PXR24_COMPRESSION":a.lines=16,a.uncompress=b;break;case"DWAA_COMPRESSION":a.lines=32,a.uncompress=y;break;case"DWAB_COMPRESSION":a.lines=256,a.uncompress=y;break;default:throw"EXRLoader.parse: "+e.compression+" is unsupported"}if(a.scanlineBlockSize=a.lines,1==a.type)switch(i){case s.RQf:a.getter=S,a.inputSize=2;break;case s.ix0:a.getter=G,a.inputSize=2}else if(2==a.type)switch(i){case s.RQf:a.getter=F,a.inputSize=4;break;case s.ix0:a.getter=D,a.inputSize=4}else throw"EXRLoader.parse: unsupported pixelType "+a.type+" for "+e.compression+".";a.blockCount=(e.dataWindow.yMax+1)/a.scanlineBlockSize;for(var o=0;o<a.blockCount;o++)R(t,n);a.outputChannels=3==a.channels?4:a.channels;let l=a.width*a.height*a.outputChannels;switch(i){case s.RQf:a.byteArray=new Float32Array(l),a.channels<a.outputChannels&&a.byteArray.fill(1,0,l);break;case s.ix0:a.byteArray=new Uint16Array(l),a.channels<a.outputChannels&&a.byteArray.fill(15360,0,l);break;default:console.error("THREE.EXRLoader: unsupported type: ",i)}return a.bytesPerLine=a.width*a.inputSize*a.channels,4==a.outputChannels?a.format=s.GWd:a.format=s.VT0,k?a.colorSpace="srgb-linear":a.encoding=3e3,a}(O,P,_,H,this.type),J={value:0},j={R:0,G:1,B:2,A:3,Y:0};for(let e=0;e<U.height/U.scanlineBlockSize;e++){let t=w(P,H);U.size=w(P,H),U.lines=t+U.scanlineBlockSize>U.height?U.height-t:U.scanlineBlockSize;let r=U.size<U.lines*U.bytesPerLine?U.uncompress(U):v(U);H.value+=U.size;for(let t=0;t<U.scanlineBlockSize;t++){let n=t+e*U.scanlineBlockSize;if(n>=U.height)break;for(let e=0;e<U.channels;e++){let i=j[O.channels[e].name];for(let a=0;a<U.width;a++){J.value=(t*(U.channels*U.width)+e*U.width+a)*U.inputSize;let o=(U.height-1-n)*(U.width*U.outputChannels)+a*U.outputChannels+i;U.byteArray[o]=U.getter(r,J)}}}}return{header:O,width:U.width,height:U.height,data:U.byteArray,format:U.format,[k?"colorSpace":"encoding"]:U[k?"colorSpace":"encoding"],type:this.type}}setDataType(e){return this.type=e,this}load(e,t,r,n){return super.load(e,function(e,r){k?e.colorSpace=r.colorSpace:e.encoding=r.encoding,e.minFilter=s.k6q,e.magFilter=s.k6q,e.generateMipmaps=!1,e.flipY=!1,t&&t(e,r)},r,n)}}var j=r(7431);let N=(e,t,r)=>{let n;switch(e){case s.OUM:n=new Uint8ClampedArray(t*r*4);break;case s.ix0:n=new Uint16Array(t*r*4);break;case s.bkx:n=new Uint32Array(t*r*4);break;case s.tJf:n=new Int8Array(t*r*4);break;case s.fBL:n=new Int16Array(t*r*4);break;case s.Yuy:n=new Int32Array(t*r*4);break;case s.RQf:n=new Float32Array(t*r*4);break;default:throw Error("Unsupported data type")}return n};class K{constructor(e){var t,r,i,a,o,l,c,u,d,f,h,p,A,m,v,g;this._rendererIsDisposable=!1,this._supportsReadPixels=!0,this.render=()=>{this._renderer.setRenderTarget(this._renderTarget);try{this._renderer.render(this._scene,this._camera)}catch(e){throw this._renderer.setRenderTarget(null),e}this._renderer.setRenderTarget(null)},this._width=e.width,this._height=e.height,this._type=e.type,this._colorSpace=e.colorSpace;let B={format:s.GWd,depthBuffer:!1,stencilBuffer:!1,type:this._type,colorSpace:this._colorSpace,anisotropy:(null==(t=e.renderTargetOptions)?void 0:t.anisotropy)!==void 0?null==(r=e.renderTargetOptions)?void 0:r.anisotropy:1,generateMipmaps:(null==(i=e.renderTargetOptions)?void 0:i.generateMipmaps)!==void 0&&(null==(a=e.renderTargetOptions)?void 0:a.generateMipmaps),magFilter:(null==(o=e.renderTargetOptions)?void 0:o.magFilter)!==void 0?null==(l=e.renderTargetOptions)?void 0:l.magFilter:s.k6q,minFilter:(null==(c=e.renderTargetOptions)?void 0:c.minFilter)!==void 0?null==(u=e.renderTargetOptions)?void 0:u.minFilter:s.k6q,samples:(null==(d=e.renderTargetOptions)?void 0:d.samples)!==void 0?null==(f=e.renderTargetOptions)?void 0:f.samples:void 0,wrapS:(null==(h=e.renderTargetOptions)?void 0:h.wrapS)!==void 0?null==(p=e.renderTargetOptions)?void 0:p.wrapS:s.ghU,wrapT:(null==(A=e.renderTargetOptions)?void 0:A.wrapT)!==void 0?null==(m=e.renderTargetOptions)?void 0:m.wrapT:s.ghU};if(this._material=e.material,e.renderer?this._renderer=e.renderer:(this._renderer=K.instantiateRenderer(),this._rendererIsDisposable=!0),this._scene=new s.Z58,this._camera=new s.qUd,this._camera.position.set(0,0,10),this._camera.left=-.5,this._camera.right=.5,this._camera.top=.5,this._camera.bottom=-.5,this._camera.updateProjectionMatrix(),!((e,t,r,i)=>{if(void 0!==n)return n;let a=new s.nWS(1,1,i);t.setRenderTarget(a);let o=new s.eaF(new s.bdM,new s.V9B({color:0xffffff}));t.render(o,r),t.setRenderTarget(null);let l=N(e,a.width,a.height);return t.readRenderTargetPixels(a,0,0,a.width,a.height,l),a.dispose(),o.geometry.dispose(),o.material.dispose(),n=0!==l[0]})(this._type,this._renderer,this._camera,B)){let e;this._type===s.ix0&&(e=this._renderer.extensions.has("EXT_color_buffer_float")?s.RQf:void 0),void 0!==e?(console.warn(`This browser does not support reading pixels from ${this._type} RenderTargets, switching to ${s.RQf}`),this._type=e):(this._supportsReadPixels=!1,console.warn("This browser dos not support toArray or toDataTexture, calls to those methods will result in an error thrown"))}this._quad=new s.eaF(new s.bdM,this._material),this._quad.geometry.computeBoundingBox(),this._scene.add(this._quad),this._renderTarget=new s.nWS(this.width,this.height,B),this._renderTarget.texture.mapping=(null==(v=e.renderTargetOptions)?void 0:v.mapping)!==void 0?null==(g=e.renderTargetOptions)?void 0:g.mapping:s.UTZ}static instantiateRenderer(){let e=new j.WebGLRenderer;return e.setSize(128,128),e}toArray(){if(!this._supportsReadPixels)throw Error("Can't read pixels in this browser");let e=N(this._type,this._width,this._height);return this._renderer.readRenderTargetPixels(this._renderTarget,0,0,this._width,this._height,e),e}toDataTexture(e){let t=new s.GYF(this.toArray(),this.width,this.height,s.GWd,this._type,(null==e?void 0:e.mapping)||s.UTZ,(null==e?void 0:e.wrapS)||s.ghU,(null==e?void 0:e.wrapT)||s.ghU,(null==e?void 0:e.magFilter)||s.k6q,(null==e?void 0:e.minFilter)||s.k6q,(null==e?void 0:e.anisotropy)||1,s.Zr2);return t.generateMipmaps=(null==e?void 0:e.generateMipmaps)!==void 0&&(null==e?void 0:e.generateMipmaps),t}disposeOnDemandRenderer(){this._renderer.setRenderTarget(null),this._rendererIsDisposable&&(this._renderer.dispose(),this._renderer.forceContextLoss())}dispose(e){this.disposeOnDemandRenderer(),e&&this.renderTarget.dispose(),this.material instanceof s.BKk&&Object.values(this.material.uniforms).forEach(e=>{e.value instanceof s.gPd&&e.value.dispose()}),Object.values(this.material).forEach(e=>{e instanceof s.gPd&&e.dispose()}),this.material.dispose(),this._quad.geometry.dispose()}get width(){return this._width}set width(e){this._width=e,this._renderTarget.setSize(this._width,this._height)}get height(){return this._height}set height(e){this._height=e,this._renderTarget.setSize(this._width,this._height)}get renderer(){return this._renderer}get renderTarget(){return this._renderTarget}set renderTarget(e){this._renderTarget=e,this._width=e.width,this._height=e.height}get material(){return this._material}get type(){return this._type}get colorSpace(){return this._colorSpace}}let Q=`
varying vec2 vUv;

void main() {
  vUv = uv;
  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
`,X=`
// min half float value
#define HALF_FLOAT_MIN vec3( -65504, -65504, -65504 )
// max half float value
#define HALF_FLOAT_MAX vec3( 65504, 65504, 65504 )

uniform sampler2D sdr;
uniform sampler2D gainMap;
uniform vec3 gamma;
uniform vec3 offsetHdr;
uniform vec3 offsetSdr;
uniform vec3 gainMapMin;
uniform vec3 gainMapMax;
uniform float weightFactor;

varying vec2 vUv;

void main() {
  vec3 rgb = texture2D( sdr, vUv ).rgb;
  vec3 recovery = texture2D( gainMap, vUv ).rgb;
  vec3 logRecovery = pow( recovery, gamma );
  vec3 logBoost = gainMapMin * ( 1.0 - logRecovery ) + gainMapMax * logRecovery;
  vec3 hdrColor = (rgb + offsetSdr) * exp2( logBoost * weightFactor ) - offsetHdr;
  vec3 clampedHdrColor = max( HALF_FLOAT_MIN, min( HALF_FLOAT_MAX, hdrColor ));
  gl_FragColor = vec4( clampedHdrColor , 1.0 );
}
`;class Y extends s.BKk{constructor({gamma:e,offsetHdr:t,offsetSdr:r,gainMapMin:n,gainMapMax:i,maxDisplayBoost:a,hdrCapacityMin:o,hdrCapacityMax:l,sdr:c,gainMap:u}){super({name:"GainMapDecoderMaterial",vertexShader:Q,fragmentShader:X,uniforms:{sdr:{value:c},gainMap:{value:u},gamma:{value:new s.Pq0(1/e[0],1/e[1],1/e[2])},offsetHdr:{value:new s.Pq0().fromArray(t)},offsetSdr:{value:new s.Pq0().fromArray(r)},gainMapMin:{value:new s.Pq0().fromArray(n)},gainMapMax:{value:new s.Pq0().fromArray(i)},weightFactor:{value:(Math.log2(a)-o)/(l-o)}},blending:s.XIg,depthTest:!1,depthWrite:!1}),this._maxDisplayBoost=a,this._hdrCapacityMin=o,this._hdrCapacityMax=l,this.needsUpdate=!0,this.uniformsNeedUpdate=!0}get sdr(){return this.uniforms.sdr.value}set sdr(e){this.uniforms.sdr.value=e}get gainMap(){return this.uniforms.gainMap.value}set gainMap(e){this.uniforms.gainMap.value=e}get offsetHdr(){return this.uniforms.offsetHdr.value.toArray()}set offsetHdr(e){this.uniforms.offsetHdr.value.fromArray(e)}get offsetSdr(){return this.uniforms.offsetSdr.value.toArray()}set offsetSdr(e){this.uniforms.offsetSdr.value.fromArray(e)}get gainMapMin(){return this.uniforms.gainMapMin.value.toArray()}set gainMapMin(e){this.uniforms.gainMapMin.value.fromArray(e)}get gainMapMax(){return this.uniforms.gainMapMax.value.toArray()}set gainMapMax(e){this.uniforms.gainMapMax.value.fromArray(e)}get gamma(){let e=this.uniforms.gamma.value;return[1/e.x,1/e.y,1/e.z]}set gamma(e){let t=this.uniforms.gamma.value;t.x=1/e[0],t.y=1/e[1],t.z=1/e[2]}get hdrCapacityMin(){return this._hdrCapacityMin}set hdrCapacityMin(e){this._hdrCapacityMin=e,this.calculateWeight()}get hdrCapacityMax(){return this._hdrCapacityMax}set hdrCapacityMax(e){this._hdrCapacityMax=e,this.calculateWeight()}get maxDisplayBoost(){return this._maxDisplayBoost}set maxDisplayBoost(e){this._maxDisplayBoost=Math.max(1,Math.min(65504,e)),this.calculateWeight()}calculateWeight(){let e=(Math.log2(this._maxDisplayBoost)-this._hdrCapacityMin)/(this._hdrCapacityMax-this._hdrCapacityMin);this.uniforms.weightFactor.value=Math.max(0,Math.min(1,e))}}class W extends Error{}class z extends Error{}let Z=(e,t,r)=>{let n=RegExp(`${t}="([^"]*)"`,"i").exec(e);if(n)return n[1];let i=RegExp(`<${t}[^>]*>([\\s\\S]*?)</${t}>`,"i").exec(e);if(i){let e=i[1].match(/<rdf:li>([^<]*)<\/rdf:li>/g);return e&&3===e.length?e.map(e=>e.replace(/<\/?rdf:li>/g,"")):i[1].trim()}if(void 0!==r)return r;throw Error(`Can't find ${t} in gainmap metadata`)};class q{constructor(e){this.options={debug:!!e&&void 0!==e.debug&&e.debug,extractFII:!e||void 0===e.extractFII||e.extractFII,extractNonFII:!e||void 0===e.extractNonFII||e.extractNonFII}}extract(e){return new Promise((t,r)=>{let n,i=this.options.debug,a=new DataView(e.buffer);if(65496!==a.getUint16(0))return void r(Error("Not a valid jpeg"));let o=a.byteLength,s=2,l=0;for(;s<o;){if(++l>250)return void r(Error(`Found no marker after ${l} loops 😵`));if(255!==a.getUint8(s))return void r(Error(`Not a valid marker at offset 0x${s.toString(16)}, found: 0x${a.getUint8(s).toString(16)}`));if(n=a.getUint8(s+1),i&&console.log(`Marker: ${n.toString(16)}`),226===n){i&&console.log("Found APP2 marker (0xffe2)");let e=s+4;if(0x4d504600===a.getUint32(e)){let n,i=e+4;if(18761===a.getUint16(i))n=!1;else{if(19789!==a.getUint16(i))return void r(Error("No valid endianness marker found in TIFF header"));n=!0}if(42!==a.getUint16(i+2,!n))return void r(Error("Not valid TIFF data! (no 0x002A marker)"));let o=a.getUint32(i+4,!n);if(o<8)return void r(Error("Not valid TIFF data! (First offset less than 8)"));let s=i+o,l=a.getUint16(s,!n),c=s+2,u=0;for(let e=c;e<c+12*l;e+=12)45057===a.getUint16(e,!n)&&(u=a.getUint32(e+8,!n));let d=s+2+12*l+4,f=[];for(let e=d;e<d+16*u;e+=16){let t={MPType:a.getUint32(e,!n),size:a.getUint32(e+4,!n),dataOffset:a.getUint32(e+8,!n),dependantImages:a.getUint32(e+12,!n),start:-1,end:-1,isFII:!1};t.dataOffset?(t.start=i+t.dataOffset,t.isFII=!1):(t.start=0,t.isFII=!0),t.end=t.start+t.size,f.push(t)}if(this.options.extractNonFII&&f.length){let e=new Blob([a]),r=[];for(let t of f){if(t.isFII&&!this.options.extractFII)continue;let n=e.slice(t.start,t.end+1,"image/jpeg");r.push(n)}t(r)}}}s+=2+a.getUint16(s+2)}})}}let V=async e=>{let t=(e=>{let t,r=(t="undefined"!=typeof TextDecoder?new TextDecoder().decode(e):e.toString()).indexOf("<x:xmpmeta");for(;-1!==r;){let e=t.indexOf("x:xmpmeta>",r),n=t.slice(r,e+10);try{let e=Z(n,"hdrgm:GainMapMin","0"),t=Z(n,"hdrgm:GainMapMax"),r=Z(n,"hdrgm:Gamma","1"),i=Z(n,"hdrgm:OffsetSDR","0.015625"),a=Z(n,"hdrgm:OffsetHDR","0.015625"),o=/hdrgm:HDRCapacityMin="([^"]*)"/.exec(n),s=o?o[1]:"0",l=/hdrgm:HDRCapacityMax="([^"]*)"/.exec(n);if(!l)throw Error("Incomplete gainmap metadata");let c=l[1];return{gainMapMin:Array.isArray(e)?e.map(e=>parseFloat(e)):[parseFloat(e),parseFloat(e),parseFloat(e)],gainMapMax:Array.isArray(t)?t.map(e=>parseFloat(e)):[parseFloat(t),parseFloat(t),parseFloat(t)],gamma:Array.isArray(r)?r.map(e=>parseFloat(e)):[parseFloat(r),parseFloat(r),parseFloat(r)],offsetSdr:Array.isArray(i)?i.map(e=>parseFloat(e)):[parseFloat(i),parseFloat(i),parseFloat(i)],offsetHdr:Array.isArray(a)?a.map(e=>parseFloat(e)):[parseFloat(a),parseFloat(a),parseFloat(a)],hdrCapacityMin:parseFloat(s),hdrCapacityMax:parseFloat(c)}}catch(e){}r=t.indexOf("<x:xmpmeta",e)}})(e);if(!t)throw new z("Gain map XMP metadata not found");let r=new q({extractFII:!0,extractNonFII:!0}),n=await r.extract(e);if(2!==n.length)throw new W("Gain map recovery image not found");return{sdr:new Uint8Array(await n[0].arrayBuffer()),gainMap:new Uint8Array(await n[1].arrayBuffer()),metadata:t}},$=e=>new Promise((t,r)=>{let n=document.createElement("img");n.onload=()=>{t(n)},n.onerror=e=>{r(e)},n.src=URL.createObjectURL(e)});class ee extends s.aHM{constructor(e,t){super(t),e&&(this._renderer=e),this._internalLoadingManager=new s.KPJ}setRenderer(e){return this._renderer=e,this}setRenderTargetOptions(e){return this._renderTargetOptions=e,this}prepareQuadRenderer(){this._renderer||console.warn("WARNING: An existing WebGL Renderer was not passed to this Loader constructor or in setRenderer, the result of this Loader will need to be converted to a Data Texture with toDataTexture() before you can use it in your renderer.");let e=new Y({gainMapMax:[1,1,1],gainMapMin:[0,0,0],gamma:[1,1,1],offsetHdr:[1,1,1],offsetSdr:[1,1,1],hdrCapacityMax:1,hdrCapacityMin:0,maxDisplayBoost:1,gainMap:new s.gPd,sdr:new s.gPd});return new K({width:16,height:16,type:s.ix0,colorSpace:s.Zr2,material:e,renderer:this._renderer,renderTargetOptions:this._renderTargetOptions})}async render(e,t,r,n){let i,a,o=n?new Blob([n],{type:"image/jpeg"}):void 0,l=new Blob([r],{type:"image/jpeg"}),c=!1;if("undefined"==typeof createImageBitmap){let e=await Promise.all([o?$(o):Promise.resolve(void 0),$(l)]);a=e[0],i=e[1],c=!0}else{let e=await Promise.all([o?createImageBitmap(o,{imageOrientation:"flipY"}):Promise.resolve(void 0),createImageBitmap(l,{imageOrientation:"flipY"})]);a=e[0],i=e[1]}let u=new s.gPd(a||new ImageData(2,2),s.UTZ,s.ghU,s.ghU,s.k6q,s.NZq,s.GWd,s.OUM,1,s.Zr2);u.flipY=c,u.needsUpdate=!0;let d=new s.gPd(i,s.UTZ,s.ghU,s.ghU,s.k6q,s.NZq,s.GWd,s.OUM,1,s.er$);d.flipY=c,d.needsUpdate=!0,e.width=i.width,e.height=i.height,e.material.gainMap=u,e.material.sdr=d,e.material.gainMapMin=t.gainMapMin,e.material.gainMapMax=t.gainMapMax,e.material.offsetHdr=t.offsetHdr,e.material.offsetSdr=t.offsetSdr,e.material.gamma=t.gamma,e.material.hdrCapacityMin=t.hdrCapacityMin,e.material.hdrCapacityMax=t.hdrCapacityMax,e.material.maxDisplayBoost=Math.pow(2,t.hdrCapacityMax),e.material.needsUpdate=!0,e.render()}}class et extends ee{load([e,t,r],n,i,a){let o,l,c,u=this.prepareQuadRenderer(),d=async()=>{if(o&&l&&c){try{await this.render(u,c,o,l)}catch(n){this.manager.itemError(e),this.manager.itemError(t),this.manager.itemError(r),"function"==typeof a&&a(n),u.disposeOnDemandRenderer();return}"function"==typeof n&&n(u),this.manager.itemEnd(e),this.manager.itemEnd(t),this.manager.itemEnd(r),u.disposeOnDemandRenderer()}},f=!0,h=0,p=0,A=!0,m=0,v=0,g=!0,B=0,C=0,b=()=>{"function"==typeof i&&i(new ProgressEvent("progress",{lengthComputable:f&&A&&g,loaded:p+v+C,total:h+m+B}))};this.manager.itemStart(e),this.manager.itemStart(t),this.manager.itemStart(r);let y=new s.Y9S(this._internalLoadingManager);y.setResponseType("arraybuffer"),y.setRequestHeader(this.requestHeader),y.setPath(this.path),y.setWithCredentials(this.withCredentials),y.load(e,async e=>{if("string"==typeof e)throw Error("Invalid sdr buffer");o=e,await d()},e=>{f=e.lengthComputable,p=e.loaded,h=e.total,b()},t=>{this.manager.itemError(e),"function"==typeof a&&a(t)});let E=new s.Y9S(this._internalLoadingManager);E.setResponseType("arraybuffer"),E.setRequestHeader(this.requestHeader),E.setPath(this.path),E.setWithCredentials(this.withCredentials),E.load(t,async e=>{if("string"==typeof e)throw Error("Invalid gainmap buffer");l=e,await d()},e=>{A=e.lengthComputable,v=e.loaded,m=e.total,b()},e=>{this.manager.itemError(t),"function"==typeof a&&a(e)});let M=new s.Y9S(this._internalLoadingManager);return M.setRequestHeader(this.requestHeader),M.setPath(this.path),M.setWithCredentials(this.withCredentials),M.load(r,async e=>{if("string"!=typeof e)throw Error("Invalid metadata string");c=JSON.parse(e),await d()},e=>{g=e.lengthComputable,C=e.loaded,B=e.total,b()},e=>{this.manager.itemError(r),"function"==typeof a&&a(e)}),u}}class er extends ee{load(e,t,r,n){let i=this.prepareQuadRenderer(),a=new s.Y9S(this._internalLoadingManager);return a.setResponseType("arraybuffer"),a.setRequestHeader(this.requestHeader),a.setPath(this.path),a.setWithCredentials(this.withCredentials),this.manager.itemStart(e),a.load(e,async r=>{let a,o,s;if("string"==typeof r)throw Error("Invalid buffer, received [string], was expecting [ArrayBuffer]");let l=new Uint8Array(r);try{let e=await V(l);a=e.sdr,o=e.gainMap,s=e.metadata}catch(t){if(t instanceof z||t instanceof W)console.warn(`Failure to reconstruct an HDR image from ${e}: Gain map metadata not found in the file, HDRJPGLoader will render the SDR jpeg`),s={gainMapMin:[0,0,0],gainMapMax:[1,1,1],gamma:[1,1,1],hdrCapacityMin:0,hdrCapacityMax:1,offsetHdr:[0,0,0],offsetSdr:[0,0,0]},a=l;else throw t}try{await this.render(i,s,a,o)}catch(t){this.manager.itemError(e),"function"==typeof n&&n(t),i.disposeOnDemandRenderer();return}"function"==typeof t&&t(i),this.manager.itemEnd(e),i.disposeOnDemandRenderer()},r,t=>{this.manager.itemError(e),"function"==typeof n&&n(t)}),i}}let en={apartment:"lebombo_1k.hdr",city:"potsdamer_platz_1k.hdr",dawn:"kiara_1_dawn_1k.hdr",forest:"forest_slope_1k.hdr",lobby:"st_fagans_interior_1k.hdr",night:"dikhololo_night_1k.hdr",park:"rooitou_park_1k.hdr",studio:"studio_small_03_1k.hdr",sunset:"venice_sunset_1k.hdr",warehouse:"empty_warehouse_01_1k.hdr"},ei="https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/",ea=e=>Array.isArray(e),eo=["/px.png","/nx.png","/py.png","/ny.png","/pz.png","/nz.png"];function es({files:e=eo,path:t="",preset:r,colorSpace:n,extensions:i}={}){r&&(eu(r),e=en[r],t=ei);let l=ea(e),{extension:c,isCubemap:u}=ed(e),d=ef(c);if(!d)throw Error("useEnvironment: Unrecognized file extension: "+e);let f=(0,o.C)(e=>e.gl);(0,a.useLayoutEffect)(()=>{("webp"===c||"jpg"===c||"jpeg"===c)&&f.domElement.addEventListener("webglcontextlost",function(){o.G.clear(d,l?[e]:e)},{once:!0})},[e,f.domElement]);let h=(0,o.G)(d,l?[e]:e,e=>{("webp"===c||"jpg"===c||"jpeg"===c)&&e.setRenderer(f),null==e.setPath||e.setPath(t),i&&i(e)}),p=l?h[0]:h;if("jpg"===c||"jpeg"===c||"webp"===c){var A;p=null==(A=p.renderTarget)?void 0:A.texture}return p.mapping=u?s.hy7:s.wfO,p.colorSpace=null!=n?n:u?"srgb":"srgb-linear",p}let el={files:eo,path:"",preset:void 0,extensions:void 0};es.preload=e=>{let t={...el,...e},{files:r,path:n=""}=t,{preset:i,extensions:a}=t;i&&(eu(i),r=en[i],n=ei);let{extension:s}=ed(r);if("webp"===s||"jpg"===s||"jpeg"===s)throw Error("useEnvironment: Preloading gainmaps is not supported");let l=ef(s);if(!l)throw Error("useEnvironment: Unrecognized file extension: "+r);o.G.preload(l,ea(r)?[r]:r,e=>{null==e.setPath||e.setPath(n),a&&a(e)})};let ec={files:eo,preset:void 0};function eu(e){if(!(e in en))throw Error("Preset must be one of: "+Object.keys(en).join(", "))}function ed(e){var t;let r=ea(e)&&6===e.length,n=ea(e)&&3===e.length&&e.some(e=>e.endsWith("json")),i=ea(e)?e[0]:e;return{extension:r?"cube":n?"webp":i.startsWith("data:application/exr")?"exr":i.startsWith("data:application/hdr")?"hdr":i.startsWith("data:image/jpeg")?"jpg":null==(t=i.split(".").pop())||null==(t=t.split("?"))||null==(t=t.shift())?void 0:t.toLowerCase(),isCubemap:r,isGainmap:n}}function ef(e){return"cube"===e?s.ScU:"hdr"===e?u:"exr"===e?J:"jpg"===e||"jpeg"===e?er:"webp"===e?et:null}function eh(e,t,r,n,i={}){var a,s,l,c;let u,d;i={backgroundBlurriness:0,backgroundIntensity:1,backgroundRotation:[0,0,0],environmentIntensity:1,environmentRotation:[0,0,0],...i};let f=(d=u=t||r).current&&d.current.isScene?u.current:u,h=f.background,p=f.environment,A={backgroundBlurriness:f.backgroundBlurriness,backgroundIntensity:f.backgroundIntensity,backgroundRotation:null!=(a=null==(s=f.backgroundRotation)||null==s.clone?void 0:s.clone())?a:[0,0,0],environmentIntensity:f.environmentIntensity,environmentRotation:null!=(l=null==(c=f.environmentRotation)||null==c.clone?void 0:c.clone())?l:[0,0,0]};return"only"!==e&&(f.environment=n),e&&(f.background=n),(0,o.s)(f,i),()=>{"only"!==e&&(f.environment=p),e&&(f.background=h),(0,o.s)(f,A)}}function ep({scene:e,background:t=!1,map:r,...n}){let i=(0,o.C)(e=>e.scene);return a.useLayoutEffect(()=>{if(r)return eh(t,e,i,r,n)}),null}function eA({background:e=!1,scene:t,blur:r,backgroundBlurriness:n,backgroundIntensity:i,backgroundRotation:s,environmentIntensity:l,environmentRotation:c,...u}){let d=es(u),f=(0,o.C)(e=>e.scene);return a.useLayoutEffect(()=>eh(e,t,f,d,{backgroundBlurriness:null!=r?r:n,backgroundIntensity:i,backgroundRotation:s,environmentIntensity:l,environmentRotation:c})),a.useEffect(()=>()=>{d.dispose()},[d]),null}function em({children:e,near:t=.1,far:r=1e3,resolution:n=256,frames:i=1,map:l,background:c=!1,blur:u,backgroundBlurriness:d,backgroundIntensity:f,backgroundRotation:h,environmentIntensity:p,environmentRotation:A,scene:m,files:v,path:g,preset:B,extensions:C}){let b=(0,o.C)(e=>e.gl),y=(0,o.C)(e=>e.scene),E=a.useRef(null),[M]=a.useState(()=>new s.Z58),w=a.useMemo(()=>{let e=new s.o6l(n);return e.texture.type=s.ix0,e},[n]);a.useEffect(()=>()=>{w.dispose()},[w]),a.useLayoutEffect(()=>{if(1===i){let e=b.autoClear;b.autoClear=!0,E.current.update(b,M),b.autoClear=e}return eh(c,m,y,w.texture,{backgroundBlurriness:null!=u?u:d,backgroundIntensity:f,backgroundRotation:h,environmentIntensity:p,environmentRotation:A})},[e,M,w.texture,m,y,c,i,b]);let x=1;return(0,o.D)(()=>{if(i===1/0||x<i){let e=b.autoClear;b.autoClear=!0,E.current.update(b,M),b.autoClear=e,x++}}),a.createElement(a.Fragment,null,(0,o.o)(a.createElement(a.Fragment,null,e,a.createElement("cubeCamera",{ref:E,args:[t,r,w]}),v||B?a.createElement(eA,{background:!0,files:v,preset:B,path:g,extensions:C}):l?a.createElement(ep,{background:!0,map:l,extensions:C}):null),M))}function ev(e){var t,r,n,s;let l=es(e),u=e.map||l;a.useMemo(()=>(0,o.e)({GroundProjectedEnvImpl:c}),[]),a.useEffect(()=>()=>{l.dispose()},[l]);let d=a.useMemo(()=>[u],[u]),f=null==(t=e.ground)?void 0:t.height,h=null==(r=e.ground)?void 0:r.radius,p=null!=(n=null==(s=e.ground)?void 0:s.scale)?n:1e3;return a.createElement(a.Fragment,null,a.createElement(ep,(0,i.A)({},e,{map:u})),a.createElement("groundProjectedEnvImpl",{args:d,scale:p,height:f,radius:h}))}function eg(e){return e.ground?a.createElement(ev,e):e.map?a.createElement(ep,e):e.children?a.createElement(em,e):a.createElement(eA,e)}es.clear=e=>{let t={...ec,...e},{files:r}=t,{preset:n}=t;n&&(eu(n),r=en[n]);let{extension:i}=ed(r),a=ef(i);if(!a)throw Error("useEnvironment: Unrecognized file extension: "+r);o.G.clear(a,ea(r)?[r]:r)}},3064:(e,t,r)=>{r.d(t,{Do:()=>a,Fh:()=>h});var n=r(7431),i=r(3264);let a=/\bvoid\s+main\s*\(\s*\)\s*{/g;function o(e){return e.replace(/^[ \t]*#include +<([\w\d./]+)>/gm,function(e,t){let r=n.ShaderChunk[t];return r?o(r):e})}let s=[];for(let e=0;e<256;e++)s[e]=(e<16?"0":"")+e.toString(16);let l=Object.assign||function(){let e=arguments[0];for(let t=1,r=arguments.length;t<r;t++){let r=arguments[t];if(r)for(let t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},c=Date.now(),u=new WeakMap,d=new Map,f=1e10;function h(e,t){let r=function(e){let t=JSON.stringify(e,A),r=v.get(t);return null==r&&v.set(t,r=++m),r}(t),n=u.get(e);if(n||u.set(e,n=Object.create(null)),n[r])return new n[r];let a=`_onBeforeCompile${r}`,g=function(n,i){e.onBeforeCompile.call(this,n,i);let s=this.customProgramCacheKey()+"|"+n.vertexShader+"|"+n.fragmentShader,u=d[s];if(!u){let e=function(e,{vertexShader:t,fragmentShader:r},n,i){let{vertexDefs:a,vertexMainIntro:s,vertexMainOutro:l,vertexTransform:c,fragmentDefs:u,fragmentMainIntro:d,fragmentMainOutro:f,fragmentColorTransform:h,customRewriter:A,timeUniform:m}=n;if(a=a||"",s=s||"",l=l||"",u=u||"",d=d||"",f=f||"",(c||A)&&(t=o(t)),(h||A)&&(r=o(r=r.replace(/^[ \t]*#include <((?:tonemapping|encodings|colorspace|fog|premultiplied_alpha|dithering)_fragment)>/gm,"\n//!BEGIN_POST_CHUNK $1\n$&\n//!END_POST_CHUNK\n"))),A){let e=A({vertexShader:t,fragmentShader:r});t=e.vertexShader,r=e.fragmentShader}if(h){let e=[];r=r.replace(/^\/\/!BEGIN_POST_CHUNK[^]+?^\/\/!END_POST_CHUNK/gm,t=>(e.push(t),"")),f=`${h}
${e.join("\n")}
${f}`}if(m){let e=`
uniform float ${m};
`;a=e+a,u=e+u}return c&&(t=`vec3 troika_position_${i};
vec3 troika_normal_${i};
vec2 troika_uv_${i};
${t}
`,a=`${a}
void troikaVertexTransform${i}(inout vec3 position, inout vec3 normal, inout vec2 uv) {
  ${c}
}
`,s=`
troika_position_${i} = vec3(position);
troika_normal_${i} = vec3(normal);
troika_uv_${i} = vec2(uv);
troikaVertexTransform${i}(troika_position_${i}, troika_normal_${i}, troika_uv_${i});
${s}
`,t=t.replace(/\b(position|normal|uv)\b/g,(e,t,r,n)=>/\battribute\s+vec[23]\s+$/.test(n.substr(0,r))?t:`troika_${t}_${i}`),e.map&&e.map.channel>0||(t=t.replace(/\bMAP_UV\b/g,`troika_uv_${i}`))),{vertexShader:t=p(t,i,a,s,l),fragmentShader:r=p(r,i,u,d,f)}}(this,n,t,r);u=d[s]=e}n.vertexShader=u.vertexShader,n.fragmentShader=u.fragmentShader,l(n.uniforms,this.uniforms),t.timeUniform&&(n.uniforms[t.timeUniform]={get value(){return Date.now()-c}}),this[a]&&this[a](n)},B=function(){return C(t.chained?e:e.clone())},C=function(n){let i=Object.create(n,b);return Object.defineProperty(i,"baseMaterial",{value:e}),Object.defineProperty(i,"id",{value:f++}),i.uuid=function(){let e=0xffffffff*Math.random()|0,t=0xffffffff*Math.random()|0,r=0xffffffff*Math.random()|0,n=0xffffffff*Math.random()|0;return(s[255&e]+s[e>>8&255]+s[e>>16&255]+s[e>>24&255]+"-"+s[255&t]+s[t>>8&255]+"-"+s[t>>16&15|64]+s[t>>24&255]+"-"+s[63&r|128]+s[r>>8&255]+"-"+s[r>>16&255]+s[r>>24&255]+s[255&n]+s[n>>8&255]+s[n>>16&255]+s[n>>24&255]).toUpperCase()}(),i.uniforms=l({},n.uniforms,t.uniforms),i.defines=l({},n.defines,t.defines),i.defines[`TROIKA_DERIVED_MATERIAL_${r}`]="",i.extensions=l({},n.extensions,t.extensions),i._listeners=void 0,i},b={constructor:{value:B},isDerivedMaterial:{value:!0},type:{get:()=>e.type,set:t=>{e.type=t}},isDerivedFrom:{writable:!0,configurable:!0,value:function(e){let t=this.baseMaterial;return e===t||t.isDerivedMaterial&&t.isDerivedFrom(e)||!1}},customProgramCacheKey:{writable:!0,configurable:!0,value:function(){return e.customProgramCacheKey()+"|"+r}},onBeforeCompile:{get:()=>g,set(e){this[a]=e}},copy:{writable:!0,configurable:!0,value:function(t){return e.copy.call(this,t),e.isShaderMaterial||e.isDerivedMaterial||(l(this.extensions,t.extensions),l(this.defines,t.defines),l(this.uniforms,i.LlO.clone(t.uniforms))),this}},clone:{writable:!0,configurable:!0,value:function(){return C(new e.constructor).copy(this)}},getDepthMaterial:{writable:!0,configurable:!0,value:function(){let r=this._depthMaterial;return r||((r=this._depthMaterial=h(e.isDerivedMaterial?e.getDepthMaterial():new i.CSG({depthPacking:i.N5j}),t)).defines.IS_DEPTH_MATERIAL="",r.uniforms=this.uniforms),r}},getDistanceMaterial:{writable:!0,configurable:!0,value:function(){let r=this._distanceMaterial;return r||((r=this._distanceMaterial=h(e.isDerivedMaterial?e.getDistanceMaterial():new i.aVO,t)).defines.IS_DISTANCE_MATERIAL="",r.uniforms=this.uniforms),r}},dispose:{writable:!0,configurable:!0,value(){let{_depthMaterial:t,_distanceMaterial:r}=this;t&&t.dispose(),r&&r.dispose(),e.dispose.call(this)}}};return n[r]=B,new B}function p(e,t,r,n,i){return(n||i||r)&&(e=e.replace(a,`
${r}
void troikaOrigMain${t}() {`)+`
void main() {
  ${n}
  troikaOrigMain${t}();
  ${i}
}`),e}function A(e,t){return"uniforms"===e?void 0:"function"==typeof t?t.toString():t}let m=0,v=new Map,g=`
uniform vec3 pointA;
uniform vec3 controlA;
uniform vec3 controlB;
uniform vec3 pointB;
uniform float radius;
varying float bezierT;

vec3 cubicBezier(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {
  float t2 = 1.0 - t;
  float b0 = t2 * t2 * t2;
  float b1 = 3.0 * t * t2 * t2;
  float b2 = 3.0 * t * t * t2;
  float b3 = t * t * t;
  return b0 * p1 + b1 * c1 + b2 * c2 + b3 * p2;
}

vec3 cubicBezierDerivative(vec3 p1, vec3 c1, vec3 c2, vec3 p2, float t) {
  float t2 = 1.0 - t;
  return -3.0 * p1 * t2 * t2 +
    c1 * (3.0 * t2 * t2 - 6.0 * t2 * t) +
    c2 * (6.0 * t2 * t - 3.0 * t * t) +
    3.0 * p2 * t * t;
}
`,B=`
float t = position.y;
bezierT = t;
vec3 bezierCenterPos = cubicBezier(pointA, controlA, controlB, pointB, t);
vec3 bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t));

// Make "sideways" always perpendicular to the camera ray; this ensures that any twists
// in the cylinder occur where you won't see them: 
vec3 viewDirection = normalMatrix * vec3(0.0, 0.0, 1.0);
if (bezierDir == viewDirection) {
  bezierDir = normalize(cubicBezierDerivative(pointA, controlA, controlB, pointB, t == 1.0 ? t - 0.0001 : t + 0.0001));
}
vec3 sideways = normalize(cross(bezierDir, viewDirection));
vec3 upish = normalize(cross(sideways, bezierDir));

// Build a matrix for transforming this disc in the cylinder:
mat4 discTx;
discTx[0].xyz = sideways * radius;
discTx[1].xyz = bezierDir * radius;
discTx[2].xyz = upish * radius;
discTx[3].xyz = bezierCenterPos;
discTx[3][3] = 1.0;

// Apply transform, ignoring original y
position = (discTx * vec4(position.x, 0.0, position.z, 1.0)).xyz;
normal = normalize(mat3(discTx) * normal);
`,C=`
uniform vec3 dashing;
varying float bezierT;
`,b=`
if (dashing.x + dashing.y > 0.0) {
  float dashFrac = mod(bezierT - dashing.z, dashing.x + dashing.y);
  if (dashFrac > dashing.x) {
    discard;
  }
}
`,y=null,E=new i._4j({color:0xffffff,side:i.$EB});class M extends i.eaF{static getGeometry(){return y||(y=new i.Ho_(1,1,1,6,64).translate(0,.5,0))}constructor(){super(M.getGeometry(),E),this.pointA=new i.Pq0,this.controlA=new i.Pq0,this.controlB=new i.Pq0,this.pointB=new i.Pq0,this.radius=.01,this.dashArray=new i.I9Y,this.dashOffset=0,this.frustumCulled=!1}get material(){let e=this._derivedMaterial,t=this._baseMaterial||this._defaultMaterial||(this._defaultMaterial=E.clone());return e&&e.baseMaterial===t||(e=this._derivedMaterial=h(t,{chained:!0,uniforms:{pointA:{value:new i.Pq0},controlA:{value:new i.Pq0},controlB:{value:new i.Pq0},pointB:{value:new i.Pq0},radius:{value:.01},dashing:{value:new i.Pq0}},vertexDefs:g,vertexTransform:B,fragmentDefs:C,fragmentMainIntro:b}),t.addEventListener("dispose",function r(){t.removeEventListener("dispose",r),e.dispose()})),e}set material(e){this._baseMaterial=e}get customDepthMaterial(){return this.material.getDepthMaterial()}set customDepthMaterial(e){}get customDistanceMaterial(){return this.material.getDistanceMaterial()}set customDistanceMaterial(e){}onBeforeRender(){let{uniforms:e}=this.material,{pointA:t,controlA:r,controlB:n,pointB:i,radius:a,dashArray:o,dashOffset:s}=this;e.pointA.value.copy(t),e.controlA.value.copy(r),e.controlB.value.copy(n),e.pointB.value.copy(i),e.radius.value=a,e.dashing.value.set(o.x,o.y,s||0)}raycast(){}}},3127:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},3498:(e,t,r)=>{let n;r.d(t,{p:()=>ec}),r(2115);var i=r(3264);let a=new WeakMap;class o extends i.aHM{constructor(e){super(e),this.decoderPath="",this.decoderConfig={},this.decoderBinary=null,this.decoderPending=null,this.workerLimit=4,this.workerPool=[],this.workerNextTaskID=1,this.workerSourceURL="",this.defaultAttributeIDs={position:"POSITION",normal:"NORMAL",color:"COLOR",uv:"TEX_COORD"},this.defaultAttributeTypes={position:"Float32Array",normal:"Float32Array",color:"Float32Array",uv:"Float32Array"}}setDecoderPath(e){return this.decoderPath=e,this}setDecoderConfig(e){return this.decoderConfig=e,this}setWorkerLimit(e){return this.workerLimit=e,this}load(e,t,r,n){let a=new i.Y9S(this.manager);a.setPath(this.path),a.setResponseType("arraybuffer"),a.setRequestHeader(this.requestHeader),a.setWithCredentials(this.withCredentials),a.load(e,e=>{let r={attributeIDs:this.defaultAttributeIDs,attributeTypes:this.defaultAttributeTypes,useUniqueIDs:!1};this.decodeGeometry(e,r).then(t).catch(n)},r,n)}decodeDracoFile(e,t,r,n){let i={attributeIDs:r||this.defaultAttributeIDs,attributeTypes:n||this.defaultAttributeTypes,useUniqueIDs:!!r};this.decodeGeometry(e,i).then(t)}decodeGeometry(e,t){let r;for(let e in t.attributeTypes){let r=t.attributeTypes[e];void 0!==r.BYTES_PER_ELEMENT&&(t.attributeTypes[e]=r.name)}let n=JSON.stringify(t);if(a.has(e)){let t=a.get(e);if(t.key===n)return t.promise;if(0===e.byteLength)throw Error("THREE.DRACOLoader: Unable to re-decode a buffer with different settings. Buffer has already been transferred.")}let i=this.workerNextTaskID++,o=e.byteLength,s=this._getWorker(i,o).then(n=>(r=n,new Promise((n,a)=>{r._callbacks[i]={resolve:n,reject:a},r.postMessage({type:"decode",id:i,taskConfig:t,buffer:e},[e])}))).then(e=>this._createGeometry(e.geometry));return s.catch(()=>!0).then(()=>{r&&i&&this._releaseTask(r,i)}),a.set(e,{key:n,promise:s}),s}_createGeometry(e){let t=new i.LoY;e.index&&t.setIndex(new i.THS(e.index.array,1));for(let r=0;r<e.attributes.length;r++){let n=e.attributes[r],a=n.name,o=n.array,s=n.itemSize;t.setAttribute(a,new i.THS(o,s))}return t}_loadLibrary(e,t){let r=new i.Y9S(this.manager);return r.setPath(this.decoderPath),r.setResponseType(t),r.setWithCredentials(this.withCredentials),new Promise((t,n)=>{r.load(e,t,void 0,n)})}preload(){return this._initDecoder(),this}_initDecoder(){if(this.decoderPending)return this.decoderPending;let e="object"!=typeof WebAssembly||"js"===this.decoderConfig.type,t=[];return e?t.push(this._loadLibrary("draco_decoder.js","text")):(t.push(this._loadLibrary("draco_wasm_wrapper.js","text")),t.push(this._loadLibrary("draco_decoder.wasm","arraybuffer"))),this.decoderPending=Promise.all(t).then(t=>{let r=t[0];e||(this.decoderConfig.wasmBinary=t[1]);let n=s.toString(),i=["/* draco decoder */",r,"\n/* worker */",n.substring(n.indexOf("{")+1,n.lastIndexOf("}"))].join("\n");this.workerSourceURL=URL.createObjectURL(new Blob([i]))}),this.decoderPending}_getWorker(e,t){return this._initDecoder().then(()=>{if(this.workerPool.length<this.workerLimit){let e=new Worker(this.workerSourceURL);e._callbacks={},e._taskCosts={},e._taskLoad=0,e.postMessage({type:"init",decoderConfig:this.decoderConfig}),e.onmessage=function(t){let r=t.data;switch(r.type){case"decode":e._callbacks[r.id].resolve(r);break;case"error":e._callbacks[r.id].reject(r);break;default:console.error('THREE.DRACOLoader: Unexpected message, "'+r.type+'"')}},this.workerPool.push(e)}else this.workerPool.sort(function(e,t){return e._taskLoad>t._taskLoad?-1:1});let r=this.workerPool[this.workerPool.length-1];return r._taskCosts[e]=t,r._taskLoad+=t,r})}_releaseTask(e,t){e._taskLoad-=e._taskCosts[t],delete e._callbacks[t],delete e._taskCosts[t]}debug(){console.log("Task load: ",this.workerPool.map(e=>e._taskLoad))}dispose(){for(let e=0;e<this.workerPool.length;++e)this.workerPool[e].terminate();return this.workerPool.length=0,this}}function s(){let e,t;onmessage=function(r){let n=r.data;switch(n.type){case"init":e=n.decoderConfig,t=new Promise(function(t){e.onModuleLoaded=function(e){t({draco:e})},DracoDecoderModule(e)});break;case"decode":let i=n.buffer,a=n.taskConfig;t.then(e=>{let t=e.draco,r=new t.Decoder,o=new t.DecoderBuffer;o.Init(new Int8Array(i),i.byteLength);try{let e=function(e,t,r,n){let i,a,o=n.attributeIDs,s=n.attributeTypes,l=t.GetEncodedGeometryType(r);if(l===e.TRIANGULAR_MESH)i=new e.Mesh,a=t.DecodeBufferToMesh(r,i);else if(l===e.POINT_CLOUD)i=new e.PointCloud,a=t.DecodeBufferToPointCloud(r,i);else throw Error("THREE.DRACOLoader: Unexpected geometry type.");if(!a.ok()||0===i.ptr)throw Error("THREE.DRACOLoader: Decoding failed: "+a.error_msg());let c={index:null,attributes:[]};for(let r in o){let a,l,u=self[s[r]];if(n.useUniqueIDs)l=o[r],a=t.GetAttributeByUniqueId(i,l);else{if(-1===(l=t.GetAttributeId(i,e[o[r]])))continue;a=t.GetAttribute(i,l)}c.attributes.push(function(e,t,r,n,i,a){let o=a.num_components(),s=r.num_points()*o,l=s*i.BYTES_PER_ELEMENT,c=function(e,t){switch(t){case Float32Array:return e.DT_FLOAT32;case Int8Array:return e.DT_INT8;case Int16Array:return e.DT_INT16;case Int32Array:return e.DT_INT32;case Uint8Array:return e.DT_UINT8;case Uint16Array:return e.DT_UINT16;case Uint32Array:return e.DT_UINT32}}(e,i),u=e._malloc(l);t.GetAttributeDataArrayForAllPoints(r,a,c,l,u);let d=new i(e.HEAPF32.buffer,u,s).slice();return e._free(u),{name:n,array:d,itemSize:o}}(e,t,i,r,u,a))}return l===e.TRIANGULAR_MESH&&(c.index=function(e,t,r){let n=3*r.num_faces(),i=4*n,a=e._malloc(i);t.GetTrianglesUInt32Array(r,i,a);let o=new Uint32Array(e.HEAPF32.buffer,a,n).slice();return e._free(a),{array:o,itemSize:1}}(e,t,i)),e.destroy(i),c}(t,r,o,a),i=e.attributes.map(e=>e.array.buffer);e.index&&i.push(e.index.array.buffer),self.postMessage({type:"decode",id:n.id,geometry:e},i)}catch(e){console.error(e),self.postMessage({type:"error",id:n.id,error:e.message})}finally{t.destroy(o),t.destroy(r)}})}}}function l(e,t){if(t===i.RJ4)return console.warn("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles."),e;if(t!==i.rYR&&t!==i.O49)return console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:",t),e;{let r=e.getIndex();if(null===r){let t=[],n=e.getAttribute("position");if(void 0===n)return console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible."),e;for(let e=0;e<n.count;e++)t.push(e);e.setIndex(t),r=e.getIndex()}let n=r.count-2,a=[];if(r)if(t===i.rYR)for(let e=1;e<=n;e++)a.push(r.getX(0)),a.push(r.getX(e)),a.push(r.getX(e+1));else for(let e=0;e<n;e++)e%2==0?(a.push(r.getX(e)),a.push(r.getX(e+1)),a.push(r.getX(e+2))):(a.push(r.getX(e+2)),a.push(r.getX(e+1)),a.push(r.getX(e)));a.length/3!==n&&console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.");let o=e.clone();return o.setIndex(a),o.clearGroups(),o}}var c=r(3625);function u(e){if("undefined"!=typeof TextDecoder)return new TextDecoder().decode(e);let t="";for(let r=0,n=e.length;r<n;r++)t+=String.fromCharCode(e[r]);try{return decodeURIComponent(escape(t))}catch(e){return t}}let d="srgb",f="srgb-linear";class h extends i.aHM{constructor(e){super(e),this.dracoLoader=null,this.ktx2Loader=null,this.meshoptDecoder=null,this.pluginCallbacks=[],this.register(function(e){return new B(e)}),this.register(function(e){return new C(e)}),this.register(function(e){return new F(e)}),this.register(function(e){return new D(e)}),this.register(function(e){return new T(e)}),this.register(function(e){return new y(e)}),this.register(function(e){return new E(e)}),this.register(function(e){return new M(e)}),this.register(function(e){return new w(e)}),this.register(function(e){return new g(e)}),this.register(function(e){return new x(e)}),this.register(function(e){return new b(e)}),this.register(function(e){return new R(e)}),this.register(function(e){return new I(e)}),this.register(function(e){return new m(e)}),this.register(function(e){return new G(e)}),this.register(function(e){return new S(e)})}load(e,t,r,n){let a,o=this;if(""!==this.resourcePath)a=this.resourcePath;else if(""!==this.path){let t=i.r6x.extractUrlBase(e);a=i.r6x.resolveURL(t,this.path)}else a=i.r6x.extractUrlBase(e);this.manager.itemStart(e);let s=function(t){n?n(t):console.error(t),o.manager.itemError(e),o.manager.itemEnd(e)},l=new i.Y9S(this.manager);l.setPath(this.path),l.setResponseType("arraybuffer"),l.setRequestHeader(this.requestHeader),l.setWithCredentials(this.withCredentials),l.load(e,function(r){try{o.parse(r,a,function(r){t(r),o.manager.itemEnd(e)},s)}catch(e){s(e)}},r,s)}setDRACOLoader(e){return this.dracoLoader=e,this}setDDSLoader(){throw Error('THREE.GLTFLoader: "MSFT_texture_dds" no longer supported. Please update to "KHR_texture_basisu".')}setKTX2Loader(e){return this.ktx2Loader=e,this}setMeshoptDecoder(e){return this.meshoptDecoder=e,this}register(e){return -1===this.pluginCallbacks.indexOf(e)&&this.pluginCallbacks.push(e),this}unregister(e){return -1!==this.pluginCallbacks.indexOf(e)&&this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(e),1),this}parse(e,t,r,n){let i,a={},o={};if("string"==typeof e)i=JSON.parse(e);else if(e instanceof ArrayBuffer)if(u(new Uint8Array(e.slice(0,4)))===P){try{a[A.KHR_BINARY_GLTF]=new H(e)}catch(e){n&&n(e);return}i=JSON.parse(a[A.KHR_BINARY_GLTF].content)}else i=JSON.parse(u(new Uint8Array(e)));else i=e;if(void 0===i.asset||i.asset.version[0]<2){n&&n(Error("THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported."));return}let s=new en(i,{path:t||this.resourcePath||"",crossOrigin:this.crossOrigin,requestHeader:this.requestHeader,manager:this.manager,ktx2Loader:this.ktx2Loader,meshoptDecoder:this.meshoptDecoder});s.fileLoader.setRequestHeader(this.requestHeader);for(let e=0;e<this.pluginCallbacks.length;e++){let t=this.pluginCallbacks[e](s);t.name||console.error("THREE.GLTFLoader: Invalid plugin found: missing name"),o[t.name]=t,a[t.name]=!0}if(i.extensionsUsed)for(let e=0;e<i.extensionsUsed.length;++e){let t=i.extensionsUsed[e],r=i.extensionsRequired||[];switch(t){case A.KHR_MATERIALS_UNLIT:a[t]=new v;break;case A.KHR_DRACO_MESH_COMPRESSION:a[t]=new O(i,this.dracoLoader);break;case A.KHR_TEXTURE_TRANSFORM:a[t]=new L;break;case A.KHR_MESH_QUANTIZATION:a[t]=new U;break;default:r.indexOf(t)>=0&&void 0===o[t]&&console.warn('THREE.GLTFLoader: Unknown extension "'+t+'".')}}s.setExtensions(a),s.setPlugins(o),s.parse(r,n)}parseAsync(e,t){let r=this;return new Promise(function(n,i){r.parse(e,t,n,i)})}}function p(){let e={};return{get:function(t){return e[t]},add:function(t,r){e[t]=r},remove:function(t){delete e[t]},removeAll:function(){e={}}}}let A={KHR_BINARY_GLTF:"KHR_binary_glTF",KHR_DRACO_MESH_COMPRESSION:"KHR_draco_mesh_compression",KHR_LIGHTS_PUNCTUAL:"KHR_lights_punctual",KHR_MATERIALS_CLEARCOAT:"KHR_materials_clearcoat",KHR_MATERIALS_DISPERSION:"KHR_materials_dispersion",KHR_MATERIALS_IOR:"KHR_materials_ior",KHR_MATERIALS_SHEEN:"KHR_materials_sheen",KHR_MATERIALS_SPECULAR:"KHR_materials_specular",KHR_MATERIALS_TRANSMISSION:"KHR_materials_transmission",KHR_MATERIALS_IRIDESCENCE:"KHR_materials_iridescence",KHR_MATERIALS_ANISOTROPY:"KHR_materials_anisotropy",KHR_MATERIALS_UNLIT:"KHR_materials_unlit",KHR_MATERIALS_VOLUME:"KHR_materials_volume",KHR_TEXTURE_BASISU:"KHR_texture_basisu",KHR_TEXTURE_TRANSFORM:"KHR_texture_transform",KHR_MESH_QUANTIZATION:"KHR_mesh_quantization",KHR_MATERIALS_EMISSIVE_STRENGTH:"KHR_materials_emissive_strength",EXT_MATERIALS_BUMP:"EXT_materials_bump",EXT_TEXTURE_WEBP:"EXT_texture_webp",EXT_TEXTURE_AVIF:"EXT_texture_avif",EXT_MESHOPT_COMPRESSION:"EXT_meshopt_compression",EXT_MESH_GPU_INSTANCING:"EXT_mesh_gpu_instancing"};class m{constructor(e){this.parser=e,this.name=A.KHR_LIGHTS_PUNCTUAL,this.cache={refs:{},uses:{}}}_markDefs(){let e=this.parser,t=this.parser.json.nodes||[];for(let r=0,n=t.length;r<n;r++){let n=t[r];n.extensions&&n.extensions[this.name]&&void 0!==n.extensions[this.name].light&&e._addNodeRef(this.cache,n.extensions[this.name].light)}}_loadLight(e){let t,r=this.parser,n="light:"+e,a=r.cache.get(n);if(a)return a;let o=r.json,s=((o.extensions&&o.extensions[this.name]||{}).lights||[])[e],l=new i.Q1f(0xffffff);void 0!==s.color&&l.setRGB(s.color[0],s.color[1],s.color[2],f);let c=void 0!==s.range?s.range:0;switch(s.type){case"directional":(t=new i.ZyN(l)).target.position.set(0,0,-1),t.add(t.target);break;case"point":(t=new i.HiM(l)).distance=c;break;case"spot":(t=new i.nCl(l)).distance=c,s.spot=s.spot||{},s.spot.innerConeAngle=void 0!==s.spot.innerConeAngle?s.spot.innerConeAngle:0,s.spot.outerConeAngle=void 0!==s.spot.outerConeAngle?s.spot.outerConeAngle:Math.PI/4,t.angle=s.spot.outerConeAngle,t.penumbra=1-s.spot.innerConeAngle/s.spot.outerConeAngle,t.target.position.set(0,0,-1),t.add(t.target);break;default:throw Error("THREE.GLTFLoader: Unexpected light type: "+s.type)}return t.position.set(0,0,0),t.decay=2,$(t,s),void 0!==s.intensity&&(t.intensity=s.intensity),t.name=r.createUniqueName(s.name||"light_"+e),a=Promise.resolve(t),r.cache.add(n,a),a}getDependency(e,t){if("light"===e)return this._loadLight(t)}createNodeAttachment(e){let t=this,r=this.parser,n=r.json.nodes[e],i=(n.extensions&&n.extensions[this.name]||{}).light;return void 0===i?null:this._loadLight(i).then(function(e){return r._getNodeRef(t.cache,i,e)})}}class v{constructor(){this.name=A.KHR_MATERIALS_UNLIT}getMaterialType(){return i.V9B}extendParams(e,t,r){let n=[];e.color=new i.Q1f(1,1,1),e.opacity=1;let a=t.pbrMetallicRoughness;if(a){if(Array.isArray(a.baseColorFactor)){let t=a.baseColorFactor;e.color.setRGB(t[0],t[1],t[2],f),e.opacity=t[3]}void 0!==a.baseColorTexture&&n.push(r.assignTexture(e,"map",a.baseColorTexture,d))}return Promise.all(n)}}class g{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_EMISSIVE_STRENGTH}extendMaterialParams(e,t){let r=this.parser.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let n=r.extensions[this.name].emissiveStrength;return void 0!==n&&(t.emissiveIntensity=n),Promise.resolve()}}class B{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_CLEARCOAT}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser,n=r.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let a=[],o=n.extensions[this.name];if(void 0!==o.clearcoatFactor&&(t.clearcoat=o.clearcoatFactor),void 0!==o.clearcoatTexture&&a.push(r.assignTexture(t,"clearcoatMap",o.clearcoatTexture)),void 0!==o.clearcoatRoughnessFactor&&(t.clearcoatRoughness=o.clearcoatRoughnessFactor),void 0!==o.clearcoatRoughnessTexture&&a.push(r.assignTexture(t,"clearcoatRoughnessMap",o.clearcoatRoughnessTexture)),void 0!==o.clearcoatNormalTexture&&(a.push(r.assignTexture(t,"clearcoatNormalMap",o.clearcoatNormalTexture)),void 0!==o.clearcoatNormalTexture.scale)){let e=o.clearcoatNormalTexture.scale;t.clearcoatNormalScale=new i.I9Y(e,e)}return Promise.all(a)}}class C{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_DISPERSION}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let n=r.extensions[this.name];return t.dispersion=void 0!==n.dispersion?n.dispersion:0,Promise.resolve()}}class b{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_IRIDESCENCE}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser,n=r.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let i=[],a=n.extensions[this.name];return void 0!==a.iridescenceFactor&&(t.iridescence=a.iridescenceFactor),void 0!==a.iridescenceTexture&&i.push(r.assignTexture(t,"iridescenceMap",a.iridescenceTexture)),void 0!==a.iridescenceIor&&(t.iridescenceIOR=a.iridescenceIor),void 0===t.iridescenceThicknessRange&&(t.iridescenceThicknessRange=[100,400]),void 0!==a.iridescenceThicknessMinimum&&(t.iridescenceThicknessRange[0]=a.iridescenceThicknessMinimum),void 0!==a.iridescenceThicknessMaximum&&(t.iridescenceThicknessRange[1]=a.iridescenceThicknessMaximum),void 0!==a.iridescenceThicknessTexture&&i.push(r.assignTexture(t,"iridescenceThicknessMap",a.iridescenceThicknessTexture)),Promise.all(i)}}class y{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_SHEEN}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser,n=r.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let a=[];t.sheenColor=new i.Q1f(0,0,0),t.sheenRoughness=0,t.sheen=1;let o=n.extensions[this.name];if(void 0!==o.sheenColorFactor){let e=o.sheenColorFactor;t.sheenColor.setRGB(e[0],e[1],e[2],f)}return void 0!==o.sheenRoughnessFactor&&(t.sheenRoughness=o.sheenRoughnessFactor),void 0!==o.sheenColorTexture&&a.push(r.assignTexture(t,"sheenColorMap",o.sheenColorTexture,d)),void 0!==o.sheenRoughnessTexture&&a.push(r.assignTexture(t,"sheenRoughnessMap",o.sheenRoughnessTexture)),Promise.all(a)}}class E{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_TRANSMISSION}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser,n=r.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let i=[],a=n.extensions[this.name];return void 0!==a.transmissionFactor&&(t.transmission=a.transmissionFactor),void 0!==a.transmissionTexture&&i.push(r.assignTexture(t,"transmissionMap",a.transmissionTexture)),Promise.all(i)}}class M{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_VOLUME}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser,n=r.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let a=[],o=n.extensions[this.name];t.thickness=void 0!==o.thicknessFactor?o.thicknessFactor:0,void 0!==o.thicknessTexture&&a.push(r.assignTexture(t,"thicknessMap",o.thicknessTexture)),t.attenuationDistance=o.attenuationDistance||1/0;let s=o.attenuationColor||[1,1,1];return t.attenuationColor=new i.Q1f().setRGB(s[0],s[1],s[2],f),Promise.all(a)}}class w{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_IOR}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let n=r.extensions[this.name];return t.ior=void 0!==n.ior?n.ior:1.5,Promise.resolve()}}class x{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_SPECULAR}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser,n=r.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let a=[],o=n.extensions[this.name];t.specularIntensity=void 0!==o.specularFactor?o.specularFactor:1,void 0!==o.specularTexture&&a.push(r.assignTexture(t,"specularIntensityMap",o.specularTexture));let s=o.specularColorFactor||[1,1,1];return t.specularColor=new i.Q1f().setRGB(s[0],s[1],s[2],f),void 0!==o.specularColorTexture&&a.push(r.assignTexture(t,"specularColorMap",o.specularColorTexture,d)),Promise.all(a)}}class I{constructor(e){this.parser=e,this.name=A.EXT_MATERIALS_BUMP}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser,n=r.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let i=[],a=n.extensions[this.name];return t.bumpScale=void 0!==a.bumpFactor?a.bumpFactor:1,void 0!==a.bumpTexture&&i.push(r.assignTexture(t,"bumpMap",a.bumpTexture)),Promise.all(i)}}class R{constructor(e){this.parser=e,this.name=A.KHR_MATERIALS_ANISOTROPY}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let r=this.parser,n=r.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let i=[],a=n.extensions[this.name];return void 0!==a.anisotropyStrength&&(t.anisotropy=a.anisotropyStrength),void 0!==a.anisotropyRotation&&(t.anisotropyRotation=a.anisotropyRotation),void 0!==a.anisotropyTexture&&i.push(r.assignTexture(t,"anisotropyMap",a.anisotropyTexture)),Promise.all(i)}}class F{constructor(e){this.parser=e,this.name=A.KHR_TEXTURE_BASISU}loadTexture(e){let t=this.parser,r=t.json,n=r.textures[e];if(!n.extensions||!n.extensions[this.name])return null;let i=n.extensions[this.name],a=t.options.ktx2Loader;if(!a)if(!(r.extensionsRequired&&r.extensionsRequired.indexOf(this.name)>=0))return null;else throw Error("THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures");return t.loadTextureImage(e,i.source,a)}}class D{constructor(e){this.parser=e,this.name=A.EXT_TEXTURE_WEBP,this.isSupported=null}loadTexture(e){let t=this.name,r=this.parser,n=r.json,i=n.textures[e];if(!i.extensions||!i.extensions[t])return null;let a=i.extensions[t],o=n.images[a.source],s=r.textureLoader;if(o.uri){let e=r.options.manager.getHandler(o.uri);null!==e&&(s=e)}return this.detectSupport().then(function(i){if(i)return r.loadTextureImage(e,a.source,s);if(n.extensionsRequired&&n.extensionsRequired.indexOf(t)>=0)throw Error("THREE.GLTFLoader: WebP required by asset but unsupported.");return r.loadTexture(e)})}detectSupport(){return this.isSupported||(this.isSupported=new Promise(function(e){let t=new Image;t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",t.onload=t.onerror=function(){e(1===t.height)}})),this.isSupported}}class T{constructor(e){this.parser=e,this.name=A.EXT_TEXTURE_AVIF,this.isSupported=null}loadTexture(e){let t=this.name,r=this.parser,n=r.json,i=n.textures[e];if(!i.extensions||!i.extensions[t])return null;let a=i.extensions[t],o=n.images[a.source],s=r.textureLoader;if(o.uri){let e=r.options.manager.getHandler(o.uri);null!==e&&(s=e)}return this.detectSupport().then(function(i){if(i)return r.loadTextureImage(e,a.source,s);if(n.extensionsRequired&&n.extensionsRequired.indexOf(t)>=0)throw Error("THREE.GLTFLoader: AVIF required by asset but unsupported.");return r.loadTexture(e)})}detectSupport(){return this.isSupported||(this.isSupported=new Promise(function(e){let t=new Image;t.src="data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI=",t.onload=t.onerror=function(){e(1===t.height)}})),this.isSupported}}class G{constructor(e){this.name=A.EXT_MESHOPT_COMPRESSION,this.parser=e}loadBufferView(e){let t=this.parser.json,r=t.bufferViews[e];if(!r.extensions||!r.extensions[this.name])return null;{let e=r.extensions[this.name],n=this.parser.getDependency("buffer",e.buffer),i=this.parser.options.meshoptDecoder;if(!i||!i.supported)if(!(t.extensionsRequired&&t.extensionsRequired.indexOf(this.name)>=0))return null;else throw Error("THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files");return n.then(function(t){let r=e.byteOffset||0,n=e.byteLength||0,a=e.count,o=e.byteStride,s=new Uint8Array(t,r,n);return i.decodeGltfBufferAsync?i.decodeGltfBufferAsync(a,o,s,e.mode,e.filter).then(function(e){return e.buffer}):i.ready.then(function(){let t=new ArrayBuffer(a*o);return i.decodeGltfBuffer(new Uint8Array(t),a,o,s,e.mode,e.filter),t})})}}}class S{constructor(e){this.name=A.EXT_MESH_GPU_INSTANCING,this.parser=e}createNodeMesh(e){let t=this.parser.json,r=t.nodes[e];if(!r.extensions||!r.extensions[this.name]||void 0===r.mesh)return null;for(let e of t.meshes[r.mesh].primitives)if(e.mode!==N.TRIANGLES&&e.mode!==N.TRIANGLE_STRIP&&e.mode!==N.TRIANGLE_FAN&&void 0!==e.mode)return null;let n=r.extensions[this.name].attributes,a=[],o={};for(let e in n)a.push(this.parser.getDependency("accessor",n[e]).then(t=>(o[e]=t,o[e])));return a.length<1?null:(a.push(this.parser.createNodeMesh(e)),Promise.all(a).then(e=>{let t=e.pop(),r=t.isGroup?t.children:[t],n=e[0].count,a=[];for(let e of r){let t=new i.kn4,r=new i.Pq0,s=new i.PTz,l=new i.Pq0(1,1,1),c=new i.ZLX(e.geometry,e.material,n);for(let e=0;e<n;e++)o.TRANSLATION&&r.fromBufferAttribute(o.TRANSLATION,e),o.ROTATION&&s.fromBufferAttribute(o.ROTATION,e),o.SCALE&&l.fromBufferAttribute(o.SCALE,e),c.setMatrixAt(e,t.compose(r,s,l));for(let t in o)if("_COLOR_0"===t){let e=o[t];c.instanceColor=new i.uWO(e.array,e.itemSize,e.normalized)}else"TRANSLATION"!==t&&"ROTATION"!==t&&"SCALE"!==t&&e.geometry.setAttribute(t,o[t]);i.B69.prototype.copy.call(c,e),this.parser.assignFinalMaterial(c),a.push(c)}return t.isGroup?(t.clear(),t.add(...a),t):a[0]}))}}let P="glTF",_={JSON:0x4e4f534a,BIN:5130562};class H{constructor(e){this.name=A.KHR_BINARY_GLTF,this.content=null,this.body=null;let t=new DataView(e,0,12);if(this.header={magic:u(new Uint8Array(e.slice(0,4))),version:t.getUint32(4,!0),length:t.getUint32(8,!0)},this.header.magic!==P)throw Error("THREE.GLTFLoader: Unsupported glTF-Binary header.");if(this.header.version<2)throw Error("THREE.GLTFLoader: Legacy binary file detected.");let r=this.header.length-12,n=new DataView(e,12),i=0;for(;i<r;){let t=n.getUint32(i,!0);i+=4;let r=n.getUint32(i,!0);if(i+=4,r===_.JSON){let r=new Uint8Array(e,12+i,t);this.content=u(r)}else if(r===_.BIN){let r=12+i;this.body=e.slice(r,r+t)}i+=t}if(null===this.content)throw Error("THREE.GLTFLoader: JSON content not found.")}}class O{constructor(e,t){if(!t)throw Error("THREE.GLTFLoader: No DRACOLoader instance provided.");this.name=A.KHR_DRACO_MESH_COMPRESSION,this.json=e,this.dracoLoader=t,this.dracoLoader.preload()}decodePrimitive(e,t){let r=this.json,n=this.dracoLoader,i=e.extensions[this.name].bufferView,a=e.extensions[this.name].attributes,o={},s={},l={};for(let e in a)o[W[e]||e.toLowerCase()]=a[e];for(let t in e.attributes){let n=W[t]||t.toLowerCase();if(void 0!==a[t]){let i=r.accessors[e.attributes[t]],a=K[i.componentType];l[n]=a.name,s[n]=!0===i.normalized}}return t.getDependency("bufferView",i).then(function(e){return new Promise(function(t,r){n.decodeDracoFile(e,function(e){for(let t in e.attributes){let r=e.attributes[t],n=s[t];void 0!==n&&(r.normalized=n)}t(e)},o,l,f,r)})})}}class L{constructor(){this.name=A.KHR_TEXTURE_TRANSFORM}extendTexture(e,t){return(void 0===t.texCoord||t.texCoord===e.channel)&&void 0===t.offset&&void 0===t.rotation&&void 0===t.scale||(e=e.clone(),void 0!==t.texCoord&&(e.channel=t.texCoord),void 0!==t.offset&&e.offset.fromArray(t.offset),void 0!==t.rotation&&(e.rotation=t.rotation),void 0!==t.scale&&e.repeat.fromArray(t.scale),e.needsUpdate=!0),e}}class U{constructor(){this.name=A.KHR_MESH_QUANTIZATION}}class k extends i.lGw{constructor(e,t,r,n){super(e,t,r,n)}copySampleValue_(e){let t=this.resultBuffer,r=this.sampleValues,n=this.valueSize,i=e*n*3+n;for(let e=0;e!==n;e++)t[e]=r[i+e];return t}interpolate_(e,t,r,n){let i=this.resultBuffer,a=this.sampleValues,o=this.valueSize,s=2*o,l=3*o,c=n-t,u=(r-t)/c,d=u*u,f=d*u,h=e*l,p=h-l,A=-2*f+3*d,m=f-d,v=1-A,g=m-d+u;for(let e=0;e!==o;e++){let t=a[p+e+o],r=a[p+e+s]*c,n=a[h+e+o],l=a[h+e]*c;i[e]=v*t+g*r+A*n+m*l}return i}}let J=new i.PTz;class j extends k{interpolate_(e,t,r,n){let i=super.interpolate_(e,t,r,n);return J.fromArray(i).normalize().toArray(i),i}}let N={POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6},K={5120:Int8Array,5121:Uint8Array,5122:Int16Array,5123:Uint16Array,5125:Uint32Array,5126:Float32Array},Q={9728:i.hxR,9729:i.k6q,9984:i.pHI,9985:i.kRr,9986:i.Cfg,9987:i.$_I},X={33071:i.ghU,33648:i.kTW,10497:i.GJx},Y={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},W={POSITION:"position",NORMAL:"normal",TANGENT:"tangent",...c.r>=152?{TEXCOORD_0:"uv",TEXCOORD_1:"uv1",TEXCOORD_2:"uv2",TEXCOORD_3:"uv3"}:{TEXCOORD_0:"uv",TEXCOORD_1:"uv2"},COLOR_0:"color",WEIGHTS_0:"skinWeight",JOINTS_0:"skinIndex"},z={scale:"scale",translation:"position",rotation:"quaternion",weights:"morphTargetInfluences"},Z={CUBICSPLINE:void 0,LINEAR:i.PJ3,STEP:i.ljd},q={OPAQUE:"OPAQUE",MASK:"MASK",BLEND:"BLEND"};function V(e,t,r){for(let n in r.extensions)void 0===e[n]&&(t.userData.gltfExtensions=t.userData.gltfExtensions||{},t.userData.gltfExtensions[n]=r.extensions[n])}function $(e,t){void 0!==t.extras&&("object"==typeof t.extras?Object.assign(e.userData,t.extras):console.warn("THREE.GLTFLoader: Ignoring primitive type .extras, "+t.extras))}function ee(e){let t="",r=Object.keys(e).sort();for(let n=0,i=r.length;n<i;n++)t+=r[n]+":"+e[r[n]]+";";return t}function et(e){switch(e){case Int8Array:return 1/127;case Uint8Array:return 1/255;case Int16Array:return 1/32767;case Uint16Array:return 1/65535;default:throw Error("THREE.GLTFLoader: Unsupported normalized accessor component type.")}}let er=new i.kn4;class en{constructor(e={},t={}){this.json=e,this.extensions={},this.plugins={},this.options=t,this.cache=new p,this.associations=new Map,this.primitiveCache={},this.nodeCache={},this.meshCache={refs:{},uses:{}},this.cameraCache={refs:{},uses:{}},this.lightCache={refs:{},uses:{}},this.sourceCache={},this.textureCache={},this.nodeNamesUsed={};let r=!1,n=!1,a=-1;"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(r=!0===/^((?!chrome|android).)*safari/i.test(navigator.userAgent),a=(n=navigator.userAgent.indexOf("Firefox")>-1)?navigator.userAgent.match(/Firefox\/([0-9]+)\./)[1]:-1),"undefined"==typeof createImageBitmap||r||n&&a<98?this.textureLoader=new i.Tap(this.options.manager):this.textureLoader=new i.Kzg(this.options.manager),this.textureLoader.setCrossOrigin(this.options.crossOrigin),this.textureLoader.setRequestHeader(this.options.requestHeader),this.fileLoader=new i.Y9S(this.options.manager),this.fileLoader.setResponseType("arraybuffer"),"use-credentials"===this.options.crossOrigin&&this.fileLoader.setWithCredentials(!0)}setExtensions(e){this.extensions=e}setPlugins(e){this.plugins=e}parse(e,t){let r=this,n=this.json,i=this.extensions;this.cache.removeAll(),this.nodeCache={},this._invokeAll(function(e){return e._markDefs&&e._markDefs()}),Promise.all(this._invokeAll(function(e){return e.beforeRoot&&e.beforeRoot()})).then(function(){return Promise.all([r.getDependencies("scene"),r.getDependencies("animation"),r.getDependencies("camera")])}).then(function(t){let a={scene:t[0][n.scene||0],scenes:t[0],animations:t[1],cameras:t[2],asset:n.asset,parser:r,userData:{}};return V(i,a,n),$(a,n),Promise.all(r._invokeAll(function(e){return e.afterRoot&&e.afterRoot(a)})).then(function(){for(let e of a.scenes)e.updateMatrixWorld();e(a)})}).catch(t)}_markDefs(){let e=this.json.nodes||[],t=this.json.skins||[],r=this.json.meshes||[];for(let r=0,n=t.length;r<n;r++){let n=t[r].joints;for(let t=0,r=n.length;t<r;t++)e[n[t]].isBone=!0}for(let t=0,n=e.length;t<n;t++){let n=e[t];void 0!==n.mesh&&(this._addNodeRef(this.meshCache,n.mesh),void 0!==n.skin&&(r[n.mesh].isSkinnedMesh=!0)),void 0!==n.camera&&this._addNodeRef(this.cameraCache,n.camera)}}_addNodeRef(e,t){void 0!==t&&(void 0===e.refs[t]&&(e.refs[t]=e.uses[t]=0),e.refs[t]++)}_getNodeRef(e,t,r){if(e.refs[t]<=1)return r;let n=r.clone(),i=(e,t)=>{let r=this.associations.get(e);for(let[n,a]of(null!=r&&this.associations.set(t,r),e.children.entries()))i(a,t.children[n])};return i(r,n),n.name+="_instance_"+e.uses[t]++,n}_invokeOne(e){let t=Object.values(this.plugins);t.push(this);for(let r=0;r<t.length;r++){let n=e(t[r]);if(n)return n}return null}_invokeAll(e){let t=Object.values(this.plugins);t.unshift(this);let r=[];for(let n=0;n<t.length;n++){let i=e(t[n]);i&&r.push(i)}return r}getDependency(e,t){let r=e+":"+t,n=this.cache.get(r);if(!n){switch(e){case"scene":n=this.loadScene(t);break;case"node":n=this._invokeOne(function(e){return e.loadNode&&e.loadNode(t)});break;case"mesh":n=this._invokeOne(function(e){return e.loadMesh&&e.loadMesh(t)});break;case"accessor":n=this.loadAccessor(t);break;case"bufferView":n=this._invokeOne(function(e){return e.loadBufferView&&e.loadBufferView(t)});break;case"buffer":n=this.loadBuffer(t);break;case"material":n=this._invokeOne(function(e){return e.loadMaterial&&e.loadMaterial(t)});break;case"texture":n=this._invokeOne(function(e){return e.loadTexture&&e.loadTexture(t)});break;case"skin":n=this.loadSkin(t);break;case"animation":n=this._invokeOne(function(e){return e.loadAnimation&&e.loadAnimation(t)});break;case"camera":n=this.loadCamera(t);break;default:if(!(n=this._invokeOne(function(r){return r!=this&&r.getDependency&&r.getDependency(e,t)})))throw Error("Unknown type: "+e)}this.cache.add(r,n)}return n}getDependencies(e){let t=this.cache.get(e);if(!t){let r=this;t=Promise.all((this.json[e+("mesh"===e?"es":"s")]||[]).map(function(t,n){return r.getDependency(e,n)})),this.cache.add(e,t)}return t}loadBuffer(e){let t=this.json.buffers[e],r=this.fileLoader;if(t.type&&"arraybuffer"!==t.type)throw Error("THREE.GLTFLoader: "+t.type+" buffer type is not supported.");if(void 0===t.uri&&0===e)return Promise.resolve(this.extensions[A.KHR_BINARY_GLTF].body);let n=this.options;return new Promise(function(e,a){r.load(i.r6x.resolveURL(t.uri,n.path),e,void 0,function(){a(Error('THREE.GLTFLoader: Failed to load buffer "'+t.uri+'".'))})})}loadBufferView(e){let t=this.json.bufferViews[e];return this.getDependency("buffer",t.buffer).then(function(e){let r=t.byteLength||0,n=t.byteOffset||0;return e.slice(n,n+r)})}loadAccessor(e){let t=this,r=this.json,n=this.json.accessors[e];if(void 0===n.bufferView&&void 0===n.sparse){let e=Y[n.type],t=K[n.componentType],r=!0===n.normalized,a=new t(n.count*e);return Promise.resolve(new i.THS(a,e,r))}let a=[];return void 0!==n.bufferView?a.push(this.getDependency("bufferView",n.bufferView)):a.push(null),void 0!==n.sparse&&(a.push(this.getDependency("bufferView",n.sparse.indices.bufferView)),a.push(this.getDependency("bufferView",n.sparse.values.bufferView))),Promise.all(a).then(function(e){let a,o,s=e[0],l=Y[n.type],c=K[n.componentType],u=c.BYTES_PER_ELEMENT,d=u*l,f=n.byteOffset||0,h=void 0!==n.bufferView?r.bufferViews[n.bufferView].byteStride:void 0,p=!0===n.normalized;if(h&&h!==d){let e=Math.floor(f/h),r="InterleavedBuffer:"+n.bufferView+":"+n.componentType+":"+e+":"+n.count,d=t.cache.get(r);d||(a=new c(s,e*h,n.count*h/u),d=new i.eB$(a,h/u),t.cache.add(r,d)),o=new i.eHs(d,l,f%h/u,p)}else a=null===s?new c(n.count*l):new c(s,f,n.count*l),o=new i.THS(a,l,p);if(void 0!==n.sparse){let t=Y.SCALAR,r=K[n.sparse.indices.componentType],a=n.sparse.indices.byteOffset||0,u=n.sparse.values.byteOffset||0,d=new r(e[1],a,n.sparse.count*t),f=new c(e[2],u,n.sparse.count*l);null!==s&&(o=new i.THS(o.array.slice(),o.itemSize,o.normalized));for(let e=0,t=d.length;e<t;e++){let t=d[e];if(o.setX(t,f[e*l]),l>=2&&o.setY(t,f[e*l+1]),l>=3&&o.setZ(t,f[e*l+2]),l>=4&&o.setW(t,f[e*l+3]),l>=5)throw Error("THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.")}}return o})}loadTexture(e){let t=this.json,r=this.options,n=t.textures[e].source,i=t.images[n],a=this.textureLoader;if(i.uri){let e=r.manager.getHandler(i.uri);null!==e&&(a=e)}return this.loadTextureImage(e,n,a)}loadTextureImage(e,t,r){let n=this,a=this.json,o=a.textures[e],s=a.images[t],l=(s.uri||s.bufferView)+":"+o.sampler;if(this.textureCache[l])return this.textureCache[l];let c=this.loadImageSource(t,r).then(function(t){t.flipY=!1,t.name=o.name||s.name||"",""===t.name&&"string"==typeof s.uri&&!1===s.uri.startsWith("data:image/")&&(t.name=s.uri);let r=(a.samplers||{})[o.sampler]||{};return t.magFilter=Q[r.magFilter]||i.k6q,t.minFilter=Q[r.minFilter]||i.$_I,t.wrapS=X[r.wrapS]||i.GJx,t.wrapT=X[r.wrapT]||i.GJx,n.associations.set(t,{textures:e}),t}).catch(function(){return null});return this.textureCache[l]=c,c}loadImageSource(e,t){let r=this.json,n=this.options;if(void 0!==this.sourceCache[e])return this.sourceCache[e].then(e=>e.clone());let a=r.images[e],o=self.URL||self.webkitURL,s=a.uri||"",l=!1;if(void 0!==a.bufferView)s=this.getDependency("bufferView",a.bufferView).then(function(e){l=!0;let t=new Blob([e],{type:a.mimeType});return s=o.createObjectURL(t)});else if(void 0===a.uri)throw Error("THREE.GLTFLoader: Image "+e+" is missing URI and bufferView");let c=Promise.resolve(s).then(function(e){return new Promise(function(r,a){let o=r;!0===t.isImageBitmapLoader&&(o=function(e){let t=new i.gPd(e);t.needsUpdate=!0,r(t)}),t.load(i.r6x.resolveURL(e,n.path),o,void 0,a)})}).then(function(e){var t;return!0===l&&o.revokeObjectURL(s),$(e,a),e.userData.mimeType=a.mimeType||((t=a.uri).search(/\.jpe?g($|\?)/i)>0||0===t.search(/^data\:image\/jpeg/)?"image/jpeg":t.search(/\.webp($|\?)/i)>0||0===t.search(/^data\:image\/webp/)?"image/webp":"image/png"),e}).catch(function(e){throw console.error("THREE.GLTFLoader: Couldn't load texture",s),e});return this.sourceCache[e]=c,c}assignTexture(e,t,r,n){let i=this;return this.getDependency("texture",r.index).then(function(a){if(!a)return null;if(void 0!==r.texCoord&&r.texCoord>0&&((a=a.clone()).channel=r.texCoord),i.extensions[A.KHR_TEXTURE_TRANSFORM]){let e=void 0!==r.extensions?r.extensions[A.KHR_TEXTURE_TRANSFORM]:void 0;if(e){let t=i.associations.get(a);a=i.extensions[A.KHR_TEXTURE_TRANSFORM].extendTexture(a,e),i.associations.set(a,t)}}return void 0!==n&&("number"==typeof n&&(n=3001===n?d:f),"colorSpace"in a?a.colorSpace=n:a.encoding=n===d?3001:3e3),e[t]=a,a})}assignFinalMaterial(e){let t=e.geometry,r=e.material,n=void 0===t.attributes.tangent,a=void 0!==t.attributes.color,o=void 0===t.attributes.normal;if(e.isPoints){let e="PointsMaterial:"+r.uuid,t=this.cache.get(e);t||(t=new i.BH$,i.imn.prototype.copy.call(t,r),t.color.copy(r.color),t.map=r.map,t.sizeAttenuation=!1,this.cache.add(e,t)),r=t}else if(e.isLine){let e="LineBasicMaterial:"+r.uuid,t=this.cache.get(e);t||(t=new i.mrM,i.imn.prototype.copy.call(t,r),t.color.copy(r.color),t.map=r.map,this.cache.add(e,t)),r=t}if(n||a||o){let e="ClonedMaterial:"+r.uuid+":";n&&(e+="derivative-tangents:"),a&&(e+="vertex-colors:"),o&&(e+="flat-shading:");let t=this.cache.get(e);t||(t=r.clone(),a&&(t.vertexColors=!0),o&&(t.flatShading=!0),n&&(t.normalScale&&(t.normalScale.y*=-1),t.clearcoatNormalScale&&(t.clearcoatNormalScale.y*=-1)),this.cache.add(e,t),this.associations.set(t,this.associations.get(r))),r=t}e.material=r}getMaterialType(){return i._4j}loadMaterial(e){let t,r=this,n=this.json,a=this.extensions,o=n.materials[e],s={},l=o.extensions||{},c=[];if(l[A.KHR_MATERIALS_UNLIT]){let e=a[A.KHR_MATERIALS_UNLIT];t=e.getMaterialType(),c.push(e.extendParams(s,o,r))}else{let n=o.pbrMetallicRoughness||{};if(s.color=new i.Q1f(1,1,1),s.opacity=1,Array.isArray(n.baseColorFactor)){let e=n.baseColorFactor;s.color.setRGB(e[0],e[1],e[2],f),s.opacity=e[3]}void 0!==n.baseColorTexture&&c.push(r.assignTexture(s,"map",n.baseColorTexture,d)),s.metalness=void 0!==n.metallicFactor?n.metallicFactor:1,s.roughness=void 0!==n.roughnessFactor?n.roughnessFactor:1,void 0!==n.metallicRoughnessTexture&&(c.push(r.assignTexture(s,"metalnessMap",n.metallicRoughnessTexture)),c.push(r.assignTexture(s,"roughnessMap",n.metallicRoughnessTexture))),t=this._invokeOne(function(t){return t.getMaterialType&&t.getMaterialType(e)}),c.push(Promise.all(this._invokeAll(function(t){return t.extendMaterialParams&&t.extendMaterialParams(e,s)})))}!0===o.doubleSided&&(s.side=i.$EB);let u=o.alphaMode||q.OPAQUE;if(u===q.BLEND?(s.transparent=!0,s.depthWrite=!1):(s.transparent=!1,u===q.MASK&&(s.alphaTest=void 0!==o.alphaCutoff?o.alphaCutoff:.5)),void 0!==o.normalTexture&&t!==i.V9B&&(c.push(r.assignTexture(s,"normalMap",o.normalTexture)),s.normalScale=new i.I9Y(1,1),void 0!==o.normalTexture.scale)){let e=o.normalTexture.scale;s.normalScale.set(e,e)}if(void 0!==o.occlusionTexture&&t!==i.V9B&&(c.push(r.assignTexture(s,"aoMap",o.occlusionTexture)),void 0!==o.occlusionTexture.strength&&(s.aoMapIntensity=o.occlusionTexture.strength)),void 0!==o.emissiveFactor&&t!==i.V9B){let e=o.emissiveFactor;s.emissive=new i.Q1f().setRGB(e[0],e[1],e[2],f)}return void 0!==o.emissiveTexture&&t!==i.V9B&&c.push(r.assignTexture(s,"emissiveMap",o.emissiveTexture,d)),Promise.all(c).then(function(){let n=new t(s);return o.name&&(n.name=o.name),$(n,o),r.associations.set(n,{materials:e}),o.extensions&&V(a,n,o),n})}createUniqueName(e){let t=i.Nwf.sanitizeNodeName(e||"");return t in this.nodeNamesUsed?t+"_"+ ++this.nodeNamesUsed[t]:(this.nodeNamesUsed[t]=0,t)}loadGeometries(e){let t=this,r=this.extensions,n=this.primitiveCache,a=[];for(let o=0,s=e.length;o<s;o++){let s=e[o],l=function(e){let t,r=e.extensions&&e.extensions[A.KHR_DRACO_MESH_COMPRESSION];if(t=r?"draco:"+r.bufferView+":"+r.indices+":"+ee(r.attributes):e.indices+":"+ee(e.attributes)+":"+e.mode,void 0!==e.targets)for(let r=0,n=e.targets.length;r<n;r++)t+=":"+ee(e.targets[r]);return t}(s),c=n[l];if(c)a.push(c.promise);else{let e;e=s.extensions&&s.extensions[A.KHR_DRACO_MESH_COMPRESSION]?function(e){return r[A.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(e,t).then(function(r){return ei(r,e,t)})}(s):ei(new i.LoY,s,t),n[l]={primitive:s,promise:e},a.push(e)}}return Promise.all(a)}loadMesh(e){let t=this,r=this.json,n=this.extensions,a=r.meshes[e],o=a.primitives,s=[];for(let e=0,t=o.length;e<t;e++){var c;let t=void 0===o[e].material?(void 0===(c=this.cache).DefaultMaterial&&(c.DefaultMaterial=new i._4j({color:0xffffff,emissive:0,metalness:1,roughness:1,transparent:!1,depthTest:!0,side:i.hB5})),c.DefaultMaterial):this.getDependency("material",o[e].material);s.push(t)}return s.push(t.loadGeometries(o)),Promise.all(s).then(function(r){let s=r.slice(0,r.length-1),c=r[r.length-1],u=[];for(let r=0,d=c.length;r<d;r++){let d,f=c[r],h=o[r],p=s[r];if(h.mode===N.TRIANGLES||h.mode===N.TRIANGLE_STRIP||h.mode===N.TRIANGLE_FAN||void 0===h.mode)!0===(d=!0===a.isSkinnedMesh?new i.I46(f,p):new i.eaF(f,p)).isSkinnedMesh&&d.normalizeSkinWeights(),h.mode===N.TRIANGLE_STRIP?d.geometry=l(d.geometry,i.O49):h.mode===N.TRIANGLE_FAN&&(d.geometry=l(d.geometry,i.rYR));else if(h.mode===N.LINES)d=new i.DXC(f,p);else if(h.mode===N.LINE_STRIP)d=new i.N1A(f,p);else if(h.mode===N.LINE_LOOP)d=new i.FCc(f,p);else if(h.mode===N.POINTS)d=new i.ONl(f,p);else throw Error("THREE.GLTFLoader: Primitive mode unsupported: "+h.mode);Object.keys(d.geometry.morphAttributes).length>0&&function(e,t){if(e.updateMorphTargets(),void 0!==t.weights)for(let r=0,n=t.weights.length;r<n;r++)e.morphTargetInfluences[r]=t.weights[r];if(t.extras&&Array.isArray(t.extras.targetNames)){let r=t.extras.targetNames;if(e.morphTargetInfluences.length===r.length){e.morphTargetDictionary={};for(let t=0,n=r.length;t<n;t++)e.morphTargetDictionary[r[t]]=t}else console.warn("THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.")}}(d,a),d.name=t.createUniqueName(a.name||"mesh_"+e),$(d,a),h.extensions&&V(n,d,h),t.assignFinalMaterial(d),u.push(d)}for(let r=0,n=u.length;r<n;r++)t.associations.set(u[r],{meshes:e,primitives:r});if(1===u.length)return a.extensions&&V(n,u[0],a),u[0];let d=new i.YJl;a.extensions&&V(n,d,a),t.associations.set(d,{meshes:e});for(let e=0,t=u.length;e<t;e++)d.add(u[e]);return d})}loadCamera(e){let t,r=this.json.cameras[e],n=r[r.type];return n?("perspective"===r.type?t=new i.ubm(i.cj9.radToDeg(n.yfov),n.aspectRatio||1,n.znear||1,n.zfar||2e6):"orthographic"===r.type&&(t=new i.qUd(-n.xmag,n.xmag,n.ymag,-n.ymag,n.znear,n.zfar)),r.name&&(t.name=this.createUniqueName(r.name)),$(t,r),Promise.resolve(t)):void console.warn("THREE.GLTFLoader: Missing camera parameters.")}loadSkin(e){let t=this.json.skins[e],r=[];for(let e=0,n=t.joints.length;e<n;e++)r.push(this._loadNodeShallow(t.joints[e]));return void 0!==t.inverseBindMatrices?r.push(this.getDependency("accessor",t.inverseBindMatrices)):r.push(null),Promise.all(r).then(function(e){let r=e.pop(),n=[],a=[];for(let o=0,s=e.length;o<s;o++){let s=e[o];if(s){n.push(s);let e=new i.kn4;null!==r&&e.fromArray(r.array,16*o),a.push(e)}else console.warn('THREE.GLTFLoader: Joint "%s" could not be found.',t.joints[o])}return new i.EAD(n,a)})}loadAnimation(e){let t=this.json,r=this,n=t.animations[e],a=n.name?n.name:"animation_"+e,o=[],s=[],l=[],c=[],u=[];for(let e=0,t=n.channels.length;e<t;e++){let t=n.channels[e],r=n.samplers[t.sampler],i=t.target,a=i.node,d=void 0!==n.parameters?n.parameters[r.input]:r.input,f=void 0!==n.parameters?n.parameters[r.output]:r.output;void 0!==i.node&&(o.push(this.getDependency("node",a)),s.push(this.getDependency("accessor",d)),l.push(this.getDependency("accessor",f)),c.push(r),u.push(i))}return Promise.all([Promise.all(o),Promise.all(s),Promise.all(l),Promise.all(c),Promise.all(u)]).then(function(e){let t=e[0],n=e[1],o=e[2],s=e[3],l=e[4],c=[];for(let e=0,i=t.length;e<i;e++){let i=t[e],a=n[e],u=o[e],d=s[e],f=l[e];if(void 0===i)continue;i.updateMatrix&&i.updateMatrix();let h=r._createAnimationTracks(i,a,u,d,f);if(h)for(let e=0;e<h.length;e++)c.push(h[e])}return new i.tz3(a,void 0,c)})}createNodeMesh(e){let t=this.json,r=this,n=t.nodes[e];return void 0===n.mesh?null:r.getDependency("mesh",n.mesh).then(function(e){let t=r._getNodeRef(r.meshCache,n.mesh,e);return void 0!==n.weights&&t.traverse(function(e){if(e.isMesh)for(let t=0,r=n.weights.length;t<r;t++)e.morphTargetInfluences[t]=n.weights[t]}),t})}loadNode(e){let t=this.json.nodes[e],r=this._loadNodeShallow(e),n=[],i=t.children||[];for(let e=0,t=i.length;e<t;e++)n.push(this.getDependency("node",i[e]));let a=void 0===t.skin?Promise.resolve(null):this.getDependency("skin",t.skin);return Promise.all([r,Promise.all(n),a]).then(function(e){let t=e[0],r=e[1],n=e[2];null!==n&&t.traverse(function(e){e.isSkinnedMesh&&e.bind(n,er)});for(let e=0,n=r.length;e<n;e++)t.add(r[e]);return t})}_loadNodeShallow(e){let t=this.json,r=this.extensions,n=this;if(void 0!==this.nodeCache[e])return this.nodeCache[e];let a=t.nodes[e],o=a.name?n.createUniqueName(a.name):"",s=[],l=n._invokeOne(function(t){return t.createNodeMesh&&t.createNodeMesh(e)});return l&&s.push(l),void 0!==a.camera&&s.push(n.getDependency("camera",a.camera).then(function(e){return n._getNodeRef(n.cameraCache,a.camera,e)})),n._invokeAll(function(t){return t.createNodeAttachment&&t.createNodeAttachment(e)}).forEach(function(e){s.push(e)}),this.nodeCache[e]=Promise.all(s).then(function(t){let s;if((s=!0===a.isBone?new i.$Kf:t.length>1?new i.YJl:1===t.length?t[0]:new i.B69)!==t[0])for(let e=0,r=t.length;e<r;e++)s.add(t[e]);if(a.name&&(s.userData.name=a.name,s.name=o),$(s,a),a.extensions&&V(r,s,a),void 0!==a.matrix){let e=new i.kn4;e.fromArray(a.matrix),s.applyMatrix4(e)}else void 0!==a.translation&&s.position.fromArray(a.translation),void 0!==a.rotation&&s.quaternion.fromArray(a.rotation),void 0!==a.scale&&s.scale.fromArray(a.scale);return n.associations.has(s)||n.associations.set(s,{}),n.associations.get(s).nodes=e,s}),this.nodeCache[e]}loadScene(e){let t=this.extensions,r=this.json.scenes[e],n=this,a=new i.YJl;r.name&&(a.name=n.createUniqueName(r.name)),$(a,r),r.extensions&&V(t,a,r);let o=r.nodes||[],s=[];for(let e=0,t=o.length;e<t;e++)s.push(n.getDependency("node",o[e]));return Promise.all(s).then(function(e){for(let t=0,r=e.length;t<r;t++)a.add(e[t]);return n.associations=(e=>{let t=new Map;for(let[e,r]of n.associations)(e instanceof i.imn||e instanceof i.gPd)&&t.set(e,r);return e.traverse(e=>{let r=n.associations.get(e);null!=r&&t.set(e,r)}),t})(a),a})}_createAnimationTracks(e,t,r,n,a){let o,s=[],l=e.name?e.name:e.uuid,c=[];switch(z[a.path]===z.weights?e.traverse(function(e){e.morphTargetInfluences&&c.push(e.name?e.name:e.uuid)}):c.push(l),z[a.path]){case z.weights:o=i.Hit;break;case z.rotation:o=i.MBL;break;case z.position:case z.scale:o=i.RiT;break;default:o=1===r.itemSize?i.Hit:i.RiT}let u=void 0!==n.interpolation?Z[n.interpolation]:i.PJ3,d=this._getArrayFromAccessor(r);for(let e=0,r=c.length;e<r;e++){let r=new o(c[e]+"."+z[a.path],t.array,d,u);"CUBICSPLINE"===n.interpolation&&this._createCubicSplineTrackInterpolant(r),s.push(r)}return s}_getArrayFromAccessor(e){let t=e.array;if(e.normalized){let e=et(t.constructor),r=new Float32Array(t.length);for(let n=0,i=t.length;n<i;n++)r[n]=t[n]*e;t=r}return t}_createCubicSplineTrackInterpolant(e){e.createInterpolant=function(e){return new(this instanceof i.MBL?j:k)(this.times,this.values,this.getValueSize()/3,e)},e.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline=!0}}function ei(e,t,r){let n=t.attributes,a=[];for(let t in n){let i=W[t]||t.toLowerCase();i in e.attributes||a.push(function(t,n){return r.getDependency("accessor",t).then(function(t){e.setAttribute(n,t)})}(n[t],i))}if(void 0!==t.indices&&!e.index){let n=r.getDependency("accessor",t.indices).then(function(t){e.setIndex(t)});a.push(n)}return $(e,t),!function(e,t,r){let n=t.attributes,a=new i.NRn;if(void 0===n.POSITION)return;{let e=r.json.accessors[n.POSITION],t=e.min,o=e.max;if(void 0===t||void 0===o)return console.warn("THREE.GLTFLoader: Missing min/max properties for accessor POSITION.");if(a.set(new i.Pq0(t[0],t[1],t[2]),new i.Pq0(o[0],o[1],o[2])),e.normalized){let t=et(K[e.componentType]);a.min.multiplyScalar(t),a.max.multiplyScalar(t)}}let o=t.targets;if(void 0!==o){let e=new i.Pq0,t=new i.Pq0;for(let n=0,i=o.length;n<i;n++){let i=o[n];if(void 0!==i.POSITION){let n=r.json.accessors[i.POSITION],a=n.min,o=n.max;if(void 0!==a&&void 0!==o){if(t.setX(Math.max(Math.abs(a[0]),Math.abs(o[0]))),t.setY(Math.max(Math.abs(a[1]),Math.abs(o[1]))),t.setZ(Math.max(Math.abs(a[2]),Math.abs(o[2]))),n.normalized){let e=et(K[n.componentType]);t.multiplyScalar(e)}e.max(t)}else console.warn("THREE.GLTFLoader: Missing min/max properties for accessor POSITION.")}}a.expandByVector(e)}e.boundingBox=a;let s=new i.iyt;a.getCenter(s.center),s.radius=a.min.distanceTo(a.max)/2,e.boundingSphere=s}(e,t,r),Promise.all(a).then(function(){return void 0!==t.targets?function(e,t,r){let n=!1,i=!1,a=!1;for(let e=0,r=t.length;e<r;e++){let r=t[e];if(void 0!==r.POSITION&&(n=!0),void 0!==r.NORMAL&&(i=!0),void 0!==r.COLOR_0&&(a=!0),n&&i&&a)break}if(!n&&!i&&!a)return Promise.resolve(e);let o=[],s=[],l=[];for(let c=0,u=t.length;c<u;c++){let u=t[c];if(n){let t=void 0!==u.POSITION?r.getDependency("accessor",u.POSITION):e.attributes.position;o.push(t)}if(i){let t=void 0!==u.NORMAL?r.getDependency("accessor",u.NORMAL):e.attributes.normal;s.push(t)}if(a){let t=void 0!==u.COLOR_0?r.getDependency("accessor",u.COLOR_0):e.attributes.color;l.push(t)}}return Promise.all([Promise.all(o),Promise.all(s),Promise.all(l)]).then(function(t){let r=t[0],o=t[1],s=t[2];return n&&(e.morphAttributes.position=r),i&&(e.morphAttributes.normal=o),a&&(e.morphAttributes.color=s),e.morphTargetsRelative=!0,e})}(e,t.targets,r):e})}var ea=r(461);let eo=null,es="https://www.gstatic.com/draco/versioned/decoders/1.5.5/";function el(e=!0,t=!0,r){return i=>{r&&r(i),e&&(eo||(eo=new o),eo.setDecoderPath("string"==typeof e?e:es),i.setDRACOLoader(eo)),t&&i.setMeshoptDecoder((()=>{let e;if(n)return n;let t=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,3,2,0,0,5,3,1,0,1,12,1,0,10,22,2,12,0,65,0,65,0,65,0,252,10,0,0,11,7,0,65,0,253,15,26,11]),r=new Uint8Array([32,0,65,253,3,1,2,34,4,106,6,5,11,8,7,20,13,33,12,16,128,9,116,64,19,113,127,15,10,21,22,14,255,66,24,54,136,107,18,23,192,26,114,118,132,17,77,101,130,144,27,87,131,44,45,74,156,154,70,167]);if("object"!=typeof WebAssembly)return{supported:!1};let i="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";WebAssembly.validate(t)&&(i="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");let a=WebAssembly.instantiate(function(e){let t=new Uint8Array(e.length);for(let r=0;r<e.length;++r){let n=e.charCodeAt(r);t[r]=n>96?n-71:n>64?n-65:n>47?n+4:n>46?63:62}let n=0;for(let i=0;i<e.length;++i)t[n++]=t[i]<60?r[t[i]]:(t[i]-60)*64+t[++i];return t.buffer.slice(0,n)}(i),{}).then(t=>{(e=t.instance).exports.__wasm_call_ctors()});function o(t,r,n,i,a,o){let s=e.exports.sbrk,l=n+3&-4,c=s(l*i),u=s(a.length),d=new Uint8Array(e.exports.memory.buffer);d.set(a,u);let f=t(c,n,i,u,a.length);if(0===f&&o&&o(c,l,i),r.set(d.subarray(c,c+n*i)),s(c-s(0)),0!==f)throw Error(`Malformed buffer data: ${f}`)}let s={0:"",1:"meshopt_decodeFilterOct",2:"meshopt_decodeFilterQuat",3:"meshopt_decodeFilterExp",NONE:"",OCTAHEDRAL:"meshopt_decodeFilterOct",QUATERNION:"meshopt_decodeFilterQuat",EXPONENTIAL:"meshopt_decodeFilterExp"},l={0:"meshopt_decodeVertexBuffer",1:"meshopt_decodeIndexBuffer",2:"meshopt_decodeIndexSequence",ATTRIBUTES:"meshopt_decodeVertexBuffer",TRIANGLES:"meshopt_decodeIndexBuffer",INDICES:"meshopt_decodeIndexSequence"};return n={ready:a,supported:!0,decodeVertexBuffer(t,r,n,i,a){o(e.exports.meshopt_decodeVertexBuffer,t,r,n,i,e.exports[s[a]])},decodeIndexBuffer(t,r,n,i){o(e.exports.meshopt_decodeIndexBuffer,t,r,n,i)},decodeIndexSequence(t,r,n,i){o(e.exports.meshopt_decodeIndexSequence,t,r,n,i)},decodeGltfBuffer(t,r,n,i,a,c){o(e.exports[l[a]],t,r,n,i,e.exports[s[c]])}}})())}}let ec=(e,t,r,n)=>(0,ea.G)(h,e,el(t,r,n));ec.preload=(e,t,r,n)=>ea.G.preload(h,e,el(t,r,n)),ec.clear=e=>ea.G.clear(h,e),ec.setDecoderPath=e=>{es=e}},3625:(e,t,r)=>{r.d(t,{r:()=>n});let n=parseInt(r(3264).sPf.replace(/\D+/g,""))},3758:(e,t,r)=>{r.d(t,{u:()=>s});var n=r(9630),i=r(2115),a=r(461),o=r(3264);let s=i.forwardRef(({envMap:e,resolution:t=256,frames:r=1/0,makeDefault:s,children:l,...c},u)=>{let d=(0,a.C)(({set:e})=>e),f=(0,a.C)(({camera:e})=>e),h=(0,a.C)(({size:e})=>e),p=i.useRef(null);i.useImperativeHandle(u,()=>p.current,[]);let A=i.useRef(null),m=function(e,t,r){let n=(0,a.C)(e=>e.size),s=(0,a.C)(e=>e.viewport),l="number"==typeof e?e:n.width*s.dpr,c=n.height*s.dpr,u=("number"==typeof e?void 0:e)||{},{samples:d=0,depth:f,...h}=u,p=null!=f?f:u.depthBuffer,A=i.useMemo(()=>{let e=new o.nWS(l,c,{minFilter:o.k6q,magFilter:o.k6q,type:o.ix0,...h});return p&&(e.depthTexture=new o.VCu(l,c,o.RQf)),e.samples=d,e},[]);return i.useLayoutEffect(()=>{A.setSize(l,c),d&&(A.samples=d)},[d,A,l,c]),i.useEffect(()=>()=>A.dispose(),[]),A}(t);i.useLayoutEffect(()=>{c.manual||(p.current.aspect=h.width/h.height)},[h,c]),i.useLayoutEffect(()=>{p.current.updateProjectionMatrix()});let v=0,g=null,B="function"==typeof l;return(0,a.D)(t=>{B&&(r===1/0||v<r)&&(A.current.visible=!1,t.gl.setRenderTarget(m),g=t.scene.background,e&&(t.scene.background=e),t.gl.render(t.scene,p.current),t.scene.background=g,t.gl.setRenderTarget(null),A.current.visible=!0,v++)}),i.useLayoutEffect(()=>{if(s)return d(()=>({camera:p.current})),()=>d(()=>({camera:f}))},[p,s,d]),i.createElement(i.Fragment,null,i.createElement("perspectiveCamera",(0,n.A)({ref:p},c),!B&&l),i.createElement("group",{ref:A},B&&l(m.texture)))})},4073:(e,t,r)=>{r.d(t,{CC:()=>N,Q6:()=>K,bL:()=>j,zi:()=>Q});var n=r(2115),i=r(9367),a=r(5185),o=r(6101),s=r(6081),l=r(5845),c=r(4315),u=r(5503),d=r(1275),f=r(3655),h=r(7328),p=r(5155),A=["PageUp","PageDown"],m=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],v={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},g="Slider",[B,C,b]=(0,h.N)(g),[y,E]=(0,s.A)(g,[b]),[M,w]=y(g),x=n.forwardRef((e,t)=>{let{name:r,min:o=0,max:s=100,step:c=1,orientation:u="horizontal",disabled:d=!1,minStepsBetweenThumbs:f=0,defaultValue:h=[o],value:v,onValueChange:g=()=>{},onValueCommit:C=()=>{},inverted:b=!1,form:y,...E}=e,w=n.useRef(new Set),x=n.useRef(0),I="horizontal"===u,[R=[],T]=(0,l.i)({prop:v,defaultProp:h,onChange:e=>{var t;null==(t=[...w.current][x.current])||t.focus(),g(e)}}),G=n.useRef(R);function S(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1},n=(String(c).split(".")[1]||"").length,a=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-o)/c)*c+o,n),l=(0,i.q)(a,[o,s]);T(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,l,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*c))return e;{x.current=n.indexOf(l);let t=String(n)!==String(e);return t&&r&&C(n),t?n:e}})}return(0,p.jsx)(M,{scope:e.__scopeSlider,name:r,disabled:d,min:o,max:s,valueIndexToChangeRef:x,thumbs:w.current,values:R,orientation:u,form:y,children:(0,p.jsx)(B.Provider,{scope:e.__scopeSlider,children:(0,p.jsx)(B.Slot,{scope:e.__scopeSlider,children:(0,p.jsx)(I?F:D,{"aria-disabled":d,"data-disabled":d?"":void 0,...E,ref:t,onPointerDown:(0,a.mK)(E.onPointerDown,()=>{d||(G.current=R)}),min:o,max:s,inverted:b,onSlideStart:d?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(R,e);S(e,t)},onSlideMove:d?void 0:function(e){S(e,x.current)},onSlideEnd:d?void 0:function(){let e=G.current[x.current];R[x.current]!==e&&C(R)},onHomeKeyDown:()=>!d&&S(o,0,{commit:!0}),onEndKeyDown:()=>!d&&S(s,R.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!d){let e=A.includes(t.key)||t.shiftKey&&m.includes(t.key),n=x.current;S(R[n]+c*(e?10:1)*r,n,{commit:!0})}}})})})})});x.displayName=g;var[I,R]=y(g,{startEdge:"left",endEdge:"right",size:"width",direction:1}),F=n.forwardRef((e,t)=>{let{min:r,max:i,dir:a,inverted:s,onSlideStart:l,onSlideMove:u,onSlideEnd:d,onStepKeyDown:f,...h}=e,[A,m]=n.useState(null),g=(0,o.s)(t,e=>m(e)),B=n.useRef(void 0),C=(0,c.jH)(a),b="ltr"===C,y=b&&!s||!b&&s;function E(e){let t=B.current||A.getBoundingClientRect(),n=J([0,t.width],y?[r,i]:[i,r]);return B.current=t,n(e-t.left)}return(0,p.jsx)(I,{scope:e.__scopeSlider,startEdge:y?"left":"right",endEdge:y?"right":"left",direction:y?1:-1,size:"width",children:(0,p.jsx)(T,{dir:C,"data-orientation":"horizontal",...h,ref:g,style:{...h.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=E(e.clientX);null==l||l(t)},onSlideMove:e=>{let t=E(e.clientX);null==u||u(t)},onSlideEnd:()=>{B.current=void 0,null==d||d()},onStepKeyDown:e=>{let t=v[y?"from-left":"from-right"].includes(e.key);null==f||f({event:e,direction:t?-1:1})}})})}),D=n.forwardRef((e,t)=>{let{min:r,max:i,inverted:a,onSlideStart:s,onSlideMove:l,onSlideEnd:c,onStepKeyDown:u,...d}=e,f=n.useRef(null),h=(0,o.s)(t,f),A=n.useRef(void 0),m=!a;function g(e){let t=A.current||f.current.getBoundingClientRect(),n=J([0,t.height],m?[i,r]:[r,i]);return A.current=t,n(e-t.top)}return(0,p.jsx)(I,{scope:e.__scopeSlider,startEdge:m?"bottom":"top",endEdge:m?"top":"bottom",size:"height",direction:m?1:-1,children:(0,p.jsx)(T,{"data-orientation":"vertical",...d,ref:h,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=g(e.clientY);null==s||s(t)},onSlideMove:e=>{let t=g(e.clientY);null==l||l(t)},onSlideEnd:()=>{A.current=void 0,null==c||c()},onStepKeyDown:e=>{let t=v[m?"from-bottom":"from-top"].includes(e.key);null==u||u({event:e,direction:t?-1:1})}})})}),T=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:i,onSlideEnd:o,onHomeKeyDown:s,onEndKeyDown:l,onStepKeyDown:c,...u}=e,d=w(g,r);return(0,p.jsx)(f.sG.span,{...u,ref:t,onKeyDown:(0,a.mK)(e.onKeyDown,e=>{"Home"===e.key?(s(e),e.preventDefault()):"End"===e.key?(l(e),e.preventDefault()):A.concat(m).includes(e.key)&&(c(e),e.preventDefault())}),onPointerDown:(0,a.mK)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),d.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,a.mK)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&i(e)}),onPointerUp:(0,a.mK)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),o(e))})})}),G="SliderTrack",S=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,i=w(G,r);return(0,p.jsx)(f.sG.span,{"data-disabled":i.disabled?"":void 0,"data-orientation":i.orientation,...n,ref:t})});S.displayName=G;var P="SliderRange",_=n.forwardRef((e,t)=>{let{__scopeSlider:r,...i}=e,a=w(P,r),s=R(P,r),l=n.useRef(null),c=(0,o.s)(t,l),u=a.values.length,d=a.values.map(e=>k(e,a.min,a.max)),h=u>1?Math.min(...d):0,A=100-Math.max(...d);return(0,p.jsx)(f.sG.span,{"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,...i,ref:c,style:{...e.style,[s.startEdge]:h+"%",[s.endEdge]:A+"%"}})});_.displayName=P;var H="SliderThumb",O=n.forwardRef((e,t)=>{let r=C(e.__scopeSlider),[i,a]=n.useState(null),s=(0,o.s)(t,e=>a(e)),l=n.useMemo(()=>i?r().findIndex(e=>e.ref.current===i):-1,[r,i]);return(0,p.jsx)(L,{...e,ref:s,index:l})}),L=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:i,name:s,...l}=e,c=w(H,r),u=R(H,r),[h,A]=n.useState(null),m=(0,o.s)(t,e=>A(e)),v=!h||c.form||!!h.closest("form"),g=(0,d.X)(h),C=c.values[i],b=void 0===C?0:k(C,c.min,c.max),y=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(i,c.values.length),E=null==g?void 0:g[u.size],M=E?function(e,t,r){let n=e/2,i=J([0,50],[0,n]);return(n-i(t)*r)*r}(E,b,u.direction):0;return n.useEffect(()=>{if(h)return c.thumbs.add(h),()=>{c.thumbs.delete(h)}},[h,c.thumbs]),(0,p.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[u.startEdge]:"calc(".concat(b,"% + ").concat(M,"px)")},children:[(0,p.jsx)(B.ItemSlot,{scope:e.__scopeSlider,children:(0,p.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||y,"aria-valuemin":c.min,"aria-valuenow":C,"aria-valuemax":c.max,"aria-orientation":c.orientation,"data-orientation":c.orientation,"data-disabled":c.disabled?"":void 0,tabIndex:c.disabled?void 0:0,...l,ref:m,style:void 0===C?{display:"none"}:e.style,onFocus:(0,a.mK)(e.onFocus,()=>{c.valueIndexToChangeRef.current=i})})}),v&&(0,p.jsx)(U,{name:null!=s?s:c.name?c.name+(c.values.length>1?"[]":""):void 0,form:c.form,value:C},i)]})});O.displayName=H;var U=n.forwardRef((e,t)=>{let{__scopeSlider:r,value:i,...a}=e,s=n.useRef(null),l=(0,o.s)(s,t),c=(0,u.Z)(i);return n.useEffect(()=>{let e=s.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(c!==i&&t){let r=new Event("input",{bubbles:!0});t.call(e,i),e.dispatchEvent(r)}},[c,i]),(0,p.jsx)(f.sG.input,{style:{display:"none"},...a,ref:l,defaultValue:i})});function k(e,t,r){return(0,i.q)(100/(r-t)*(e-t),[0,100])}function J(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}U.displayName="RadioBubbleInput";var j=x,N=S,K=_,Q=O},4229:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4253:(e,t,r)=>{r.d(t,{A:()=>n});function n(){return function(e){function t(e,t){for(var r,n,i,a,o,s=/([MLQCZ])([^MLQCZ]*)/g;r=s.exec(e);){var l=r[2].replace(/^\s*|\s*$/g,"").split(/[,\s]+/).map(function(e){return parseFloat(e)});switch(r[1]){case"M":a=n=l[0],o=i=l[1];break;case"L":(l[0]!==a||l[1]!==o)&&t("L",a,o,a=l[0],o=l[1]);break;case"Q":t("Q",a,o,a=l[2],o=l[3],l[0],l[1]);break;case"C":t("C",a,o,a=l[4],o=l[5],l[0],l[1],l[2],l[3]);break;case"Z":(a!==n||o!==i)&&t("L",a,o,n,i)}}}function r(e,r,n){void 0===n&&(n=16);var i={x:0,y:0};t(e,function(e,t,a,o,s,l,c,u,d){switch(e){case"L":r(t,a,o,s);break;case"Q":for(var f=t,h=a,p=1;p<n;p++)!function(e,t,r,n,i,a,o,s){var l=1-o;s.x=l*l*e+2*l*o*r+o*o*i,s.y=l*l*t+2*l*o*n+o*o*a}(t,a,l,c,o,s,p/(n-1),i),r(f,h,i.x,i.y),f=i.x,h=i.y;break;case"C":for(var A=t,m=a,v=1;v<n;v++)!function(e,t,r,n,i,a,o,s,l,c){var u=1-l;c.x=u*u*u*e+3*u*u*l*r+3*u*l*l*i+l*l*l*o,c.y=u*u*u*t+3*u*u*l*n+3*u*l*l*a+l*l*l*s}(t,a,l,c,u,d,o,s,v/(n-1),i),r(A,m,i.x,i.y),A=i.x,m=i.y}})}var n="precision highp float;attribute vec2 aUV;varying vec2 vUV;void main(){vUV=aUV;gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}",i=new WeakMap,a={premultipliedAlpha:!1,preserveDrawingBuffer:!0,antialias:!1,depth:!1};function o(e,t){var r=e.getContext?e.getContext("webgl",a):e,n=i.get(r);if(!n){var o="undefined"!=typeof WebGL2RenderingContext&&r instanceof WebGL2RenderingContext,s={},l={},c={},u=-1,d=[];function f(e){var t=s[e];if(!t&&!(t=s[e]=r.getExtension(e)))throw Error(e+" not supported");return t}function h(e,t){var n=r.createShader(t);return r.shaderSource(n,e),r.compileShader(n),n}function p(){s={},l={},c={},u=-1,d.length=0}r.canvas.addEventListener("webglcontextlost",function(e){p(),e.preventDefault()},!1),i.set(r,n={gl:r,isWebGL2:o,getExtension:f,withProgram:function(e,t,n,i){if(!l[e]){var a={},s={},c=r.createProgram();r.attachShader(c,h(t,r.VERTEX_SHADER)),r.attachShader(c,h(n,r.FRAGMENT_SHADER)),r.linkProgram(c),l[e]={program:c,transaction:function(e){r.useProgram(c),e({setUniform:function(e,t){for(var n=[],i=arguments.length-2;i-- >0;)n[i]=arguments[i+2];var a=s[t]||(s[t]=r.getUniformLocation(c,t));r["uniform"+e].apply(r,[a].concat(n))},setAttribute:function(e,t,n,i,s){var l=a[e];l||(l=a[e]={buf:r.createBuffer(),loc:r.getAttribLocation(c,e),data:null}),r.bindBuffer(r.ARRAY_BUFFER,l.buf),r.vertexAttribPointer(l.loc,t,r.FLOAT,!1,0,0),r.enableVertexAttribArray(l.loc),o?r.vertexAttribDivisor(l.loc,i):f("ANGLE_instanced_arrays").vertexAttribDivisorANGLE(l.loc,i),s!==l.data&&(r.bufferData(r.ARRAY_BUFFER,s,n),l.data=s)}})}}}l[e].transaction(i)},withTexture:function(e,t){u++;try{r.activeTexture(r.TEXTURE0+u);var n=c[e];n||(n=c[e]=r.createTexture(),r.bindTexture(r.TEXTURE_2D,n),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,r.NEAREST),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MAG_FILTER,r.NEAREST)),r.bindTexture(r.TEXTURE_2D,n),t(n,u)}finally{u--}},withTextureFramebuffer:function(e,t,n){var i=r.createFramebuffer();d.push(i),r.bindFramebuffer(r.FRAMEBUFFER,i),r.activeTexture(r.TEXTURE0+t),r.bindTexture(r.TEXTURE_2D,e),r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.TEXTURE_2D,e,0);try{n(i)}finally{r.deleteFramebuffer(i),r.bindFramebuffer(r.FRAMEBUFFER,d[--d.length-1]||null)}},handleContextLoss:p})}t(n)}function s(e,t,r,i,a,s,l,c){void 0===l&&(l=15),void 0===c&&(c=null),o(e,function(e){var o=e.gl,u=e.withProgram;(0,e.withTexture)("copy",function(e,d){o.texImage2D(o.TEXTURE_2D,0,o.RGBA,a,s,0,o.RGBA,o.UNSIGNED_BYTE,t),u("copy",n,"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){gl_FragColor=texture2D(tex,vUV);}",function(e){var t=e.setUniform;(0,e.setAttribute)("aUV",2,o.STATIC_DRAW,0,new Float32Array([0,0,2,0,0,2])),t("1i","image",d),o.bindFramebuffer(o.FRAMEBUFFER,c||null),o.disable(o.BLEND),o.colorMask(8&l,4&l,2&l,1&l),o.viewport(r,i,a,s),o.scissor(r,i,a,s),o.drawArrays(o.TRIANGLES,0,3)})})})}var l=Object.freeze({__proto__:null,withWebGLContext:o,renderImageData:s,resizeWebGLCanvasWithoutClearing:function(e,t,r){var n=e.width,i=e.height;o(e,function(a){var o=a.gl,l=new Uint8Array(n*i*4);o.readPixels(0,0,n,i,o.RGBA,o.UNSIGNED_BYTE,l),e.width=t,e.height=r,s(o,l,0,0,n,i)})}});function c(e,t,n,i,a,o){void 0===o&&(o=1);var s=new Uint8Array(e*t),l=i[2]-i[0],c=i[3]-i[1],u=[];r(n,function(e,t,r,n){u.push({x1:e,y1:t,x2:r,y2:n,minX:Math.min(e,r),minY:Math.min(t,n),maxX:Math.max(e,r),maxY:Math.max(t,n)})}),u.sort(function(e,t){return e.maxX-t.maxX});for(var d=0;d<e;d++)for(var f=0;f<t;f++){var h=function(e,t){for(var r=1/0,n=1/0,i=u.length;i--;){var a=u[i];if(a.maxX+n<=e)break;if(e+n>a.minX&&t-n<a.maxY&&t+n>a.minY){var o=function(e,t,r,n,i,a){var o=i-r,s=a-n,l=o*o+s*s,c=l?Math.max(0,Math.min(1,((e-r)*o+(t-n)*s)/l)):0,u=e-(r+c*o),d=t-(n+c*s);return u*u+d*d}(e,t,a.x1,a.y1,a.x2,a.y2);o<r&&(n=Math.sqrt(r=o))}}return function(e,t){for(var r=0,n=u.length;n--;){var i=u[n];if(i.maxX<=e)break;i.y1>t!=i.y2>t&&e<(i.x2-i.x1)*(t-i.y1)/(i.y2-i.y1)+i.x1&&(r+=i.y1<i.y2?1:-1)}return 0!==r}(e,t)&&(n=-n),n}(i[0]+l*(d+.5)/e,i[1]+c*(f+.5)/t),p=Math.pow(1-Math.abs(h)/a,o)/2;h<0&&(p=1-p),p=Math.max(0,Math.min(255,Math.round(255*p))),s[f*e+d]=p}return s}function u(e,t,r,n,i,a,o,s,l,c){void 0===a&&(a=1),void 0===s&&(s=0),void 0===l&&(l=0),void 0===c&&(c=0),d(e,t,r,n,i,a,o,null,s,l,c)}function d(e,t,r,n,i,a,o,l,u,d,f){void 0===a&&(a=1),void 0===u&&(u=0),void 0===d&&(d=0),void 0===f&&(f=0);for(var h=c(e,t,r,n,i,a),p=new Uint8Array(4*h.length),A=0;A<h.length;A++)p[4*A+f]=h[A];s(o,p,u,d,e,t,1<<3-f,l)}var f=Object.freeze({__proto__:null,generate:c,generateIntoCanvas:u,generateIntoFramebuffer:d}),h=new Float32Array([0,0,2,0,0,2]),p=null,A=!1,m={},v=new WeakMap;function g(e){if(!A&&!y(e))throw Error("WebGL generation not supported")}function B(e,t,r,n,i,a,s){if(void 0===a&&(a=1),void 0===s&&(s=null),!s&&!(s=p)){var l="function"==typeof OffscreenCanvas?new OffscreenCanvas(1,1):"undefined"!=typeof document?document.createElement("canvas"):null;if(!l)throw Error("OffscreenCanvas or DOM canvas not supported");s=p=l.getContext("webgl",{depth:!1})}g(s);var c=new Uint8Array(e*t*4);o(s,function(o){var s=o.gl,l=o.withTexture,u=o.withTextureFramebuffer;l("readable",function(o,l){s.texImage2D(s.TEXTURE_2D,0,s.RGBA,e,t,0,s.RGBA,s.UNSIGNED_BYTE,null),u(o,l,function(o){b(e,t,r,n,i,a,s,o,0,0,0),s.readPixels(0,0,e,t,s.RGBA,s.UNSIGNED_BYTE,c)})})});for(var u=new Uint8Array(e*t),d=0,f=0;d<c.length;d+=4)u[f++]=c[d];return u}function C(e,t,r,n,i,a,o,s,l,c){void 0===a&&(a=1),void 0===s&&(s=0),void 0===l&&(l=0),void 0===c&&(c=0),b(e,t,r,n,i,a,o,null,s,l,c)}function b(e,t,i,a,s,l,c,u,d,f,p){void 0===l&&(l=1),void 0===d&&(d=0),void 0===f&&(f=0),void 0===p&&(p=0),g(c);var A=[];r(i,function(e,t,r,n){A.push(e,t,r,n)}),A=new Float32Array(A),o(c,function(r){var i=r.gl,o=r.isWebGL2,c=r.getExtension,m=r.withProgram,v=r.withTexture,g=r.withTextureFramebuffer,B=r.handleContextLoss;if(v("rawDistances",function(r,v){(e!==r._lastWidth||t!==r._lastHeight)&&i.texImage2D(i.TEXTURE_2D,0,i.RGBA,r._lastWidth=e,r._lastHeight=t,0,i.RGBA,i.UNSIGNED_BYTE,null),m("main","precision highp float;uniform vec4 uGlyphBounds;attribute vec2 aUV;attribute vec4 aLineSegment;varying vec4 vLineSegment;varying vec2 vGlyphXY;void main(){vLineSegment=aLineSegment;vGlyphXY=mix(uGlyphBounds.xy,uGlyphBounds.zw,aUV);gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}","precision highp float;uniform vec4 uGlyphBounds;uniform float uMaxDistance;uniform float uExponent;varying vec4 vLineSegment;varying vec2 vGlyphXY;float absDistToSegment(vec2 point,vec2 lineA,vec2 lineB){vec2 lineDir=lineB-lineA;float lenSq=dot(lineDir,lineDir);float t=lenSq==0.0 ? 0.0 : clamp(dot(point-lineA,lineDir)/lenSq,0.0,1.0);vec2 linePt=lineA+t*lineDir;return distance(point,linePt);}void main(){vec4 seg=vLineSegment;vec2 p=vGlyphXY;float dist=absDistToSegment(p,seg.xy,seg.zw);float val=pow(1.0-clamp(dist/uMaxDistance,0.0,1.0),uExponent)*0.5;bool crossing=(seg.y>p.y!=seg.w>p.y)&&(p.x<(seg.z-seg.x)*(p.y-seg.y)/(seg.w-seg.y)+seg.x);bool crossingUp=crossing&&vLineSegment.y<vLineSegment.w;gl_FragColor=vec4(crossingUp ? 1.0/255.0 : 0.0,crossing&&!crossingUp ? 1.0/255.0 : 0.0,0.0,val);}",function(n){var u=n.setAttribute,d=n.setUniform,f=!o&&c("ANGLE_instanced_arrays"),p=!o&&c("EXT_blend_minmax");u("aUV",2,i.STATIC_DRAW,0,h),u("aLineSegment",4,i.DYNAMIC_DRAW,1,A),d.apply(void 0,["4f","uGlyphBounds"].concat(a)),d("1f","uMaxDistance",s),d("1f","uExponent",l),g(r,v,function(r){i.enable(i.BLEND),i.colorMask(!0,!0,!0,!0),i.viewport(0,0,e,t),i.scissor(0,0,e,t),i.blendFunc(i.ONE,i.ONE),i.blendEquationSeparate(i.FUNC_ADD,o?i.MAX:p.MAX_EXT),i.clear(i.COLOR_BUFFER_BIT),o?i.drawArraysInstanced(i.TRIANGLES,0,3,A.length/4):f.drawArraysInstancedANGLE(i.TRIANGLES,0,3,A.length/4)})}),m("post",n,"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){vec4 color=texture2D(tex,vUV);bool inside=color.r!=color.g;float val=inside ? 1.0-color.a : color.a;gl_FragColor=vec4(val);}",function(r){r.setAttribute("aUV",2,i.STATIC_DRAW,0,h),r.setUniform("1i","tex",v),i.bindFramebuffer(i.FRAMEBUFFER,u),i.disable(i.BLEND),i.colorMask(0===p,1===p,2===p,3===p),i.viewport(d,f,e,t),i.scissor(d,f,e,t),i.drawArrays(i.TRIANGLES,0,3)})}),i.isContextLost())throw B(),Error("webgl context lost")})}function y(e){var t=e&&e!==p?e.canvas||e:m,r=v.get(t);if(void 0===r){A=!0;var n=null;try{var i=[97,106,97,61,99,137,118,80,80,118,137,99,61,97,106,97],a=B(4,4,"M8,8L16,8L24,24L16,24Z",[0,0,32,32],24,1,e);(r=a&&i.length===a.length&&a.every(function(e,t){return e===i[t]}))||(n="bad trial run results",console.info(i,a))}catch(e){r=!1,n=e.message}n&&console.warn("WebGL SDF generation not supported:",n),A=!1,v.set(t,r)}return r}var E=Object.freeze({__proto__:null,generate:B,generateIntoCanvas:C,generateIntoFramebuffer:b,isSupported:y});return e.forEachPathCommand=t,e.generate=function(e,t,r,n,i,a){void 0===i&&(i=Math.max(n[2]-n[0],n[3]-n[1])/2),void 0===a&&(a=1);try{return B.apply(E,arguments)}catch(e){return console.info("WebGL SDF generation failed, falling back to JS",e),c.apply(f,arguments)}},e.generateIntoCanvas=function(e,t,r,n,i,a,o,s,l,c){void 0===i&&(i=Math.max(n[2]-n[0],n[3]-n[1])/2),void 0===a&&(a=1),void 0===s&&(s=0),void 0===l&&(l=0),void 0===c&&(c=0);try{return C.apply(E,arguments)}catch(e){return console.info("WebGL SDF generation failed, falling back to JS",e),u.apply(f,arguments)}},e.javascript=f,e.pathToLineSegments=r,e.webgl=E,e.webglUtils=l,Object.defineProperty(e,"__esModule",{value:!0}),e}({})}},4342:(e,t,r)=>{e.exports=r(7319)},4688:(e,t,r)=>{r.d(t,{N:()=>m});var n=r(9630),i=r(461),a=r(2115),o=r(3264),s=Object.defineProperty;class l{constructor(){((e,t,r)=>((e,t,r)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r))(this,"_listeners")}addEventListener(e,t){void 0===this._listeners&&(this._listeners={});let r=this._listeners;void 0===r[e]&&(r[e]=[]),-1===r[e].indexOf(t)&&r[e].push(t)}hasEventListener(e,t){if(void 0===this._listeners)return!1;let r=this._listeners;return void 0!==r[e]&&-1!==r[e].indexOf(t)}removeEventListener(e,t){if(void 0===this._listeners)return;let r=this._listeners[e];if(void 0!==r){let e=r.indexOf(t);-1!==e&&r.splice(e,1)}}dispatchEvent(e){if(void 0===this._listeners)return;let t=this._listeners[e.type];if(void 0!==t){e.target=this;let r=t.slice(0);for(let t=0,n=r.length;t<n;t++)r[t].call(this,e);e.target=null}}}var c=Object.defineProperty,u=(e,t,r)=>(((e,t,r)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r)(e,"symbol"!=typeof t?t+"":t,r),r);let d=new o.RlV,f=new o.Zcv,h=Math.cos(Math.PI/180*70),p=(e,t)=>(e%t+t)%t;class A extends l{constructor(e,t){super(),u(this,"object"),u(this,"domElement"),u(this,"enabled",!0),u(this,"target",new o.Pq0),u(this,"minDistance",0),u(this,"maxDistance",1/0),u(this,"minZoom",0),u(this,"maxZoom",1/0),u(this,"minPolarAngle",0),u(this,"maxPolarAngle",Math.PI),u(this,"minAzimuthAngle",-1/0),u(this,"maxAzimuthAngle",1/0),u(this,"enableDamping",!1),u(this,"dampingFactor",.05),u(this,"enableZoom",!0),u(this,"zoomSpeed",1),u(this,"enableRotate",!0),u(this,"rotateSpeed",1),u(this,"enablePan",!0),u(this,"panSpeed",1),u(this,"screenSpacePanning",!0),u(this,"keyPanSpeed",7),u(this,"zoomToCursor",!1),u(this,"autoRotate",!1),u(this,"autoRotateSpeed",2),u(this,"reverseOrbit",!1),u(this,"reverseHorizontalOrbit",!1),u(this,"reverseVerticalOrbit",!1),u(this,"keys",{LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"}),u(this,"mouseButtons",{LEFT:o.kBv.ROTATE,MIDDLE:o.kBv.DOLLY,RIGHT:o.kBv.PAN}),u(this,"touches",{ONE:o.wtR.ROTATE,TWO:o.wtR.DOLLY_PAN}),u(this,"target0"),u(this,"position0"),u(this,"zoom0"),u(this,"_domElementKeyEvents",null),u(this,"getPolarAngle"),u(this,"getAzimuthalAngle"),u(this,"setPolarAngle"),u(this,"setAzimuthalAngle"),u(this,"getDistance"),u(this,"getZoomScale"),u(this,"listenToKeyEvents"),u(this,"stopListenToKeyEvents"),u(this,"saveState"),u(this,"reset"),u(this,"update"),u(this,"connect"),u(this,"dispose"),u(this,"dollyIn"),u(this,"dollyOut"),u(this,"getScale"),u(this,"setScale"),this.object=e,this.domElement=t,this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this.getPolarAngle=()=>A.phi,this.getAzimuthalAngle=()=>A.theta,this.setPolarAngle=e=>{let t=p(e,2*Math.PI),n=A.phi;n<0&&(n+=2*Math.PI),t<0&&(t+=2*Math.PI);let i=Math.abs(t-n);2*Math.PI-i<i&&(t<n?t+=2*Math.PI:n+=2*Math.PI),m.phi=t-n,r.update()},this.setAzimuthalAngle=e=>{let t=p(e,2*Math.PI),n=A.theta;n<0&&(n+=2*Math.PI),t<0&&(t+=2*Math.PI);let i=Math.abs(t-n);2*Math.PI-i<i&&(t<n?t+=2*Math.PI:n+=2*Math.PI),m.theta=t-n,r.update()},this.getDistance=()=>r.object.position.distanceTo(r.target),this.listenToKeyEvents=e=>{e.addEventListener("keydown",ee),this._domElementKeyEvents=e},this.stopListenToKeyEvents=()=>{this._domElementKeyEvents.removeEventListener("keydown",ee),this._domElementKeyEvents=null},this.saveState=()=>{r.target0.copy(r.target),r.position0.copy(r.object.position),r.zoom0=r.object.zoom},this.reset=()=>{r.target.copy(r.target0),r.object.position.copy(r.position0),r.object.zoom=r.zoom0,r.object.updateProjectionMatrix(),r.dispatchEvent(n),r.update(),l=s.NONE},this.update=(()=>{let t=new o.Pq0,i=new o.Pq0(0,1,0),a=new o.PTz().setFromUnitVectors(e.up,i),u=a.clone().invert(),p=new o.Pq0,B=new o.PTz,C=2*Math.PI;return function(){let b=r.object.position;a.setFromUnitVectors(e.up,i),u.copy(a).invert(),t.copy(b).sub(r.target),t.applyQuaternion(a),A.setFromVector3(t),r.autoRotate&&l===s.NONE&&P(2*Math.PI/60/60*r.autoRotateSpeed),r.enableDamping?(A.theta+=m.theta*r.dampingFactor,A.phi+=m.phi*r.dampingFactor):(A.theta+=m.theta,A.phi+=m.phi);let y=r.minAzimuthAngle,E=r.maxAzimuthAngle;isFinite(y)&&isFinite(E)&&(y<-Math.PI?y+=C:y>Math.PI&&(y-=C),E<-Math.PI?E+=C:E>Math.PI&&(E-=C),y<=E?A.theta=Math.max(y,Math.min(E,A.theta)):A.theta=A.theta>(y+E)/2?Math.max(y,A.theta):Math.min(E,A.theta)),A.phi=Math.max(r.minPolarAngle,Math.min(r.maxPolarAngle,A.phi)),A.makeSafe(),!0===r.enableDamping?r.target.addScaledVector(g,r.dampingFactor):r.target.add(g),r.zoomToCursor&&D||r.object.isOrthographicCamera?A.radius=J(A.radius):A.radius=J(A.radius*v),t.setFromSpherical(A),t.applyQuaternion(u),b.copy(r.target).add(t),r.object.matrixAutoUpdate||r.object.updateMatrix(),r.object.lookAt(r.target),!0===r.enableDamping?(m.theta*=1-r.dampingFactor,m.phi*=1-r.dampingFactor,g.multiplyScalar(1-r.dampingFactor)):(m.set(0,0,0),g.set(0,0,0));let M=!1;if(r.zoomToCursor&&D){let n=null;if(r.object instanceof o.ubm&&r.object.isPerspectiveCamera){let e=t.length();n=J(e*v);let i=e-n;r.object.position.addScaledVector(R,i),r.object.updateMatrixWorld()}else if(r.object.isOrthographicCamera){let e=new o.Pq0(F.x,F.y,0);e.unproject(r.object),r.object.zoom=Math.max(r.minZoom,Math.min(r.maxZoom,r.object.zoom/v)),r.object.updateProjectionMatrix(),M=!0;let i=new o.Pq0(F.x,F.y,0);i.unproject(r.object),r.object.position.sub(i).add(e),r.object.updateMatrixWorld(),n=t.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),r.zoomToCursor=!1;null!==n&&(r.screenSpacePanning?r.target.set(0,0,-1).transformDirection(r.object.matrix).multiplyScalar(n).add(r.object.position):(d.origin.copy(r.object.position),d.direction.set(0,0,-1).transformDirection(r.object.matrix),Math.abs(r.object.up.dot(d.direction))<h?e.lookAt(r.target):(f.setFromNormalAndCoplanarPoint(r.object.up,r.target),d.intersectPlane(f,r.target))))}else r.object instanceof o.qUd&&r.object.isOrthographicCamera&&(M=1!==v)&&(r.object.zoom=Math.max(r.minZoom,Math.min(r.maxZoom,r.object.zoom/v)),r.object.updateProjectionMatrix());return v=1,D=!1,!!(M||p.distanceToSquared(r.object.position)>c||8*(1-B.dot(r.object.quaternion))>c)&&(r.dispatchEvent(n),p.copy(r.object.position),B.copy(r.object.quaternion),M=!1,!0)}})(),this.connect=e=>{r.domElement=e,r.domElement.style.touchAction="none",r.domElement.addEventListener("contextmenu",et),r.domElement.addEventListener("pointerdown",Z),r.domElement.addEventListener("pointercancel",V),r.domElement.addEventListener("wheel",$)},this.dispose=()=>{var e,t,n,i,a,o;r.domElement&&(r.domElement.style.touchAction="auto"),null==(e=r.domElement)||e.removeEventListener("contextmenu",et),null==(t=r.domElement)||t.removeEventListener("pointerdown",Z),null==(n=r.domElement)||n.removeEventListener("pointercancel",V),null==(i=r.domElement)||i.removeEventListener("wheel",$),null==(a=r.domElement)||a.ownerDocument.removeEventListener("pointermove",q),null==(o=r.domElement)||o.ownerDocument.removeEventListener("pointerup",V),null!==r._domElementKeyEvents&&r._domElementKeyEvents.removeEventListener("keydown",ee)};let r=this,n={type:"change"},i={type:"start"},a={type:"end"},s={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6},l=s.NONE,c=1e-6,A=new o.YHV,m=new o.YHV,v=1,g=new o.Pq0,B=new o.I9Y,C=new o.I9Y,b=new o.I9Y,y=new o.I9Y,E=new o.I9Y,M=new o.I9Y,w=new o.I9Y,x=new o.I9Y,I=new o.I9Y,R=new o.Pq0,F=new o.I9Y,D=!1,T=[],G={};function S(){return Math.pow(.95,r.zoomSpeed)}function P(e){r.reverseOrbit||r.reverseHorizontalOrbit?m.theta+=e:m.theta-=e}function _(e){r.reverseOrbit||r.reverseVerticalOrbit?m.phi+=e:m.phi-=e}let H=(()=>{let e=new o.Pq0;return function(t,r){e.setFromMatrixColumn(r,0),e.multiplyScalar(-t),g.add(e)}})(),O=(()=>{let e=new o.Pq0;return function(t,n){!0===r.screenSpacePanning?e.setFromMatrixColumn(n,1):(e.setFromMatrixColumn(n,0),e.crossVectors(r.object.up,e)),e.multiplyScalar(t),g.add(e)}})(),L=(()=>{let e=new o.Pq0;return function(t,n){let i=r.domElement;if(i&&r.object instanceof o.ubm&&r.object.isPerspectiveCamera){let a=r.object.position;e.copy(a).sub(r.target);let o=e.length();H(2*t*(o*=Math.tan(r.object.fov/2*Math.PI/180))/i.clientHeight,r.object.matrix),O(2*n*o/i.clientHeight,r.object.matrix)}else i&&r.object instanceof o.qUd&&r.object.isOrthographicCamera?(H(t*(r.object.right-r.object.left)/r.object.zoom/i.clientWidth,r.object.matrix),O(n*(r.object.top-r.object.bottom)/r.object.zoom/i.clientHeight,r.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),r.enablePan=!1)}})();function U(e){r.object instanceof o.ubm&&r.object.isPerspectiveCamera||r.object instanceof o.qUd&&r.object.isOrthographicCamera?v=e:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),r.enableZoom=!1)}function k(e){if(!r.zoomToCursor||!r.domElement)return;D=!0;let t=r.domElement.getBoundingClientRect(),n=e.clientX-t.left,i=e.clientY-t.top,a=t.width,o=t.height;F.x=n/a*2-1,F.y=-(i/o*2)+1,R.set(F.x,F.y,1).unproject(r.object).sub(r.object.position).normalize()}function J(e){return Math.max(r.minDistance,Math.min(r.maxDistance,e))}function j(e){B.set(e.clientX,e.clientY)}function N(e){y.set(e.clientX,e.clientY)}function K(){if(1==T.length)B.set(T[0].pageX,T[0].pageY);else{let e=.5*(T[0].pageX+T[1].pageX),t=.5*(T[0].pageY+T[1].pageY);B.set(e,t)}}function Q(){if(1==T.length)y.set(T[0].pageX,T[0].pageY);else{let e=.5*(T[0].pageX+T[1].pageX),t=.5*(T[0].pageY+T[1].pageY);y.set(e,t)}}function X(){let e=T[0].pageX-T[1].pageX,t=T[0].pageY-T[1].pageY,r=Math.sqrt(e*e+t*t);w.set(0,r)}function Y(e){if(1==T.length)C.set(e.pageX,e.pageY);else{let t=en(e),r=.5*(e.pageX+t.x),n=.5*(e.pageY+t.y);C.set(r,n)}b.subVectors(C,B).multiplyScalar(r.rotateSpeed);let t=r.domElement;t&&(P(2*Math.PI*b.x/t.clientHeight),_(2*Math.PI*b.y/t.clientHeight)),B.copy(C)}function W(e){if(1==T.length)E.set(e.pageX,e.pageY);else{let t=en(e),r=.5*(e.pageX+t.x),n=.5*(e.pageY+t.y);E.set(r,n)}M.subVectors(E,y).multiplyScalar(r.panSpeed),L(M.x,M.y),y.copy(E)}function z(e){var t;let n=en(e),i=e.pageX-n.x,a=e.pageY-n.y,o=Math.sqrt(i*i+a*a);x.set(0,o),I.set(0,Math.pow(x.y/w.y,r.zoomSpeed)),t=I.y,U(v/t),w.copy(x)}function Z(e){var t,n,a;!1!==r.enabled&&(0===T.length&&(null==(t=r.domElement)||t.ownerDocument.addEventListener("pointermove",q),null==(n=r.domElement)||n.ownerDocument.addEventListener("pointerup",V)),a=e,T.push(a),"touch"===e.pointerType?function(e){switch(er(e),T.length){case 1:switch(r.touches.ONE){case o.wtR.ROTATE:if(!1===r.enableRotate)return;K(),l=s.TOUCH_ROTATE;break;case o.wtR.PAN:if(!1===r.enablePan)return;Q(),l=s.TOUCH_PAN;break;default:l=s.NONE}break;case 2:switch(r.touches.TWO){case o.wtR.DOLLY_PAN:if(!1===r.enableZoom&&!1===r.enablePan)return;r.enableZoom&&X(),r.enablePan&&Q(),l=s.TOUCH_DOLLY_PAN;break;case o.wtR.DOLLY_ROTATE:if(!1===r.enableZoom&&!1===r.enableRotate)return;r.enableZoom&&X(),r.enableRotate&&K(),l=s.TOUCH_DOLLY_ROTATE;break;default:l=s.NONE}break;default:l=s.NONE}l!==s.NONE&&r.dispatchEvent(i)}(e):function(e){let t;switch(e.button){case 0:t=r.mouseButtons.LEFT;break;case 1:t=r.mouseButtons.MIDDLE;break;case 2:t=r.mouseButtons.RIGHT;break;default:t=-1}switch(t){case o.kBv.DOLLY:if(!1===r.enableZoom)return;k(e),w.set(e.clientX,e.clientY),l=s.DOLLY;break;case o.kBv.ROTATE:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===r.enablePan)return;N(e),l=s.PAN}else{if(!1===r.enableRotate)return;j(e),l=s.ROTATE}break;case o.kBv.PAN:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===r.enableRotate)return;j(e),l=s.ROTATE}else{if(!1===r.enablePan)return;N(e),l=s.PAN}break;default:l=s.NONE}l!==s.NONE&&r.dispatchEvent(i)}(e))}function q(e){!1!==r.enabled&&("touch"===e.pointerType?function(e){switch(er(e),l){case s.TOUCH_ROTATE:if(!1===r.enableRotate)return;Y(e),r.update();break;case s.TOUCH_PAN:if(!1===r.enablePan)return;W(e),r.update();break;case s.TOUCH_DOLLY_PAN:if(!1===r.enableZoom&&!1===r.enablePan)return;r.enableZoom&&z(e),r.enablePan&&W(e),r.update();break;case s.TOUCH_DOLLY_ROTATE:if(!1===r.enableZoom&&!1===r.enableRotate)return;r.enableZoom&&z(e),r.enableRotate&&Y(e),r.update();break;default:l=s.NONE}}(e):function(e){if(!1!==r.enabled)switch(l){case s.ROTATE:if(!1===r.enableRotate)return;C.set(e.clientX,e.clientY),b.subVectors(C,B).multiplyScalar(r.rotateSpeed);let t=r.domElement;t&&(P(2*Math.PI*b.x/t.clientHeight),_(2*Math.PI*b.y/t.clientHeight)),B.copy(C),r.update();break;case s.DOLLY:var n,i;if(!1===r.enableZoom)return;(x.set(e.clientX,e.clientY),I.subVectors(x,w),I.y>0)?(n=S(),U(v/n)):I.y<0&&(i=S(),U(v*i)),w.copy(x),r.update();break;case s.PAN:if(!1===r.enablePan)return;E.set(e.clientX,e.clientY),M.subVectors(E,y).multiplyScalar(r.panSpeed),L(M.x,M.y),y.copy(E),r.update()}}(e))}function V(e){var t,n,i;(function(e){delete G[e.pointerId];for(let t=0;t<T.length;t++)if(T[t].pointerId==e.pointerId)return void T.splice(t,1)})(e),0===T.length&&(null==(t=r.domElement)||t.releasePointerCapture(e.pointerId),null==(n=r.domElement)||n.ownerDocument.removeEventListener("pointermove",q),null==(i=r.domElement)||i.ownerDocument.removeEventListener("pointerup",V)),r.dispatchEvent(a),l=s.NONE}function $(e){if(!1!==r.enabled&&!1!==r.enableZoom&&(l===s.NONE||l===s.ROTATE)){var t,n;e.preventDefault(),r.dispatchEvent(i),(k(e),e.deltaY<0)?(t=S(),U(v*t)):e.deltaY>0&&(n=S(),U(v/n)),r.update(),r.dispatchEvent(a)}}function ee(e){if(!1!==r.enabled&&!1!==r.enablePan){let t=!1;switch(e.code){case r.keys.UP:L(0,r.keyPanSpeed),t=!0;break;case r.keys.BOTTOM:L(0,-r.keyPanSpeed),t=!0;break;case r.keys.LEFT:L(r.keyPanSpeed,0),t=!0;break;case r.keys.RIGHT:L(-r.keyPanSpeed,0),t=!0}t&&(e.preventDefault(),r.update())}}function et(e){!1!==r.enabled&&e.preventDefault()}function er(e){let t=G[e.pointerId];void 0===t&&(t=new o.I9Y,G[e.pointerId]=t),t.set(e.pageX,e.pageY)}function en(e){return G[(e.pointerId===T[0].pointerId?T[1]:T[0]).pointerId]}this.dollyIn=(e=S())=>{U(v*e),r.update()},this.dollyOut=(e=S())=>{U(v/e),r.update()},this.getScale=()=>v,this.setScale=e=>{U(e),r.update()},this.getZoomScale=()=>S(),void 0!==t&&this.connect(t),this.update()}}let m=a.forwardRef(({makeDefault:e,camera:t,regress:r,domElement:o,enableDamping:s=!0,keyEvents:l=!1,onChange:c,onStart:u,onEnd:d,...f},h)=>{let p=(0,i.C)(e=>e.invalidate),m=(0,i.C)(e=>e.camera),v=(0,i.C)(e=>e.gl),g=(0,i.C)(e=>e.events),B=(0,i.C)(e=>e.setEvents),C=(0,i.C)(e=>e.set),b=(0,i.C)(e=>e.get),y=(0,i.C)(e=>e.performance),E=t||m,M=o||g.connected||v.domElement,w=a.useMemo(()=>new A(E),[E]);return(0,i.D)(()=>{w.enabled&&w.update()},-1),a.useEffect(()=>(l&&w.connect(!0===l?M:l),w.connect(M),()=>void w.dispose()),[l,M,r,w,p]),a.useEffect(()=>{let e=e=>{p(),r&&y.regress(),c&&c(e)},t=e=>{u&&u(e)},n=e=>{d&&d(e)};return w.addEventListener("change",e),w.addEventListener("start",t),w.addEventListener("end",n),()=>{w.removeEventListener("start",t),w.removeEventListener("end",n),w.removeEventListener("change",e)}},[c,u,d,w,p,B]),a.useEffect(()=>{if(e){let e=b().controls;return C({controls:w}),()=>C({controls:e})}},[e,w]),a.createElement("primitive",(0,n.A)({ref:h,object:w,enableDamping:s},f))})},5026:(e,t,r)=>{r.d(t,{p:()=>o}),r(2115);var n=r(3264),i=r(5521);let a=0,o=(0,i.v)(e=>(n.h_9.onStart=(t,r,n)=>{e({active:!0,item:t,loaded:r,total:n,progress:(r-a)/(n-a)*100})},n.h_9.onLoad=()=>{e({active:!1})},n.h_9.onError=t=>e(e=>({errors:[...e.errors,t]})),n.h_9.onProgress=(t,r,n)=>{r===n&&(a=n),e({active:!0,item:t,loaded:r,total:n,progress:(r-a)/(n-a)*100||100})},{errors:[],active:!1,progress:0,item:"",loaded:0,total:0}))},5202:(e,t,r)=>{function n(){var e=Object.create(null);function t(e,t){var r=void 0;self.troikaDefine=function(e){return r=e};var n=URL.createObjectURL(new Blob(["/** "+e.replace(/\*/g,"")+" **/\n\ntroikaDefine(\n"+t+"\n)"],{type:"application/javascript"}));try{importScripts(n)}catch(e){console.error(e)}return URL.revokeObjectURL(n),delete self.troikaDefine,r}self.addEventListener("message",function(r){var n=r.data,i=n.messageId,a=n.action,o=n.data;try{"registerModule"===a&&function r(n,i){var a=n.id,o=n.name,s=n.dependencies;void 0===s&&(s=[]);var l=n.init;void 0===l&&(l=function(){});var c=n.getTransferables;if(void 0===c&&(c=null),!e[a])try{s=s.map(function(t){return t&&t.isWorkerModule&&(r(t,function(e){if(e instanceof Error)throw e}),t=e[t.id].value),t}),l=t("<"+o+">.init",l),c&&(c=t("<"+o+">.getTransferables",c));var u=null;"function"==typeof l?u=l.apply(void 0,s):console.error("worker module init function failed to rehydrate"),e[a]={id:a,value:u,getTransferables:c},i(u)}catch(e){e&&e.noLog||console.error(e),i(e)}}(o,function(e){e instanceof Error?postMessage({messageId:i,success:!1,error:e.message}):postMessage({messageId:i,success:!0,result:{isCallable:"function"==typeof e}})}),"callModule"===a&&function(t,r){var n,i=t.id,a=t.args;e[i]&&"function"==typeof e[i].value||r(Error("Worker module "+i+": not found or its 'init' did not return a function"));try{var o=(n=e[i]).value.apply(n,a);o&&"function"==typeof o.then?o.then(s,function(e){return r(e instanceof Error?e:Error(""+e))}):s(o)}catch(e){r(e)}function s(t){try{var n=e[i].getTransferables&&e[i].getTransferables(t);n&&Array.isArray(n)&&n.length||(n=void 0),r(t,n)}catch(e){console.error(e),r(e)}}}(o,function(e,t){e instanceof Error?postMessage({messageId:i,success:!1,error:e.message}):postMessage({messageId:i,success:!0,result:e},t||void 0)})}catch(e){postMessage({messageId:i,success:!1,error:e.stack})}})}r.d(t,{Qw:()=>d,kl:()=>function e(t){if((!t||"function"!=typeof t.init)&&!s)throw Error("requires `options.init` function");var r,n=t.dependencies,o=t.init,l=t.getTransferables,u=t.workerId,d=((r=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];return r._getInitResult().then(function(t){if("function"==typeof t)return t.apply(void 0,e);throw Error("Worker module function was called but `init` did not return a callable function")})})._getInitResult=function(){var e=t.dependencies,n=t.init,i=Promise.all(e=Array.isArray(e)?e.map(function(e){return e&&(e=e.onMainThread||e)._getInitResult&&(e=e._getInitResult()),e}):[]).then(function(e){return n.apply(null,e)});return r._getInitResult=function(){return i},i},r);null==u&&(u="#default");var p="workerModule"+ ++a,A=t.name||p,m=null;function v(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];if(!i())return d.apply(void 0,e);if(!m){m=h(u,"registerModule",v.workerModuleData);var r=function(){m=null,c[u].delete(r)};(c[u]||(c[u]=new Set)).add(r)}return m.then(function(t){if(t.isCallable)return h(u,"callModule",{id:p,args:e});throw Error("Worker module function was called but `init` did not return a callable function")})}return n=n&&n.map(function(t){return"function"!=typeof t||t.workerModuleData||(s=!0,t=e({workerId:u,name:"<"+A+"> function dependency: "+t.name,init:"function(){return (\n"+f(t)+"\n)}"}),s=!1),t&&t.workerModuleData&&(t=t.workerModuleData),t}),v.workerModuleData={isWorkerModule:!0,id:p,name:A,dependencies:n,init:f(o),getTransferables:l&&f(l)},v.onMainThread=d,v}}),r(4561);var i=function(){var e=!1;if("undefined"!=typeof window&&void 0!==window.document)try{new Worker(URL.createObjectURL(new Blob([""],{type:"application/javascript"}))).terminate(),e=!0}catch(e){console.log("Troika createWorkerModule: web workers not allowed; falling back to main thread execution. Cause: ["+e.message+"]")}return i=function(){return e},e},a=0,o=0,s=!1,l=Object.create(null),c=Object.create(null),u=Object.create(null);function d(e){c[e]&&c[e].forEach(function(e){e()}),l[e]&&(l[e].terminate(),delete l[e])}function f(e){var t=e.toString();return!/^function/.test(t)&&/^\w+\s*\(/.test(t)&&(t="function "+t),t}function h(e,t,r){return new Promise(function(i,a){var s=++o;u[s]=function(e){e.success?i(e.result):a(Error("Error in worker "+t+" call: "+e.error))},(function(e){var t=l[e];if(!t){var r=f(n);(t=l[e]=new Worker(URL.createObjectURL(new Blob(["/** Worker Module Bootstrap: "+e.replace(/\*/g,"")+" **/\n\n;("+r+")()"],{type:"application/javascript"})))).onmessage=function(e){var t=e.data,r=t.messageId,n=u[r];if(!n)throw Error("WorkerModule response with empty or unknown messageId");delete u[r],n(t)}}return t})(e).postMessage({messageId:s,action:t,data:r})})}},5220:(e,t,r)=>{e.exports=r(1724)},5273:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},5454:(e,t,r)=>{r.d(t,{A:()=>n});let n=function(){return function(e){var t,r,n,i,a={R:"13k,1a,2,3,3,2+1j,ch+16,a+1,5+2,2+n,5,a,4,6+16,4+3,h+1b,4mo,179q,2+9,2+11,2i9+7y,2+68,4,3+4,5+13,4+3,2+4k,3+29,8+cf,1t+7z,w+17,3+3m,1t+3z,16o1+5r,8+30,8+mc,29+1r,29+4v,75+73",EN:"1c+9,3d+1,6,187+9,513,4+5,7+9,sf+j,175h+9,qw+q,161f+1d,4xt+a,25i+9",ES:"17,2,6dp+1,f+1,av,16vr,mx+1,4o,2",ET:"z+2,3h+3,b+1,ym,3e+1,2o,p4+1,8,6u,7c,g6,1wc,1n9+4,30+1b,2n,6d,qhx+1,h0m,a+1,49+2,63+1,4+1,6bb+3,12jj",AN:"16o+5,2j+9,2+1,35,ed,1ff2+9,87+u",CS:"18,2+1,b,2u,12k,55v,l,17v0,2,3,53,2+1,b",B:"a,3,f+2,2v,690",S:"9,2,k",WS:"c,k,4f4,1vk+a,u,1j,335",ON:"x+1,4+4,h+5,r+5,r+3,z,5+3,2+1,2+1,5,2+2,3+4,o,w,ci+1,8+d,3+d,6+8,2+g,39+1,9,6+1,2,33,b8,3+1,3c+1,7+1,5r,b,7h+3,sa+5,2,3i+6,jg+3,ur+9,2v,ij+1,9g+9,7+a,8m,4+1,49+x,14u,2+2,c+2,e+2,e+2,e+1,i+n,e+e,2+p,u+2,e+2,36+1,2+3,2+1,b,2+2,6+5,2,2,2,h+1,5+4,6+3,3+f,16+2,5+3l,3+81,1y+p,2+40,q+a,m+13,2r+ch,2+9e,75+hf,3+v,2+2w,6e+5,f+6,75+2a,1a+p,2+2g,d+5x,r+b,6+3,4+o,g,6+1,6+2,2k+1,4,2j,5h+z,1m+1,1e+f,t+2,1f+e,d+3,4o+3,2s+1,w,535+1r,h3l+1i,93+2,2s,b+1,3l+x,2v,4g+3,21+3,kz+1,g5v+1,5a,j+9,n+v,2,3,2+8,2+1,3+2,2,3,46+1,4+4,h+5,r+5,r+a,3h+2,4+6,b+4,78,1r+24,4+c,4,1hb,ey+6,103+j,16j+c,1ux+7,5+g,fsh,jdq+1t,4,57+2e,p1,1m,1m,1m,1m,4kt+1,7j+17,5+2r,d+e,3+e,2+e,2+10,m+4,w,1n+5,1q,4z+5,4b+rb,9+c,4+c,4+37,d+2g,8+b,l+b,5+1j,9+9,7+13,9+t,3+1,27+3c,2+29,2+3q,d+d,3+4,4+2,6+6,a+o,8+6,a+2,e+6,16+42,2+1i",BN:"0+8,6+d,2s+5,2+p,e,4m9,1kt+2,2b+5,5+5,17q9+v,7k,6p+8,6+1,119d+3,440+7,96s+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+1,1ekf+75,6p+2rz,1ben+1,1ekf+1,1ekf+1",NSM:"lc+33,7o+6,7c+18,2,2+1,2+1,2,21+a,1d+k,h,2u+6,3+5,3+1,2+3,10,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,g+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+g,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,k1+w,2db+2,3y,2p+v,ff+3,30+1,n9x+3,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,r2,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+5,3+1,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2d+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,f0c+4,1o+6,t5,1s+3,2a,f5l+1,43t+2,i+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,gzhy+6n",AL:"16w,3,2,e+1b,z+2,2+2s,g+1,8+1,b+m,2+t,s+2i,c+e,4h+f,1d+1e,1bwe+dp,3+3z,x+c,2+1,35+3y,2rm+z,5+7,b+5,dt+l,c+u,17nl+27,1t+27,4x+6n,3+d",LRO:"6ct",RLO:"6cu",LRE:"6cq",RLE:"6cr",PDF:"6cs",LRI:"6ee",RLI:"6ef",FSI:"6eg",PDI:"6eh"},o={},s={};o.L=1,s[1]="L",Object.keys(a).forEach(function(e,t){o[e]=1<<t+1,s[o[e]]=e}),Object.freeze(o);var l=o.LRI|o.RLI|o.FSI,c=o.L|o.R|o.AL,u=o.B|o.S|o.WS|o.ON|o.FSI|o.LRI|o.RLI|o.PDI,d=o.BN|o.RLE|o.LRE|o.RLO|o.LRO|o.PDF,f=o.S|o.WS|o.B|l|o.PDI|d,h=null;function p(e){if(!h){h=new Map;var t=function(e){if(a.hasOwnProperty(e)){var t=0;a[e].split(",").forEach(function(r){var n=r.split("+"),i=n[0],a=n[1];i=parseInt(i,36),a=a?parseInt(a,36):0,h.set(t+=i,o[e]);for(var s=0;s<a;s++)h.set(++t,o[e])})}};for(var r in a)t(r)}return h.get(e.codePointAt(0))||o.L}function A(e,t){var r,n=0,i=new Map,a=t&&new Map;return e.split(",").forEach(function e(o){if(-1!==o.indexOf("+"))for(var s=+o;s--;)e(r);else{r=o;var l=o.split(">"),c=l[0],u=l[1];c=String.fromCodePoint(n+=parseInt(c,36)),u=String.fromCodePoint(n+=parseInt(u,36)),i.set(c,u),t&&a.set(u,c)}}),{map:i,reverseMap:a}}function m(){if(!t){var e=A("14>1,1e>2,u>2,2wt>1,1>1,1ge>1,1wp>1,1j>1,f>1,hm>1,1>1,u>1,u6>1,1>1,+5,28>1,w>1,1>1,+3,b8>1,1>1,+3,1>3,-1>-1,3>1,1>1,+2,1s>1,1>1,x>1,th>1,1>1,+2,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,4q>1,1e>2,u>2,2>1,+1",!0),i=e.map,a=e.reverseMap;t=i,r=a,n=A("6f1>-6dx,6dy>-6dx,6ec>-6ed,6ee>-6ed,6ww>2jj,-2ji>2jj,14r4>-1e7l,1e7m>-1e7l,1e7m>-1e5c,1e5d>-1e5b,1e5c>-14qx,14qy>-14qx,14vn>-1ecg,1ech>-1ecg,1edu>-1ecg,1eci>-1ecg,1eda>-1ecg,1eci>-1ecg,1eci>-168q,168r>-168q,168s>-14ye,14yf>-14ye",!1).map}}function v(e){return m(),t.get(e)||null}function g(e){return m(),r.get(e)||null}function B(e){return m(),n.get(e)||null}var C=o.L,b=o.R,y=o.EN,E=o.ES,M=o.ET,w=o.AN,x=o.CS,I=o.B,R=o.S,F=o.ON,D=o.BN,T=o.NSM,G=o.AL,S=o.LRO,P=o.RLO,_=o.LRE,H=o.RLE,O=o.PDF,L=o.LRI,U=o.RLI,k=o.FSI,J=o.PDI;function j(e){if(!i){var t=A("14>1,j>2,t>2,u>2,1a>g,2v3>1,1>1,1ge>1,1wd>1,b>1,1j>1,f>1,ai>3,-2>3,+1,8>1k0,-1jq>1y7,-1y6>1hf,-1he>1h6,-1h5>1ha,-1h8>1qi,-1pu>1,6>3u,-3s>7,6>1,1>1,f>1,1>1,+2,3>1,1>1,+13,4>1,1>1,6>1eo,-1ee>1,3>1mg,-1me>1mk,-1mj>1mi,-1mg>1mi,-1md>1,1>1,+2,1>10k,-103>1,1>1,4>1,5>1,1>1,+10,3>1,1>8,-7>8,+1,-6>7,+1,a>1,1>1,u>1,u6>1,1>1,+5,26>1,1>1,2>1,2>2,8>1,7>1,4>1,1>1,+5,b8>1,1>1,+3,1>3,-2>1,2>1,1>1,+2,c>1,3>1,1>1,+2,h>1,3>1,a>1,1>1,2>1,3>1,1>1,d>1,f>1,3>1,1a>1,1>1,6>1,7>1,13>1,k>1,1>1,+19,4>1,1>1,+2,2>1,1>1,+18,m>1,a>1,1>1,lk>1,1>1,4>1,2>1,f>1,3>1,1>1,+3,db>1,1>1,+3,3>1,1>1,+2,14qm>1,1>1,+1,6>1,4j>1,j>2,t>2,u>2,2>1,+1",!0),r=t.map;t.reverseMap.forEach(function(e,t){r.set(t,e)}),i=r}return i.get(e)||null}function N(e,t,r,n){var i=e.length;r=Math.max(0,null==r?0:+r),n=Math.min(i-1,null==n?i-1:+n);var a=[];return t.paragraphs.forEach(function(i){var o=Math.max(r,i.start),s=Math.min(n,i.end);if(o<s){for(var l=t.levels.slice(o,s+1),c=s;c>=o&&p(e[c])&f;c--)l[c]=i.level;for(var u=i.level,d=1/0,h=0;h<l.length;h++){var A=l[h];A>u&&(u=A),A<d&&(d=1|A)}for(var m=u;m>=d;m--)for(var v=0;v<l.length;v++)if(l[v]>=m){for(var g=v;v+1<l.length&&l[v+1]>=m;)v++;v>g&&a.push([g+o,v+o])}}}),a}function K(e,t,r,n){for(var i=N(e,t,r,n),a=[],o=0;o<e.length;o++)a[o]=o;return i.forEach(function(e){for(var t=e[0],r=e[1],n=a.slice(t,r+1),i=n.length;i--;)a[r-i]=n[i]}),a}return e.closingToOpeningBracket=g,e.getBidiCharType=p,e.getBidiCharTypeName=function(e){return s[p(e)]},e.getCanonicalBracket=B,e.getEmbeddingLevels=function(e,t){for(var r=new Uint32Array(e.length),n=0;n<e.length;n++)r[n]=p(e[n]);var i=new Map;function a(e,t){var n=r[e];r[e]=t,i.set(n,i.get(n)-1),n&u&&i.set(u,i.get(u)-1),i.set(t,(i.get(t)||0)+1),t&u&&i.set(u,(i.get(u)||0)+1)}for(var o=new Uint8Array(e.length),s=new Map,h=[],A=null,m=0;m<e.length;m++)A||h.push(A={start:m,end:e.length-1,level:"rtl"===t?1:"ltr"===t?0:tD(m,!1)}),r[m]&I&&(A.end=m,A=null);for(var j=H|_|P|S|l|J|O|I,N=function(e){return e+(1&e?1:2)},K=function(e){return e+(1&e?2:1)},Q=0;Q<h.length;Q++){var X=[{_level:(A=h[Q]).level,_override:0,_isolate:0}],Y=void 0,W=0,z=0,Z=0;i.clear();for(var q=A.start;q<=A.end;q++){var V=r[q];if(Y=X[X.length-1],i.set(V,(i.get(V)||0)+1),V&u&&i.set(u,(i.get(u)||0)+1),V&j)if(V&(H|_)){o[q]=Y._level;var $=(V===H?K:N)(Y._level);!($<=125)||W||z?!W&&z++:X.push({_level:$,_override:0,_isolate:0})}else if(V&(P|S)){o[q]=Y._level;var ee=(V===P?K:N)(Y._level);!(ee<=125)||W||z?!W&&z++:X.push({_level:ee,_override:V&P?b:C,_isolate:0})}else if(V&l){V&k&&(V=1===tD(q+1,!0)?U:L),o[q]=Y._level,Y._override&&a(q,Y._override);var et=(V===U?K:N)(Y._level);et<=125&&0===W&&0===z?(Z++,X.push({_level:et,_override:0,_isolate:1,_isolInitIndex:q})):W++}else if(V&J){if(W>0)W--;else if(Z>0){for(z=0;!X[X.length-1]._isolate;)X.pop();var er=X[X.length-1]._isolInitIndex;null!=er&&(s.set(er,q),s.set(q,er)),X.pop(),Z--}Y=X[X.length-1],o[q]=Y._level,Y._override&&a(q,Y._override)}else V&O?(0===W&&(z>0?z--:!Y._isolate&&X.length>1&&(X.pop(),Y=X[X.length-1])),o[q]=Y._level):V&I&&(o[q]=A.level);else o[q]=Y._level,Y._override&&V!==D&&a(q,Y._override)}for(var en=[],ei=null,ea=A.start;ea<=A.end;ea++){var eo=r[ea];if(!(eo&d)){var es=o[ea],el=eo&l,ec=eo===J;ei&&es===ei._level?(ei._end=ea,ei._endsWithIsolInit=el):en.push(ei={_start:ea,_end:ea,_level:es,_startsWithPDI:ec,_endsWithIsolInit:el})}}for(var eu=[],ed=0;ed<en.length;ed++){var ef=en[ed];if(!ef._startsWithPDI||ef._startsWithPDI&&!s.has(ef._start)){for(var eh=[ei=ef],ep=void 0;ei&&ei._endsWithIsolInit&&null!=(ep=s.get(ei._end));)for(var eA=ed+1;eA<en.length;eA++)if(en[eA]._start===ep){eh.push(ei=en[eA]);break}for(var em=[],ev=0;ev<eh.length;ev++)for(var eg=eh[ev],eB=eg._start;eB<=eg._end;eB++)em.push(eB);for(var eC=o[em[0]],eb=A.level,ey=em[0]-1;ey>=0;ey--)if(!(r[ey]&d)){eb=o[ey];break}var eE=em[em.length-1],eM=o[eE],ew=A.level;if(!(r[eE]&l)){for(var ex=eE+1;ex<=A.end;ex++)if(!(r[ex]&d)){ew=o[ex];break}}eu.push({_seqIndices:em,_sosType:Math.max(eb,eC)%2?b:C,_eosType:Math.max(ew,eM)%2?b:C})}}for(var eI=0;eI<eu.length;eI++){var eR=eu[eI],eF=eR._seqIndices,eD=eR._sosType,eT=eR._eosType,eG=1&o[eF[0]]?b:C;if(i.get(T))for(var eS=0;eS<eF.length;eS++){var eP=eF[eS];if(r[eP]&T){for(var e_=eD,eH=eS-1;eH>=0;eH--)if(!(r[eF[eH]]&d)){e_=r[eF[eH]];break}a(eP,e_&(l|J)?F:e_)}}if(i.get(y))for(var eO=0;eO<eF.length;eO++){var eL=eF[eO];if(r[eL]&y)for(var eU=eO-1;eU>=-1;eU--){var ek=-1===eU?eD:r[eF[eU]];if(ek&c){ek===G&&a(eL,w);break}}}if(i.get(G))for(var eJ=0;eJ<eF.length;eJ++){var ej=eF[eJ];r[ej]&G&&a(ej,b)}if(i.get(E)||i.get(x))for(var eN=1;eN<eF.length-1;eN++){var eK=eF[eN];if(r[eK]&(E|x)){for(var eQ=0,eX=0,eY=eN-1;eY>=0&&(eQ=r[eF[eY]])&d;eY--);for(var eW=eN+1;eW<eF.length&&(eX=r[eF[eW]])&d;eW++);eQ===eX&&(r[eK]===E?eQ===y:eQ&(y|w))&&a(eK,eQ)}}if(i.get(y)){for(var ez=0;ez<eF.length;ez++)if(r[eF[ez]]&y){for(var eZ=ez-1;eZ>=0&&r[eF[eZ]]&(M|d);eZ--)a(eF[eZ],y);for(ez++;ez<eF.length&&r[eF[ez]]&(M|d|y);ez++)r[eF[ez]]!==y&&a(eF[ez],y)}}if(i.get(M)||i.get(E)||i.get(x))for(var eq=0;eq<eF.length;eq++){var eV=eF[eq];if(r[eV]&(M|E|x)){a(eV,F);for(var e$=eq-1;e$>=0&&r[eF[e$]]&d;e$--)a(eF[e$],F);for(var e0=eq+1;e0<eF.length&&r[eF[e0]]&d;e0++)a(eF[e0],F)}}if(i.get(y))for(var e1=0,e2=eD;e1<eF.length;e1++){var e9=eF[e1],e3=r[e9];e3&y?e2===C&&a(e9,C):e3&c&&(e2=e3)}if(i.get(u)){for(var e6=b|y|w,e8=e6|C,e5=[],e4=[],e7=0;e7<eF.length;e7++)if(r[eF[e7]]&u){var te=e[eF[e7]],tt=void 0;if(null!==v(te))if(e4.length<63)e4.push({char:te,seqIndex:e7});else break;else if(null!==(tt=g(te)))for(var tr=e4.length-1;tr>=0;tr--){var tn=e4[tr].char;if(tn===tt||tn===g(B(te))||v(B(tn))===te){e5.push([e4[tr].seqIndex,e7]),e4.length=tr;break}}}e5.sort(function(e,t){return e[0]-t[0]});for(var ti=0;ti<e5.length;ti++){for(var ta=e5[ti],to=ta[0],ts=ta[1],tl=!1,tc=0,tu=to+1;tu<ts;tu++){var td=eF[tu];if(r[td]&e8){tl=!0;var tf=r[td]&e6?b:C;if(tf===eG){tc=tf;break}}}if(tl&&!tc){tc=eD;for(var th=to-1;th>=0;th--){var tp=eF[th];if(r[tp]&e8){var tA=r[tp]&e6?b:C;tc=tA!==eG?tA:eG;break}}}if(tc){if(r[eF[to]]=r[eF[ts]]=tc,tc!==eG){for(var tm=to+1;tm<eF.length;tm++)if(!(r[eF[tm]]&d)){p(e[eF[tm]])&T&&(r[eF[tm]]=tc);break}}if(tc!==eG){for(var tv=ts+1;tv<eF.length;tv++)if(!(r[eF[tv]]&d)){p(e[eF[tv]])&T&&(r[eF[tv]]=tc);break}}}}for(var tg=0;tg<eF.length;tg++)if(r[eF[tg]]&u){for(var tB=tg,tC=tg,tb=eD,ty=tg-1;ty>=0;ty--)if(r[eF[ty]]&d)tB=ty;else{tb=r[eF[ty]]&e6?b:C;break}for(var tE=eT,tM=tg+1;tM<eF.length;tM++)if(r[eF[tM]]&(u|d))tC=tM;else{tE=r[eF[tM]]&e6?b:C;break}for(var tw=tB;tw<=tC;tw++)r[eF[tw]]=tb===tE?tb:eG;tg=tC}}}for(var tx=A.start;tx<=A.end;tx++){var tI=o[tx],tR=r[tx];if(1&tI?tR&(C|y|w)&&o[tx]++:tR&b?o[tx]++:tR&(w|y)&&(o[tx]+=2),tR&d&&(o[tx]=0===tx?A.level:o[tx-1]),tx===A.end||p(e[tx])&(R|I))for(var tF=tx;tF>=0&&p(e[tF])&f;tF--)o[tF]=A.level}}return{levels:o,paragraphs:h};function tD(t,n){for(var i=t;i<e.length;i++){var a=r[i];if(a&(b|G))return 1;if(a&(I|C)||n&&a===J)break;if(a&l){var o=function(t){for(var n=1,i=t+1;i<e.length;i++){var a=r[i];if(a&I)break;if(a&J){if(0==--n)return i}else a&l&&n++}return -1}(i);i=-1===o?e.length:o}}return 0}},e.getMirroredCharacter=j,e.getMirroredCharactersMap=function(e,t,r,n){var i=e.length;r=Math.max(0,null==r?0:+r),n=Math.min(i-1,null==n?i-1:+n);for(var a=new Map,o=r;o<=n;o++)if(1&t[o]){var s=j(e[o]);null!==s&&a.set(o,s)}return a},e.getReorderSegments=N,e.getReorderedIndices=K,e.getReorderedString=function(e,t,r,n){var i=K(e,t,r,n),a=[].concat(e);return i.forEach(function(r,n){a[n]=(1&t.levels[r]?j(e[r]):null)||e[r]}),a.join("")},e.openingToClosingBracket=v,Object.defineProperty(e,"__esModule",{value:!0}),e}({})}},5571:(e,t,r)=>{r.d(t,{f:()=>o});var n=r(2115),i=r(3264),a=r(461);function o(e,t){let r=n.useRef(null),[o]=n.useState(()=>t?t instanceof i.B69?{current:t}:t:r),[s]=n.useState(()=>new i.Iw4(void 0));n.useLayoutEffect(()=>{t&&(o.current=t instanceof i.B69?t:t.current),s._root=o.current});let l=n.useRef({}),c=n.useMemo(()=>{let t={};return e.forEach(e=>Object.defineProperty(t,e.name,{enumerable:!0,get(){if(o.current)return l.current[e.name]||(l.current[e.name]=s.clipAction(e,o.current))},configurable:!0})),{ref:o,clips:e,actions:t,names:e.map(e=>e.name),mixer:s}},[e]);return(0,a.D)((e,t)=>s.update(t)),n.useEffect(()=>{let e=o.current;return()=>{l.current={},s.stopAllAction(),Object.values(c.actions).forEach(t=>{e&&s.uncacheAction(t,e)})}},[e]),c}},5643:(e,t,r)=>{e.exports=r(6115)},5657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},5690:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]])},6115:(e,t,r)=>{var n=r(2115),i=r(1414),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,s=n.useRef,l=n.useEffect,c=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var d=s(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var h=o(e,(d=c(function(){function e(e){if(!l){if(l=!0,o=e,e=n(e),void 0!==i&&f.hasValue){var t=f.value;if(i(t,e))return s=t}return s=e}if(t=s,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,s=r)}var o,s,l=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,i]))[0],d[1]);return l(function(){f.hasValue=!0,f.value=h},[h]),u(h),h}},6354:(e,t,r)=>{r.d(t,{Af:()=>s,Nz:()=>i,u5:()=>l,y3:()=>d});var n=r(2115);function i(e,t,r){if(!e)return;if(!0===r(e))return e;let n=t?e.return:e.child;for(;n;){let e=i(n,t,r);if(e)return e;n=t?null:n.sibling}}function a(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}(()=>{var e,t;return"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative")})()?n.useLayoutEffect:n.useEffect;let o=a(n.createContext(null));class s extends n.Component{render(){return n.createElement(o.Provider,{value:this._reactInternals},this.props.children)}}function l(){let e=n.useContext(o);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=n.useId();return n.useMemo(()=>{for(let r of[e,null==e?void 0:e.alternate]){if(!r)continue;let e=i(r,!1,e=>{let r=e.memoizedState;for(;r;){if(r.memoizedState===t)return!0;r=r.next}});if(e)return e}},[e,t])}let c=Symbol.for("react.context"),u=e=>null!==e&&"object"==typeof e&&"$$typeof"in e&&e.$$typeof===c;function d(){let e=function(){let e=l(),[t]=n.useState(()=>new Map);t.clear();let r=e;for(;r;){let e=r.type;u(e)&&e!==o&&!t.has(e)&&t.set(e,n.use(a(e))),r=r.return}return t}();return n.useMemo(()=>Array.from(e.keys()).reduce((t,r)=>i=>n.createElement(t,null,n.createElement(r.Provider,{...i,value:e.get(r)})),e=>n.createElement(s,{...e})),[e])}},6500:(e,t)=>{t.ConcurrentRoot=1,t.ContinuousEventPriority=8,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},7319:(e,t)=>{function r(e,t){var r=e.length;for(e.push(t);0<r;){var n=r-1>>>1,i=e[n];if(0<a(i,t))e[n]=t,e[r]=i,r=n;else break}}function n(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;for(var n=0,i=e.length,o=i>>>1;n<o;){var s=2*(n+1)-1,l=e[s],c=s+1,u=e[c];if(0>a(l,r))c<i&&0>a(u,l)?(e[n]=u,e[c]=r,n=c):(e[n]=l,e[s]=r,n=s);else if(c<i&&0>a(u,r))e[n]=u,e[c]=r,n=c;else break}}return t}function a(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var o,s=performance;t.unstable_now=function(){return s.now()}}else{var l=Date,c=l.now();t.unstable_now=function(){return l.now()-c}}var u=[],d=[],f=1,h=null,p=3,A=!1,m=!1,v=!1,g="function"==typeof setTimeout?setTimeout:null,B="function"==typeof clearTimeout?clearTimeout:null,C="undefined"!=typeof setImmediate?setImmediate:null;function b(e){for(var t=n(d);null!==t;){if(null===t.callback)i(d);else if(t.startTime<=e)i(d),t.sortIndex=t.expirationTime,r(u,t);else break;t=n(d)}}function y(e){if(v=!1,b(e),!m)if(null!==n(u))m=!0,T();else{var t=n(d);null!==t&&G(y,t.startTime-e)}}var E=!1,M=-1,w=5,x=-1;function I(){return!(t.unstable_now()-x<w)}function R(){if(E){var e=t.unstable_now();x=e;var r=!0;try{e:{m=!1,v&&(v=!1,B(M),M=-1),A=!0;var a=p;try{t:{for(b(e),h=n(u);null!==h&&!(h.expirationTime>e&&I());){var s=h.callback;if("function"==typeof s){h.callback=null,p=h.priorityLevel;var l=s(h.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof l){h.callback=l,b(e),r=!0;break t}h===n(u)&&i(u),b(e)}else i(u);h=n(u)}if(null!==h)r=!0;else{var c=n(d);null!==c&&G(y,c.startTime-e),r=!1}}break e}finally{h=null,p=a,A=!1}}}finally{r?o():E=!1}}}if("function"==typeof C)o=function(){C(R)};else if("undefined"!=typeof MessageChannel){var F=new MessageChannel,D=F.port2;F.port1.onmessage=R,o=function(){D.postMessage(null)}}else o=function(){g(R,0)};function T(){E||(E=!0,o())}function G(e,r){M=g(function(){e(t.unstable_now())},r)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||A||(m=!0,T())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):w=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return n(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var r=p;p=t;try{return e()}finally{p=r}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=p;p=e;try{return t()}finally{p=r}},t.unstable_scheduleCallback=function(e,i,a){var o=t.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?o+a:o,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=0x3fffffff;break;case 4:s=1e4;break;default:s=5e3}return s=a+s,e={id:f++,callback:i,priorityLevel:e,startTime:a,expirationTime:s,sortIndex:-1},a>o?(e.sortIndex=a,r(d,e),null===n(u)&&e===n(d)&&(v?(B(M),M=-1):v=!0,G(y,a-o))):(e.sortIndex=s,r(u,e),m||A||(m=!0,T())),e},t.unstable_shouldYield=I,t.unstable_wrapCallback=function(e){var t=p;return function(){var r=p;p=t;try{return e.apply(this,arguments)}finally{p=r}}}},7558:(e,t,r)=>{r.d(t,{Hl:()=>d});var n=r(461),i=r(2115),a=r(7431);function o(e,t){let r;return(...n)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...n),t)}}let s=["x","y","top","bottom","left","right","width","height"];var l=r(6354),c=r(5155);function u({ref:e,children:t,fallback:r,resize:l,style:u,gl:d,events:f=n.f,eventSource:h,eventPrefix:p,shadows:A,linear:m,flat:v,legacy:g,orthographic:B,frameloop:C,dpr:b,performance:y,raycaster:E,camera:M,scene:w,onPointerMissed:x,onCreated:I,...R}){i.useMemo(()=>(0,n.e)(a),[]);let F=(0,n.u)(),[D,T]=function({debounce:e,scroll:t,polyfill:r,offsetSize:n}={debounce:0,scroll:!1,offsetSize:!1}){var a,l,c;let u=r||("undefined"==typeof window?class{}:window.ResizeObserver);if(!u)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[d,f]=(0,i.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),h=(0,i.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:d,orientationHandler:null}),p=e?"number"==typeof e?e:e.scroll:null,A=e?"number"==typeof e?e:e.resize:null,m=(0,i.useRef)(!1);(0,i.useEffect)(()=>(m.current=!0,()=>void(m.current=!1)));let[v,g,B]=(0,i.useMemo)(()=>{let e=()=>{let e,t;if(!h.current.element)return;let{left:r,top:i,width:a,height:o,bottom:l,right:c,x:u,y:d}=h.current.element.getBoundingClientRect(),p={left:r,top:i,width:a,height:o,bottom:l,right:c,x:u,y:d};h.current.element instanceof HTMLElement&&n&&(p.height=h.current.element.offsetHeight,p.width=h.current.element.offsetWidth),Object.freeze(p),m.current&&(e=h.current.lastBounds,t=p,!s.every(r=>e[r]===t[r]))&&f(h.current.lastBounds=p)};return[e,A?o(e,A):e,p?o(e,p):e]},[f,n,p,A]);function C(){h.current.scrollContainers&&(h.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",B,!0)),h.current.scrollContainers=null),h.current.resizeObserver&&(h.current.resizeObserver.disconnect(),h.current.resizeObserver=null),h.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",h.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",h.current.orientationHandler))}function b(){h.current.element&&(h.current.resizeObserver=new u(B),h.current.resizeObserver.observe(h.current.element),t&&h.current.scrollContainers&&h.current.scrollContainers.forEach(e=>e.addEventListener("scroll",B,{capture:!0,passive:!0})),h.current.orientationHandler=()=>{B()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",h.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",h.current.orientationHandler))}return a=B,l=!!t,(0,i.useEffect)(()=>{if(l)return window.addEventListener("scroll",a,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",a,!0)},[a,l]),c=g,(0,i.useEffect)(()=>(window.addEventListener("resize",c),()=>void window.removeEventListener("resize",c)),[c]),(0,i.useEffect)(()=>{C(),b()},[t,B,g]),(0,i.useEffect)(()=>C,[]),[e=>{e&&e!==h.current.element&&(C(),h.current.element=e,h.current.scrollContainers=function e(t){let r=[];if(!t||t===document.body)return r;let{overflow:n,overflowX:i,overflowY:a}=window.getComputedStyle(t);return[n,i,a].some(e=>"auto"===e||"scroll"===e)&&r.push(t),[...r,...e(t.parentElement)]}(e),b())},d,v]}({scroll:!0,debounce:{scroll:50,resize:0},...l}),G=i.useRef(null),S=i.useRef(null);i.useImperativeHandle(e,()=>G.current);let P=(0,n.a)(x),[_,H]=i.useState(!1),[O,L]=i.useState(!1);if(_)throw _;if(O)throw O;let U=i.useRef(null);(0,n.b)(()=>{let e=G.current;T.width>0&&T.height>0&&e&&(U.current||(U.current=(0,n.c)(e)),async function(){await U.current.configure({gl:d,scene:w,events:f,shadows:A,linear:m,flat:v,legacy:g,orthographic:B,frameloop:C,dpr:b,performance:y,raycaster:E,camera:M,size:T,onPointerMissed:(...e)=>null==P.current?void 0:P.current(...e),onCreated:e=>{null==e.events.connect||e.events.connect(h?(0,n.i)(h)?h.current:h:S.current),p&&e.setEvents({compute:(e,t)=>{let r=e[p+"X"],n=e[p+"Y"];t.pointer.set(r/t.size.width*2-1,-(2*(n/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==I||I(e)}}),U.current.render((0,c.jsx)(F,{children:(0,c.jsx)(n.E,{set:L,children:(0,c.jsx)(i.Suspense,{fallback:(0,c.jsx)(n.B,{set:H}),children:null!=t?t:null})})}))}())}),i.useEffect(()=>{let e=G.current;if(e)return()=>(0,n.d)(e)},[]);let k=h?"none":"auto";return(0,c.jsx)("div",{ref:S,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:k,...u},...R,children:(0,c.jsx)("div",{ref:D,style:{width:"100%",height:"100%"},children:(0,c.jsx)("canvas",{ref:G,style:{display:"block"},children:r})})})}function d(e){return(0,c.jsx)(l.Af,{children:(0,c.jsx)(u,{...e})})}r(1933),r(5220),r(4342)},8247:(e,t,r)=>{e.exports=r(620)},8435:(e,t,r)=>{r.d(t,{_:()=>c});var n=r(9630),i=r(2115),a=r(3264),o=r(461);let s={uniforms:{tDiffuse:{value:null},h:{value:1/512}},vertexShader:`
      varying vec2 vUv;

      void main() {

        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );

      }
  `,fragmentShader:`
    uniform sampler2D tDiffuse;
    uniform float h;

    varying vec2 vUv;

    void main() {

    	vec4 sum = vec4( 0.0 );

    	sum += texture2D( tDiffuse, vec2( vUv.x - 4.0 * h, vUv.y ) ) * 0.051;
    	sum += texture2D( tDiffuse, vec2( vUv.x - 3.0 * h, vUv.y ) ) * 0.0918;
    	sum += texture2D( tDiffuse, vec2( vUv.x - 2.0 * h, vUv.y ) ) * 0.12245;
    	sum += texture2D( tDiffuse, vec2( vUv.x - 1.0 * h, vUv.y ) ) * 0.1531;
    	sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y ) ) * 0.1633;
    	sum += texture2D( tDiffuse, vec2( vUv.x + 1.0 * h, vUv.y ) ) * 0.1531;
    	sum += texture2D( tDiffuse, vec2( vUv.x + 2.0 * h, vUv.y ) ) * 0.12245;
    	sum += texture2D( tDiffuse, vec2( vUv.x + 3.0 * h, vUv.y ) ) * 0.0918;
    	sum += texture2D( tDiffuse, vec2( vUv.x + 4.0 * h, vUv.y ) ) * 0.051;

    	gl_FragColor = sum;

    }
  `},l={uniforms:{tDiffuse:{value:null},v:{value:1/512}},vertexShader:`
    varying vec2 vUv;

    void main() {

      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );

    }
  `,fragmentShader:`

  uniform sampler2D tDiffuse;
  uniform float v;

  varying vec2 vUv;

  void main() {

    vec4 sum = vec4( 0.0 );

    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 4.0 * v ) ) * 0.051;
    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 3.0 * v ) ) * 0.0918;
    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 2.0 * v ) ) * 0.12245;
    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 1.0 * v ) ) * 0.1531;
    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y ) ) * 0.1633;
    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 1.0 * v ) ) * 0.1531;
    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 2.0 * v ) ) * 0.12245;
    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 3.0 * v ) ) * 0.0918;
    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 4.0 * v ) ) * 0.051;

    gl_FragColor = sum;

  }
  `},c=i.forwardRef(({scale:e=10,frames:t=1/0,opacity:r=1,width:c=1,height:u=1,blur:d=1,near:f=0,far:h=10,resolution:p=512,smooth:A=!0,color:m="#000000",depthWrite:v=!1,renderOrder:g,...B},C)=>{let b,y,E=i.useRef(null),M=(0,o.C)(e=>e.scene),w=(0,o.C)(e=>e.gl),x=i.useRef(null);c*=Array.isArray(e)?e[0]:e||1,u*=Array.isArray(e)?e[1]:e||1;let[I,R,F,D,T,G,S]=i.useMemo(()=>{let e=new a.nWS(p,p),t=new a.nWS(p,p);t.texture.generateMipmaps=e.texture.generateMipmaps=!1;let r=new a.bdM(c,u).rotateX(Math.PI/2),n=new a.eaF(r),i=new a.CSG;i.depthTest=i.depthWrite=!1,i.onBeforeCompile=e=>{e.uniforms={...e.uniforms,ucolor:{value:new a.Q1f(m)}},e.fragmentShader=e.fragmentShader.replace("void main() {",`uniform vec3 ucolor;
           void main() {
          `),e.fragmentShader=e.fragmentShader.replace("vec4( vec3( 1.0 - fragCoordZ ), opacity );","vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );")};let o=new a.BKk(s),d=new a.BKk(l);return d.depthTest=o.depthTest=!1,[e,r,i,n,o,d,t]},[p,c,u,e,m]),P=e=>{D.visible=!0,D.material=T,T.uniforms.tDiffuse.value=I.texture,T.uniforms.h.value=e/256,w.setRenderTarget(S),w.render(D,x.current),D.material=G,G.uniforms.tDiffuse.value=S.texture,G.uniforms.v.value=e/256,w.setRenderTarget(I),w.render(D,x.current),D.visible=!1},_=0;return(0,o.D)(()=>{x.current&&(t===1/0||_<t)&&(_++,b=M.background,y=M.overrideMaterial,E.current.visible=!1,M.background=null,M.overrideMaterial=F,w.setRenderTarget(I),w.render(M,x.current),P(d),A&&P(.4*d),w.setRenderTarget(null),E.current.visible=!0,M.overrideMaterial=y,M.background=b)}),i.useImperativeHandle(C,()=>E.current,[]),i.createElement("group",(0,n.A)({"rotation-x":Math.PI/2},B,{ref:E}),i.createElement("mesh",{renderOrder:g,geometry:R,scale:[1,-1,1],rotation:[-Math.PI/2,0,0]},i.createElement("meshBasicMaterial",{transparent:!0,map:I.texture,opacity:r,depthWrite:v})),i.createElement("orthographicCamera",{ref:x,args:[-c/2,c/2,u/2,-u/2,f,h]}))})},8842:(e,t,r)=>{r.d(t,{E:()=>l});var n=r(9630),i=r(2115),a=r(1966),o=r(461),s=r(228);let l=i.forwardRef(({sdfGlyphSize:e=64,anchorX:t="center",anchorY:r="middle",font:l,fontSize:c=1,children:u,characters:d,onSync:f,...h},p)=>{let A=(0,o.C)(({invalidate:e})=>e),[m]=i.useState(()=>new a.EY),[v,g]=i.useMemo(()=>{let e=[],t="";return i.Children.forEach(u,r=>{"string"==typeof r||"number"==typeof r?t+=r:e.push(r)}),[e,t]},[u]);return(0,s.DY)(()=>new Promise(e=>(0,a.PY)({font:l,characters:d},e)),["troika-text",l,d]),i.useLayoutEffect(()=>void m.sync(()=>{A(),f&&f(m)})),i.useEffect(()=>()=>m.dispose(),[m]),i.createElement("primitive",(0,n.A)({object:m,ref:p,font:l,text:g,anchorX:t,anchorY:r,fontSize:c,sdfGlyphSize:e},h),v)})},9630:(e,t,r)=>{r.d(t,{A:()=>n});function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}},9957:(e,t,r)=>{let n,i;r.d(t,{E:()=>B});var a=r(9630),o=r(2115),s=r(2669),l=r(3264),c=r(461);let u=new l.Pq0,d=new l.Pq0,f=new l.Pq0,h=new l.I9Y;function p(e,t,r){let n=u.setFromMatrixPosition(e.matrixWorld);n.project(t);let i=r.width/2,a=r.height/2;return[n.x*i+i,-(n.y*a)+a]}let A=e=>1e-10>Math.abs(e)?0:e;function m(e,t,r=""){let n="matrix3d(";for(let r=0;16!==r;r++)n+=A(t[r]*e.elements[r])+(15!==r?",":")");return r+n}let v=(n=[1,-1,1,1,1,-1,1,1,1,-1,1,1,1,-1,1,1],e=>m(e,n)),g=(i=e=>[1/e,1/e,1/e,1,-1/e,-1/e,-1/e,-1,1/e,1/e,1/e,1,1,1,1,1],(e,t)=>m(e,i(t),"translate(-50%,-50%)")),B=o.forwardRef(({children:e,eps:t=.001,style:r,className:n,prepend:i,center:m,fullscreen:B,portal:C,distanceFactor:b,sprite:y=!1,transform:E=!1,occlude:M,onOcclude:w,castShadow:x,receiveShadow:I,material:R,geometry:F,zIndexRange:D=[0x1000037,0],calculatePosition:T=p,as:G="div",wrapperClass:S,pointerEvents:P="auto",..._},H)=>{let{gl:O,camera:L,scene:U,size:k,raycaster:J,events:j,viewport:N}=(0,c.C)(),[K]=o.useState(()=>document.createElement(G)),Q=o.useRef(null),X=o.useRef(null),Y=o.useRef(0),W=o.useRef([0,0]),z=o.useRef(null),Z=o.useRef(null),q=(null==C?void 0:C.current)||j.connected||O.domElement.parentNode,V=o.useRef(null),$=o.useRef(!1),ee=o.useMemo(()=>M&&"blending"!==M||Array.isArray(M)&&M.length&&function(e){return e&&"object"==typeof e&&"current"in e}(M[0]),[M]);o.useLayoutEffect(()=>{let e=O.domElement;M&&"blending"===M?(e.style.zIndex=`${Math.floor(D[0]/2)}`,e.style.position="absolute",e.style.pointerEvents="none"):(e.style.zIndex=null,e.style.position=null,e.style.pointerEvents=null)},[M]),o.useLayoutEffect(()=>{if(X.current){let e=Q.current=s.createRoot(K);if(U.updateMatrixWorld(),E)K.style.cssText="position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;";else{let e=T(X.current,L,k);K.style.cssText=`position:absolute;top:0;left:0;transform:translate3d(${e[0]}px,${e[1]}px,0);transform-origin:0 0;`}return q&&(i?q.prepend(K):q.appendChild(K)),()=>{q&&q.removeChild(K),e.unmount()}}},[q,E]),o.useLayoutEffect(()=>{S&&(K.className=S)},[S]);let et=o.useMemo(()=>E?{position:"absolute",top:0,left:0,width:k.width,height:k.height,transformStyle:"preserve-3d",pointerEvents:"none"}:{position:"absolute",transform:m?"translate3d(-50%,-50%,0)":"none",...B&&{top:-k.height/2,left:-k.width/2,width:k.width,height:k.height},...r},[r,m,B,k,E]),er=o.useMemo(()=>({position:"absolute",pointerEvents:P}),[P]);o.useLayoutEffect(()=>{var t,i;$.current=!1,E?null==(t=Q.current)||t.render(o.createElement("div",{ref:z,style:et},o.createElement("div",{ref:Z,style:er},o.createElement("div",{ref:H,className:n,style:r,children:e})))):null==(i=Q.current)||i.render(o.createElement("div",{ref:H,style:et,className:n,children:e}))});let en=o.useRef(!0);(0,c.D)(e=>{if(X.current){L.updateMatrixWorld(),X.current.updateWorldMatrix(!0,!1);let e=E?W.current:T(X.current,L,k);if(E||Math.abs(Y.current-L.zoom)>t||Math.abs(W.current[0]-e[0])>t||Math.abs(W.current[1]-e[1])>t){let t=function(e,t){let r=u.setFromMatrixPosition(e.matrixWorld),n=d.setFromMatrixPosition(t.matrixWorld),i=r.sub(n),a=t.getWorldDirection(f);return i.angleTo(a)>Math.PI/2}(X.current,L),r=!1;ee&&(Array.isArray(M)?r=M.map(e=>e.current):"blending"!==M&&(r=[U]));let n=en.current;r?en.current=function(e,t,r,n){let i=u.setFromMatrixPosition(e.matrixWorld),a=i.clone();a.project(t),h.set(a.x,a.y),r.setFromCamera(h,t);let o=r.intersectObjects(n,!0);if(o.length){let e=o[0].distance;return i.distanceTo(r.ray.origin)<e}return!0}(X.current,L,J,r)&&!t:en.current=!t,n!==en.current&&(w?w(!en.current):K.style.display=en.current?"block":"none");let i=Math.floor(D[0]/2),a=M?ee?[D[0],i]:[i-1,0]:D;if(K.style.zIndex=`${function(e,t,r){if(t instanceof l.ubm||t instanceof l.qUd){let n=u.setFromMatrixPosition(e.matrixWorld),i=d.setFromMatrixPosition(t.matrixWorld),a=n.distanceTo(i),o=(r[1]-r[0])/(t.far-t.near),s=r[1]-o*t.far;return Math.round(o*a+s)}}(X.current,L,a)}`,E){let[e,t]=[k.width/2,k.height/2],r=L.projectionMatrix.elements[5]*t,{isOrthographicCamera:n,top:i,left:a,bottom:o,right:s}=L,l=v(L.matrixWorldInverse),c=n?`scale(${r})translate(${A(-(s+a)/2)}px,${A((i+o)/2)}px)`:`translateZ(${r}px)`,u=X.current.matrixWorld;y&&((u=L.matrixWorldInverse.clone().transpose().copyPosition(u).scale(X.current.scale)).elements[3]=u.elements[7]=u.elements[11]=0,u.elements[15]=1),K.style.width=k.width+"px",K.style.height=k.height+"px",K.style.perspective=n?"":`${r}px`,z.current&&Z.current&&(z.current.style.transform=`${c}${l}translate(${e}px,${t}px)`,Z.current.style.transform=g(u,1/((b||10)/400)))}else{let t=void 0===b?1:function(e,t){if(t instanceof l.qUd)return t.zoom;if(!(t instanceof l.ubm))return 1;{let r=u.setFromMatrixPosition(e.matrixWorld),n=d.setFromMatrixPosition(t.matrixWorld);return 1/(2*Math.tan(t.fov*Math.PI/180/2)*r.distanceTo(n))}}(X.current,L)*b;K.style.transform=`translate3d(${e[0]}px,${e[1]}px,0) scale(${t})`}W.current=e,Y.current=L.zoom}}if(!ee&&V.current&&!$.current)if(E){if(z.current){let e=z.current.children[0];if(null!=e&&e.clientWidth&&null!=e&&e.clientHeight){let{isOrthographicCamera:t}=L;if(t||F)_.scale&&(Array.isArray(_.scale)?_.scale instanceof l.Pq0?V.current.scale.copy(_.scale.clone().divideScalar(1)):V.current.scale.set(1/_.scale[0],1/_.scale[1],1/_.scale[2]):V.current.scale.setScalar(1/_.scale));else{let t=(b||10)/400,r=e.clientWidth*t,n=e.clientHeight*t;V.current.scale.set(r,n,1)}$.current=!0}}}else{let t=K.children[0];if(null!=t&&t.clientWidth&&null!=t&&t.clientHeight){let e=1/N.factor,r=t.clientWidth*e,n=t.clientHeight*e;V.current.scale.set(r,n,1),$.current=!0}V.current.lookAt(e.camera.position)}});let ei=o.useMemo(()=>({vertexShader:E?void 0:`
          /*
            This shader is from the THREE's SpriteMaterial.
            We need to turn the backing plane into a Sprite
            (make it always face the camera) if "transfrom"
            is false.
          */
          #include <common>

          void main() {
            vec2 center = vec2(0., 1.);
            float rotation = 0.0;

            // This is somewhat arbitrary, but it seems to work well
            // Need to figure out how to derive this dynamically if it even matters
            float size = 0.03;

            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );
            vec2 scale;
            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );
            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );

            bool isPerspective = isPerspectiveMatrix( projectionMatrix );
            if ( isPerspective ) scale *= - mvPosition.z;

            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;
            vec2 rotatedPosition;
            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;
            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;
            mvPosition.xy += rotatedPosition;

            gl_Position = projectionMatrix * mvPosition;
          }
      `,fragmentShader:`
        void main() {
          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);
        }
      `}),[E]);return o.createElement("group",(0,a.A)({},_,{ref:X}),M&&!ee&&o.createElement("mesh",{castShadow:x,receiveShadow:I,ref:V},F||o.createElement("planeGeometry",null),R||o.createElement("shaderMaterial",{side:l.$EB,vertexShader:ei.vertexShader,fragmentShader:ei.fragmentShader})))})}}]);