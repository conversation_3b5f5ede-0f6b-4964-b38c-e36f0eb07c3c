'use client';

// 3D Experience component with Canvas, lighting, and camera controls

import { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import {
  Environment,
  Html,
  useProgress,
  OrbitControls,
  PerspectiveCamera,
  ContactShadows
} from '@react-three/drei';
import TeacherModel from './TeacherModel';
import Blackboard from './Blackboard';
import { useBlackboardText } from '@/store/useAITeacher';

// Loading component
function Loader() {
  const { progress } = useProgress();
  
  return (
    <Html center>
      <div className="flex flex-col items-center space-y-4">
        <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        <div className="text-center">
          <p className="text-lg font-medium text-gray-900">Loading AI Teacher</p>
          <p className="text-sm text-gray-600">{Math.round(progress)}% complete</p>
        </div>
      </div>
    </Html>
  );
}

// Classroom environment setup
function ClassroomEnvironment() {
  return (
    <>
      {/* Lighting setup */}
      <ambientLight intensity={0.4} />
      <directionalLight
        position={[5, 5, 5]}
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      <pointLight position={[-5, 5, 5]} intensity={0.5} />
      
      {/* Environment map for reflections */}
      <Environment preset="studio" />
      
      {/* Ground/Floor */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]} receiveShadow>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial color="#f0f0f0" />
      </mesh>
      
      {/* Contact shadows for better grounding */}
      <ContactShadows
        position={[0, -1.99, 0]}
        opacity={0.4}
        scale={10}
        blur={2}
        far={4}
      />
    </>
  );
}

// Camera setup component
function CameraSetup() {
  return (
    <PerspectiveCamera
      makeDefault
      position={[0, 0, 5]}
      fov={50}
      near={0.1}
      far={1000}
    />
  );
}

// Main Experience component
interface ExperienceProps {
  enableControls?: boolean;
  showBlackboard?: boolean;
  className?: string;
}

export default function Experience({ 
  enableControls = true, 
  showBlackboard = true,
  className = "w-full h-full"
}: ExperienceProps) {
  const blackboardText = useBlackboardText();

  return (
    <div className={className}>
      <Canvas
        shadows
        camera={{ position: [0, 0, 5], fov: 50 }}
        gl={{ 
          antialias: true, 
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        <Suspense fallback={<Loader />}>
          {/* Camera setup */}
          <CameraSetup />
          
          {/* Camera controls */}
          {enableControls && (
            <OrbitControls
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              minDistance={2}
              maxDistance={10}
              minPolarAngle={Math.PI / 6}
              maxPolarAngle={Math.PI / 2}
              target={[0, 0, 0]}
            />
          )}
          
          {/* Environment and lighting */}
          <ClassroomEnvironment />
          
          {/* Teacher model */}
          <TeacherModel position={[0, -2, 0]} />
          
          {/* Blackboard */}
          {showBlackboard && (
            <Blackboard 
              position={[0, 1, -3]} 
              text={blackboardText}
            />
          )}
          
          {/* Additional classroom elements */}
          <group position={[3, -1, -2]}>
            {/* Desk */}
            <mesh castShadow>
              <boxGeometry args={[2, 0.1, 1]} />
              <meshStandardMaterial color="#8B4513" />
            </mesh>
            {/* Desk legs */}
            {[[-0.8, -0.5, -0.4], [0.8, -0.5, -0.4], [-0.8, -0.5, 0.4], [0.8, -0.5, 0.4]].map((pos, i) => (
              <mesh key={i} position={pos as [number, number, number]} castShadow>
                <cylinderGeometry args={[0.05, 0.05, 1]} />
                <meshStandardMaterial color="#654321" />
              </mesh>
            ))}
          </group>
          
          {/* Bookshelf */}
          <group position={[-3, 0, -2]}>
            <mesh castShadow>
              <boxGeometry args={[1, 3, 0.3]} />
              <meshStandardMaterial color="#8B4513" />
            </mesh>
            {/* Books */}
            {Array.from({ length: 8 }, (_, i) => (
              <mesh 
                key={i} 
                position={[
                  -0.3 + (i % 4) * 0.2, 
                  -1 + Math.floor(i / 4) * 0.5, 
                  0.2
                ]} 
                castShadow
              >
                <boxGeometry args={[0.15, 0.4, 0.05]} />
                <meshStandardMaterial 
                  color={`hsl(${i * 45}, 70%, 50%)`} 
                />
              </mesh>
            ))}
          </group>
        </Suspense>
      </Canvas>
    </div>
  );
}
