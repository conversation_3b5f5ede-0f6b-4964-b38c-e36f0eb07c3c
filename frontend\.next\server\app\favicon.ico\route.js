const CHUNK_PUBLIC_PATH = "server/app/favicon.ico/route.js";
const runtime = require("../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/27da8_next_2a0e2838._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__1a81fbce._.js");
runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/.next-internal/server/app/favicon.ico/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Desktop/AI/ai/frontend/src/app/favicon--route-entry.js [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Desktop/AI/ai/frontend/src/app/favicon--route-entry.js [app-rsc] (ecmascript)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
