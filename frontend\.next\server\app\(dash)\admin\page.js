(()=>{var a={};a.id=336,a.ids=[336],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9355:(a,b,c)=>{Promise.resolve().then(c.bind(c,68065))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},58648:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI\\\\ai\\\\frontend\\\\src\\\\app\\\\(dash)\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\admin\\page.tsx","default")},62498:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dash)",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,58648)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,73035)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a),async a=>(await Promise.resolve().then(c.bind(c,59645))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a),async a=>(await Promise.resolve().then(c.bind(c,59645))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\admin\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dash)/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dash)/admin/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68065:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x});var d=c(60687),e=c(43210),f=c(82136),g=c(16189),h=c(44493),i=c(29523),j=c(96834),k=c(62688);let l=(0,k.A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),m=(0,k.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),n=(0,k.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),o=(0,k.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var p=c(41312),q=c(27351),r=c(93508),s=c(82080),t=c(53411),u=c(84027);let v=(0,k.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var w=c(90631);function x(){let{data:a}=(0,f.useSession)(),b=(0,g.useRouter)(),c=(0,w.st)(),[k,x]=(0,e.useState)(!0);return k?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Loading admin dashboard..."})]})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-red-600 to-pink-600 rounded-lg p-6 text-white",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"System Administration"}),(0,d.jsxs)("p",{className:"text-red-100",children:["Welcome, ",a?.user?.name,"! Manage the entire AI Tutor Platform from here."]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,d.jsx)(l,{className:"h-5 w-5 text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"System Status"}),(0,d.jsx)("p",{className:"text-lg font-bold text-green-600",children:"Healthy"})]})]})})}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(m,{className:"h-5 w-5 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Uptime"}),(0,d.jsx)("p",{className:"text-lg font-bold",children:"99.9%"})]})]})})}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,d.jsx)(n,{className:"h-5 w-5 text-purple-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Storage Used"}),(0,d.jsx)("p",{className:"text-lg font-bold",children:"2.4 TB"})]})]})})}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,d.jsx)(o,{className:"h-5 w-5 text-orange-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Security"}),(0,d.jsx)("p",{className:"text-lg font-bold text-green-600",children:"Secure"})]})]})})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(p.A,{className:"h-5 w-5 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Total Users"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:c?.totalUsers||150})]})]})})}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,d.jsx)(q.A,{className:"h-5 w-5 text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Students"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:c?.totalStudents||120})]})]})})}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,d.jsx)(r.A,{className:"h-5 w-5 text-purple-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Teachers"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:c?.totalTeachers||25})]})]})})}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,d.jsx)(s.A,{className:"h-5 w-5 text-orange-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Organizations"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:"12"})]})]})})})]}),(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"Administrative Actions"}),(0,d.jsx)(h.BT,{children:"System management and configuration"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsxs)(i.$,{onClick:()=>b.push("/admin/users"),className:"h-20 flex-col space-y-2",children:[(0,d.jsx)(p.A,{className:"h-6 w-6"}),(0,d.jsx)("span",{children:"Manage Users"})]}),(0,d.jsxs)(i.$,{onClick:()=>b.push("/admin/organizations"),variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,d.jsx)(s.A,{className:"h-6 w-6"}),(0,d.jsx)("span",{children:"Organizations"})]}),(0,d.jsxs)(i.$,{onClick:()=>b.push("/admin/analytics"),variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,d.jsx)(t.A,{className:"h-6 w-6"}),(0,d.jsx)("span",{children:"Global Analytics"})]}),(0,d.jsxs)(i.$,{onClick:()=>b.push("/admin/settings"),variant:"outline",className:"h-20 flex-col space-y-2",children:[(0,d.jsx)(u.A,{className:"h-6 w-6"}),(0,d.jsx)("span",{children:"System Settings"})]})]})})]}),(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsxs)(h.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(v,{className:"h-5 w-5 text-yellow-600"}),(0,d.jsx)("span",{children:"System Alerts"})]}),(0,d.jsx)(h.BT,{children:"Important system notifications and warnings"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,d.jsx)(v,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"font-medium",children:"High Storage Usage"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Storage is at 85% capacity. Consider upgrading or cleaning up old files."}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Detected 1 hour ago"})]}),(0,d.jsx)(j.E,{variant:"secondary",children:"Warning"})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,d.jsx)(o,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"font-medium",children:"Security Update Applied"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Latest security patches have been successfully installed."}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Completed 2 hours ago"})]}),(0,d.jsx)(j.E,{variant:"outline",children:"Success"})]})]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"Recent User Activity"}),(0,d.jsx)(h.BT,{children:"Latest user registrations and activities"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"S"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:"New student registered"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Sarah Johnson - Tokyo University"})]})]}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"5 min ago"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"T"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:"Teacher created new subject"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Advanced Physics - Dr. Smith"})]})]}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"1 hour ago"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold",children:"H"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:"HOD sent announcement"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"System maintenance notice"})]})]}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"3 hours ago"})]})]})})]}),(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"System Performance"}),(0,d.jsx)(h.BT,{children:"Key performance metrics"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"API Response Time"}),(0,d.jsx)("span",{className:"font-bold text-green-600",children:"145ms"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Database Queries/sec"}),(0,d.jsx)("span",{className:"font-bold",children:"2,340"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Active Sessions"}),(0,d.jsx)("span",{className:"font-bold",children:"89"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Error Rate"}),(0,d.jsx)("span",{className:"font-bold text-green-600",children:"0.02%"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"Cache Hit Rate"}),(0,d.jsx)("span",{className:"font-bold text-green-600",children:"94.5%"})]})]})})]})]}),(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"Organizations Overview"}),(0,d.jsx)(h.BT,{children:"Institutions using the platform"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsx)("div",{className:"space-y-3",children:[{name:"Tokyo University",users:45,status:"Active",plan:"Premium"},{name:"Osaka Institute",users:32,status:"Active",plan:"Standard"},{name:"Kyoto Academy",users:28,status:"Active",plan:"Premium"},{name:"Nagoya School",users:19,status:"Trial",plan:"Basic"}].map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold",children:a.name.charAt(0)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:a.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[a.users," users"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(j.E,{variant:"Active"===a.status?"default":"secondary",children:a.status}),(0,d.jsx)(j.E,{variant:"outline",children:a.plan})]})]},a.name))})})]})]})}},72403:(a,b,c)=>{Promise.resolve().then(c.bind(c,58648))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,653,393,484,192,343],()=>b(b.s=62498));module.exports=c})();