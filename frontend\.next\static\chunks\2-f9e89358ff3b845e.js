"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2],{381:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1497:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("message-square",[["path",{d:"M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z",key:"18887p"}]])},2421:(e,t,r)=>{r.d(t,{y:()=>n});let a=e=>{let t,r=new Set,a=(e,a)=>{let n="function"==typeof e?e(t):e;if(!Object.is(n,t)){let e=t;t=(null!=a?a:"object"!=typeof n||null===n)?n:Object.assign({},t,n),r.forEach(r=>r(t,e))}},n=()=>t,s={setState:a,getState:n,getInitialState:()=>i,subscribe:e=>(r.add(e),()=>r.delete(e))},i=t=e(a,n,s);return s},n=e=>e?a(e):a},2657:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3717:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4616:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5040:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5521:(e,t,r)=>{r.d(t,{v:()=>i});var a=r(2115),n=r(2421);let s=e=>{let t=(0,n.y)(e),r=e=>(function(e,t=e=>e){let r=a.useSyncExternalStore(e.subscribe,a.useCallback(()=>t(e.getState()),[e,t]),a.useCallback(()=>t(e.getInitialState()),[e,t]));return a.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},i=e=>e?s(e):s},5657:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},6786:(e,t,r)=>{r.d(t,{Zr:()=>o,lt:()=>s});let a=new Map,n=e=>{let t=a.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},s=(e,t={})=>(r,s,l)=>{let o,{enabled:d,anonymousActionType:u,store:c,...h}=t;try{o=(null==d||d)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!o)return e(r,s,l);let{connection:p,...y}=((e,t,r)=>{if(void 0===e)return{type:"untracked",connection:t.connect(r)};let n=a.get(r.name);if(n)return{type:"tracked",store:e,...n};let s={connection:t.connect(r),stores:{}};return a.set(r.name,s),{type:"tracked",store:e,...s}})(c,o,h),v=!0;l.setState=(e,t,a)=>{let i=r(e,t);if(!v)return i;let o=void 0===a?{type:u||(e=>{var t,r;if(!e)return;let a=e.split("\n"),n=a.findIndex(e=>e.includes("api.setState"));if(n<0)return;let s=(null==(t=a[n+1])?void 0:t.trim())||"";return null==(r=/.+ (.+) .+/.exec(s))?void 0:r[1]})(Error().stack)||"anonymous"}:"string"==typeof a?{type:a}:a;return void 0===c?null==p||p.send(o,s()):null==p||p.send({...o,type:`${c}/${o.type}`},{...n(h.name),[c]:l.getState()}),i},l.devtools={cleanup:()=>{p&&"function"==typeof p.unsubscribe&&p.unsubscribe(),((e,t)=>{if(void 0===t)return;let r=a.get(e);r&&(delete r.stores[t],0===Object.keys(r.stores).length&&a.delete(e))})(h.name,c)}};let f=(...e)=>{let t=v;v=!1,r(...e),v=t},g=e(l.setState,s,l);if("untracked"===y.type?null==p||p.init(g):(y.stores[y.store]=l,null==p||p.init(Object.fromEntries(Object.entries(y.stores).map(([e,t])=>[e,e===y.store?g:t.getState()])))),l.dispatchFromDevtools&&"function"==typeof l.dispatch){let e=!1,t=l.dispatch;l.dispatch=(...r)=>{"__setState"!==r[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...r)}}return p.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return i(e.payload,e=>{if("__setState"===e.type){if(void 0===c)return void f(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[c];return void(null==t||JSON.stringify(l.getState())!==JSON.stringify(t)&&f(t))}l.dispatchFromDevtools&&"function"==typeof l.dispatch&&l.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(f(g),void 0===c)return null==p?void 0:p.init(l.getState());return null==p?void 0:p.init(n(h.name));case"COMMIT":if(void 0===c){null==p||p.init(l.getState());break}return null==p?void 0:p.init(n(h.name));case"ROLLBACK":return i(e.state,e=>{if(void 0===c){f(e),null==p||p.init(l.getState());return}f(e[c]),null==p||p.init(n(h.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return i(e.state,e=>{if(void 0===c)return void f(e);JSON.stringify(l.getState())!==JSON.stringify(e[c])&&f(e[c])});case"IMPORT_STATE":{let{nextLiftedState:r}=e.payload,a=null==(t=r.computedStates.slice(-1)[0])?void 0:t.state;if(!a)return;void 0===c?f(a):f(a[c]),null==p||p.send(null,r);break}case"PAUSE_RECORDING":return v=!v}return}}),g},i=(e,t)=>{let r;try{r=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==r&&t(r)},l=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>l(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>l(t)(e)}}},o=(e,t)=>(r,a,n)=>{let s,i={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let a=e=>null===e?null:JSON.parse(e,void 0),n=null!=(t=r.getItem(e))?t:null;return n instanceof Promise?n.then(a):a(n)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},o=!1,d=new Set,u=new Set,c=i.storage;if(!c)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...e)},a,n);let h=()=>{let e=i.partialize({...a()});return c.setItem(i.name,{state:e,version:i.version})},p=n.setState;n.setState=(e,t)=>{p(e,t),h()};let y=e((...e)=>{r(...e),h()},a,n);n.getInitialState=()=>y;let v=()=>{var e,t;if(!c)return;o=!1,d.forEach(e=>{var t;return e(null!=(t=a())?t:y)});let n=(null==(t=i.onRehydrateStorage)?void 0:t.call(i,null!=(e=a())?e:y))||void 0;return l(c.getItem.bind(c))(i.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===i.version)return[!1,e.state];else{if(i.migrate){let t=i.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[n,l]=e;if(r(s=i.merge(l,null!=(t=a())?t:y),!0),n)return h()}).then(()=>{null==n||n(s,void 0),s=a(),o=!0,u.forEach(e=>e(s))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{i={...i,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>v(),hasHydrated:()=>o,onHydrate:e=>(d.add(e),()=>{d.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},i.skipHydration||v(),s||y}},7434:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7580:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9869:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}}]);