module.exports = {

"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Desktop/AI/ai/frontend/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/27da8_9c6fbf32._.js",
  "build/chunks/[root-of-the-server]__0eb92213._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Desktop/AI/ai/frontend/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),

};