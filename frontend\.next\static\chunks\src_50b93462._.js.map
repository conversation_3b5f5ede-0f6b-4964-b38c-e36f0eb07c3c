{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/lib/api.ts"], "sourcesContent": ["// API utility functions for the AI Tutor Platform\n\nimport {\n  User,\n  Subject,\n  StudyMaterial,\n  ChatMessage,\n  TeacherAvatar,\n  Announcement,\n  Analytics,\n  ApiResponse,\n  PaginatedResponse,\n  S3SignedUrl,\n  StreamingResponse,\n  Viseme\n} from '@/types';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1';\n\n// Generic API request function\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<ApiResponse<T>> {\n  try {\n    const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('API request failed:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred',\n    };\n  }\n}\n\n// Authentication APIs\nexport const authApi = {\n  // Get current user profile\n  getMe: (): Promise<ApiResponse<User>> => \n    apiRequest<User>('/me'),\n\n  // Update user profile\n  updateProfile: (data: Partial<User>): Promise<ApiResponse<User>> =>\n    apiRequest<User>('/me', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n};\n\n// Subject APIs\nexport const subjectApi = {\n  // Get all subjects for current user\n  getSubjects: (): Promise<ApiResponse<Subject[]>> =>\n    apiRequest<Subject[]>('/subjects'),\n\n  // Get subject by ID\n  getSubject: (id: string): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>(`/subjects/${id}`),\n\n  // Create new subject (teacher/admin only)\n  createSubject: (data: Omit<Subject, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>('/subjects', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update subject (teacher/admin only)\n  updateSubject: (id: string, data: Partial<Subject>): Promise<ApiResponse<Subject>> =>\n    apiRequest<Subject>(`/subjects/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete subject (teacher/admin only)\n  deleteSubject: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/subjects/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Study Material APIs\nexport const materialApi = {\n  // Get materials for a subject\n  getMaterials: (subjectId: string): Promise<ApiResponse<StudyMaterial[]>> =>\n    apiRequest<StudyMaterial[]>(`/materials?subjectId=${subjectId}`),\n\n  // Upload new material\n  uploadMaterial: (data: FormData): Promise<ApiResponse<StudyMaterial>> =>\n    fetch(`${API_BASE_URL}/materials`, {\n      method: 'POST',\n      body: data,\n    }).then(res => res.json()),\n\n  // Delete material\n  deleteMaterial: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/materials/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Chat APIs\nexport const chatApi = {\n  // Send message and get streaming response\n  sendMessage: async function* (\n    message: string,\n    subjectId?: string\n  ): AsyncGenerator<StreamingResponse, void, unknown> {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ message, subjectId }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new Error('No response body');\n    }\n\n    const decoder = new TextDecoder();\n    let buffer = '';\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') {\n              return;\n            }\n            try {\n              const parsed: StreamingResponse = JSON.parse(data);\n              yield parsed;\n            } catch (e) {\n              console.error('Failed to parse SSE data:', e);\n            }\n          }\n        }\n      }\n    } finally {\n      reader.releaseLock();\n    }\n  },\n\n  // Get chat history\n  getChatHistory: (subjectId?: string): Promise<ApiResponse<ChatMessage[]>> =>\n    apiRequest<ChatMessage[]>(`/chat/history${subjectId ? `?subjectId=${subjectId}` : ''}`),\n};\n\n// Text-to-Speech APIs\nexport const ttsApi = {\n  // Get TTS audio and visemes\n  getTTS: (messageId: string): Promise<ApiResponse<{ audioUrl: string; visemes: Viseme[] }>> =>\n    apiRequest<{ audioUrl: string; visemes: Viseme[] }>(`/tts?id=${messageId}`),\n};\n\n// Avatar APIs\nexport const avatarApi = {\n  // Get teacher avatar configuration\n  getAvatar: (teacherId?: string): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>(`/avatar${teacherId ? `?teacherId=${teacherId}` : ''}`),\n\n  // Save teacher avatar configuration\n  saveAvatar: (data: Omit<TeacherAvatar, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>('/avatar', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update avatar configuration\n  updateAvatar: (id: string, data: Partial<TeacherAvatar>): Promise<ApiResponse<TeacherAvatar>> =>\n    apiRequest<TeacherAvatar>(`/avatar/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n};\n\n// S3 Upload APIs\nexport const s3Api = {\n  // Get signed URL for file upload\n  getSignedUrl: (fileName: string, fileType: string): Promise<ApiResponse<S3SignedUrl>> =>\n    apiRequest<S3SignedUrl>('/s3/sign', {\n      method: 'POST',\n      body: JSON.stringify({ fileName, fileType }),\n    }),\n\n  // Upload file to S3 using signed URL\n  uploadFile: async (file: File, signedUrl: S3SignedUrl): Promise<boolean> => {\n    try {\n      const formData = new FormData();\n      Object.entries(signedUrl.fields).forEach(([key, value]) => {\n        formData.append(key, value);\n      });\n      formData.append('file', file);\n\n      const response = await fetch(signedUrl.url, {\n        method: 'POST',\n        body: formData,\n      });\n\n      return response.ok;\n    } catch (error) {\n      console.error('S3 upload failed:', error);\n      return false;\n    }\n  },\n};\n\n// Announcement APIs\nexport const announcementApi = {\n  // Get announcements\n  getAnnouncements: (): Promise<ApiResponse<Announcement[]>> =>\n    apiRequest<Announcement[]>('/announcements'),\n\n  // Create announcement (HOD/Admin only)\n  createAnnouncement: (data: Omit<Announcement, 'id' | 'authorId' | 'authorName' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Announcement>> =>\n    apiRequest<Announcement>('/announcements', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete announcement\n  deleteAnnouncement: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/announcements/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Analytics APIs\nexport const analyticsApi = {\n  // Get analytics data (HOD/Admin only)\n  getAnalytics: (): Promise<ApiResponse<Analytics>> =>\n    apiRequest<Analytics>('/analytics'),\n\n  // Get user analytics\n  getUserAnalytics: (userId?: string): Promise<ApiResponse<Record<string, unknown>>> =>\n    apiRequest<Record<string, unknown>>(`/analytics/users${userId ? `/${userId}` : ''}`),\n};\n\n// User Management APIs (Admin only)\nexport const userApi = {\n  // Get all users\n  getUsers: async (page = 1, limit = 20): Promise<PaginatedResponse<User>> => {\n    const response = await apiRequest<User[]>(`/users?page=${page}&limit=${limit}`);\n    // Transform to paginated response format\n    return {\n      success: response.success,\n      data: response.data || [],\n      pagination: {\n        page,\n        limit,\n        total: response.data?.length || 0,\n        totalPages: Math.ceil((response.data?.length || 0) / limit)\n      }\n    };\n  },\n\n  // Create user\n  createUser: (data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<User>> =>\n    apiRequest<User>('/users', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    }),\n\n  // Update user\n  updateUser: (id: string, data: Partial<User>): Promise<ApiResponse<User>> =>\n    apiRequest<User>(`/users/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    }),\n\n  // Delete user\n  deleteUser: (id: string): Promise<ApiResponse<void>> =>\n    apiRequest<void>(`/users/${id}`, {\n      method: 'DELETE',\n    }),\n};\n\n// Mock data for development (remove when backend is ready)\nexport const mockData = {\n  user: {\n    id: '1',\n    name: 'John Doe',\n    email: '<EMAIL>',\n    role: 'student' as const,\n    orgId: 'org1',\n    createdAt: new Date(),\n    updatedAt: new Date(),\n  },\n  subjects: [\n    {\n      id: '1',\n      name: 'Japanese Language',\n      description: 'Learn Japanese with AI assistance',\n      teacherId: 'teacher1',\n      teacherName: 'Tanaka Sensei',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n    {\n      id: '2',\n      name: 'Mathematics',\n      description: 'Advanced mathematics concepts',\n      teacherId: 'teacher2',\n      teacherName: 'Smith Sensei',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n  ],\n  messages: [\n    {\n      id: '1',\n      content: 'Hello! How can I help you today?',\n      role: 'assistant' as const,\n      timestamp: new Date(),\n      subjectId: '1',\n    },\n    {\n      id: '2',\n      content: 'Can you explain the difference between は and が?',\n      role: 'user' as const,\n      timestamp: new Date(),\n      subjectId: '1',\n    },\n  ],\n};\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;AAiB7B;AAArB,MAAM,eAAe,6DAAmC;AAExD,+BAA+B;AAC/B,eAAe,WACb,QAAgB;QAChB,UAAA,iEAAuB,CAAC;IAExB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,AAAC,GAAiB,OAAf,cAAwB,OAAT,WAAY;YACzD,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QACxD;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,UAAU;IACrB,2BAA2B;IAC3B,OAAO,IACL,WAAiB;IAEnB,sBAAsB;IACtB,eAAe,CAAC,OACd,WAAiB,OAAO;YACtB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,aAAa;IACxB,oCAAoC;IACpC,aAAa,IACX,WAAsB;IAExB,oBAAoB;IACpB,YAAY,CAAC,KACX,WAAoB,AAAC,aAAe,OAAH;IAEnC,0CAA0C;IAC1C,eAAe,CAAC,OACd,WAAoB,aAAa;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sCAAsC;IACtC,eAAe,CAAC,IAAY,OAC1B,WAAoB,AAAC,aAAe,OAAH,KAAM;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sCAAsC;IACtC,eAAe,CAAC,KACd,WAAiB,AAAC,aAAe,OAAH,KAAM;YAClC,QAAQ;QACV;AACJ;AAGO,MAAM,cAAc;IACzB,8BAA8B;IAC9B,cAAc,CAAC,YACb,WAA4B,AAAC,wBAAiC,OAAV;IAEtD,sBAAsB;IACtB,gBAAgB,CAAC,OACf,MAAM,AAAC,GAAe,OAAb,cAAa,eAAa;YACjC,QAAQ;YACR,MAAM;QACR,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;IAEzB,kBAAkB;IAClB,gBAAgB,CAAC,KACf,WAAiB,AAAC,cAAgB,OAAH,KAAM;YACnC,QAAQ;QACV;AACJ;AAGO,MAAM,UAAU;IACrB,0CAA0C;IAC1C,aAAa,gBACX,OAAe,EACf,SAAkB;YAcH;QAZf,MAAM,WAAW,MAAM,MAAM,AAAC,GAAe,OAAb,cAAa,UAAQ;YACnD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAU;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QACxD;QAEA,MAAM,UAAS,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,SAAS;QACvC,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAAU,IAAI;QACpB,IAAI,SAAS;QAEb,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,UAAU,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBAC/C,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,SAAS,MAAM,GAAG,MAAM;gBAExB,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,IAAI,SAAS,UAAU;4BACrB;wBACF;wBACA,IAAI;4BACF,MAAM,SAA4B,KAAK,KAAK,CAAC;4BAC7C,MAAM;wBACR,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,6BAA6B;wBAC7C;oBACF;gBACF;YACF;QACF,SAAU;YACR,OAAO,WAAW;QACpB;IACF;IAEA,mBAAmB;IACnB,gBAAgB,CAAC,YACf,WAA0B,AAAC,gBAA0D,OAA3C,YAAY,AAAC,cAAuB,OAAV,aAAc;AACtF;AAGO,MAAM,SAAS;IACpB,4BAA4B;IAC5B,QAAQ,CAAC,YACP,WAAoD,AAAC,WAAoB,OAAV;AACnE;AAGO,MAAM,YAAY;IACvB,mCAAmC;IACnC,WAAW,CAAC,YACV,WAA0B,AAAC,UAAoD,OAA3C,YAAY,AAAC,cAAuB,OAAV,aAAc;IAE9E,oCAAoC;IACpC,YAAY,CAAC,OACX,WAA0B,WAAW;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,8BAA8B;IAC9B,cAAc,CAAC,IAAY,OACzB,WAA0B,AAAC,WAAa,OAAH,KAAM;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;AACJ;AAGO,MAAM,QAAQ;IACnB,iCAAiC;IACjC,cAAc,CAAC,UAAkB,WAC/B,WAAwB,YAAY;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAU;YAAS;QAC5C;IAEF,qCAAqC;IACrC,YAAY,OAAO,MAAY;QAC7B,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,OAAO,OAAO,CAAC,UAAU,MAAM,EAAE,OAAO,CAAC;oBAAC,CAAC,KAAK,MAAM;gBACpD,SAAS,MAAM,CAAC,KAAK;YACvB;YACA,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,UAAU,GAAG,EAAE;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;QACT;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,oBAAoB;IACpB,kBAAkB,IAChB,WAA2B;IAE7B,uCAAuC;IACvC,oBAAoB,CAAC,OACnB,WAAyB,kBAAkB;YACzC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,sBAAsB;IACtB,oBAAoB,CAAC,KACnB,WAAiB,AAAC,kBAAoB,OAAH,KAAM;YACvC,QAAQ;QACV;AACJ;AAGO,MAAM,eAAe;IAC1B,sCAAsC;IACtC,cAAc,IACZ,WAAsB;IAExB,qBAAqB;IACrB,kBAAkB,CAAC,SACjB,WAAoC,AAAC,mBAA6C,OAA3B,SAAS,AAAC,IAAU,OAAP,UAAW;AACnF;AAGO,MAAM,UAAU;IACrB,gBAAgB;IAChB,UAAU;YAAO,wEAAO,GAAG,yEAAQ;YAStB,gBACgB;QAT3B,MAAM,WAAW,MAAM,WAAmB,AAAC,eAA4B,OAAd,MAAK,WAAe,OAAN;QACvE,yCAAyC;QACzC,OAAO;YACL,SAAS,SAAS,OAAO;YACzB,MAAM,SAAS,IAAI,IAAI,EAAE;YACzB,YAAY;gBACV;gBACA;gBACA,OAAO,EAAA,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,MAAM,KAAI;gBAChC,YAAY,KAAK,IAAI,CAAC,CAAC,EAAA,kBAAA,SAAS,IAAI,cAAb,sCAAA,gBAAe,MAAM,KAAI,CAAC,IAAI;YACvD;QACF;IACF;IAEA,cAAc;IACd,YAAY,CAAC,OACX,WAAiB,UAAU;YACzB,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,cAAc;IACd,YAAY,CAAC,IAAY,OACvB,WAAiB,AAAC,UAAY,OAAH,KAAM;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEF,cAAc;IACd,YAAY,CAAC,KACX,WAAiB,AAAC,UAAY,OAAH,KAAM;YAC/B,QAAQ;QACV;AACJ;AAGO,MAAM,WAAW;IACtB,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW;YACX,aAAa;YACb,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW;YACX,aAAa;YACb,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;KACD;IACD,UAAU;QACR;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,MAAM;YACN,WAAW,IAAI;YACf,WAAW;QACb;KACD;AACH", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/store/useUser.ts"], "sourcesContent": ["// Zustand store for User and App state management\n\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { useCallback } from 'react';\nimport { User, Subject, StudyMaterial, Announcement, Analytics, UserStore } from '@/types';\nimport { mockData } from '@/lib/api';\n\nexport const useUser = create<UserStore>()(\n  devtools(\n    persist(\n      (set) => ({\n        // State\n        user: undefined,\n        subjects: [],\n        materials: [],\n        announcements: [],\n        analytics: undefined,\n\n        // Actions\n        setUser: (user: User | undefined) => {\n          set({ user }, false, 'setUser');\n        },\n\n        setSubjects: (subjects: Subject[]) => {\n          set({ subjects }, false, 'setSubjects');\n        },\n\n        setMaterials: (materials: StudyMaterial[]) => {\n          set({ materials }, false, 'setMaterials');\n        },\n\n        setAnnouncements: (announcements: Announcement[]) => {\n          set({ announcements }, false, 'setAnnouncements');\n        },\n\n        setAnalytics: (analytics: Analytics) => {\n          set({ analytics }, false, 'setAnalytics');\n        },\n      }),\n      {\n        name: 'user-store',\n        partialize: (state) => ({\n          user: state.user,\n          subjects: state.subjects,\n          announcements: state.announcements,\n        }),\n      }\n    ),\n    {\n      name: 'user-store',\n    }\n  )\n);\n\n// Selectors for optimized re-renders\nexport const useCurrentUser = () => useUser((state) => state.user);\nexport const useSubjects = () => useUser((state) => state.subjects);\nexport const useMaterials = () => useUser((state) => state.materials);\nexport const useAnnouncements = () => useUser((state) => state.announcements);\nexport const useAnalytics = () => useUser((state) => state.analytics);\n\n// Actions selectors\nexport const useUserActions = () => useUser((state) => ({\n  setUser: state.setUser,\n  setSubjects: state.setSubjects,\n  setMaterials: state.setMaterials,\n  setAnnouncements: state.setAnnouncements,\n  setAnalytics: state.setAnalytics,\n}));\n\n// Authentication helpers\nexport const useAuth = () => {\n  const { user, setUser } = useUser();\n\n  const login = async (email: string, password: string) => {\n    try {\n      // TODO: Implement actual login when NextAuth is configured\n      console.log('Logging in:', email, 'with password length:', password.length);\n\n      // For now, use mock data\n      setUser(mockData.user);\n      return { success: true };\n    } catch (error) {\n      console.error('Login failed:', error);\n      return { success: false, error: 'Login failed' };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      // TODO: Implement actual logout when NextAuth is configured\n      setUser(undefined);\n      // Clear other stores if needed\n      return { success: true };\n    } catch (error) {\n      console.error('Logout failed:', error);\n      return { success: false, error: 'Logout failed' };\n    }\n  };\n\n  const loadUserProfile = async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await authApi.getMe();\n      console.log('Loading user profile');\n      \n      // For now, use mock data\n      setUser(mockData.user);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load user profile:', error);\n      return { success: false, error: 'Failed to load profile' };\n    }\n  };\n\n  return {\n    user,\n    login,\n    logout,\n    loadUserProfile,\n    isAuthenticated: !!user,\n    isStudent: user?.role === 'student',\n    isTeacher: user?.role === 'teacher',\n    isHOD: user?.role === 'hod',\n    isAdmin: user?.role === 'admin',\n  };\n};\n\n// Data loading helpers\nexport const useDataLoaders = () => {\n  const { user } = useAuth();\n\n  const loadSubjects = useCallback(async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await subjectApi.getSubjects();\n      console.log('Loading subjects');\n\n      // For now, use mock data\n      useUser.getState().setSubjects(mockData.subjects);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load subjects:', error);\n      return { success: false, error: 'Failed to load subjects' };\n    }\n  }, []); // Using getState() to avoid dependency issues\n\n  const loadMaterials = useCallback(async (subjectId?: string) => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await materialApi.getMaterials(subjectId);\n      console.log('Loading materials for subject:', subjectId);\n\n      // For now, use empty array\n      useUser.getState().setMaterials([]);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load materials:', error);\n      return { success: false, error: 'Failed to load materials' };\n    }\n  }, []); // Using getState() to avoid dependency issues\n\n  const loadAnnouncements = useCallback(async () => {\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await announcementApi.getAnnouncements();\n      console.log('Loading announcements');\n\n      // For now, use empty array\n      useUser.getState().setAnnouncements([]);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load announcements:', error);\n      return { success: false, error: 'Failed to load announcements' };\n    }\n  }, []); // Using getState() to avoid dependency issues\n\n  const loadAnalytics = useCallback(async () => {\n    if (!user || (user.role !== 'hod' && user.role !== 'admin')) {\n      return { success: false, error: 'Unauthorized' };\n    }\n\n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await analyticsApi.getAnalytics();\n      console.log('Loading analytics');\n\n      // For now, use mock analytics data\n      const mockAnalytics: Analytics = {\n        totalUsers: 150,\n        totalStudents: 120,\n        totalTeachers: 25,\n        totalSubjects: 15,\n        totalMaterials: 85,\n        totalChats: 1250,\n        activeUsersToday: 45,\n        activeUsersThisWeek: 98,\n        popularSubjects: [\n          { subjectId: '1', subjectName: 'Japanese Language', chatCount: 450 },\n          { subjectId: '2', subjectName: 'Mathematics', chatCount: 320 },\n        ],\n        userGrowth: [\n          { date: '2024-01-01', count: 100 },\n          { date: '2024-02-01', count: 120 },\n          { date: '2024-03-01', count: 150 },\n        ],\n      };\n\n      useUser.getState().setAnalytics(mockAnalytics);\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to load analytics:', error);\n      return { success: false, error: 'Failed to load analytics' };\n    }\n  }, [user]); // Include user dependency for role check\n\n  const loadAllData = useCallback(async () => {\n    const results = await Promise.allSettled([\n      loadSubjects(),\n      loadAnnouncements(),\n      ...(user?.role === 'hod' || user?.role === 'admin' ? [loadAnalytics()] : []),\n    ]);\n\n    const failures = results.filter(result => result.status === 'rejected');\n    if (failures.length > 0) {\n      console.error('Some data failed to load:', failures);\n    }\n\n    return { success: failures.length === 0 };\n  }, [loadSubjects, loadAnnouncements, loadAnalytics, user?.role]);\n\n  return {\n    loadSubjects,\n    loadMaterials,\n    loadAnnouncements,\n    loadAnalytics,\n    loadAllData,\n  };\n};\n\n// Subject helpers\nexport const useSubjectHelpers = () => {\n  const subjects = useSubjects();\n  const { user } = useAuth();\n\n  const getSubjectById = (id: string) => {\n    return subjects.find(subject => subject.id === id);\n  };\n\n  const getSubjectsByTeacher = (teacherId: string) => {\n    return subjects.filter(subject => subject.teacherId === teacherId);\n  };\n\n  const getUserSubjects = () => {\n    if (!user) return [];\n    \n    if (user.role === 'student') {\n      // Students see all subjects in their org\n      return subjects.filter(subject => subject.orgId === user.orgId);\n    } else if (user.role === 'teacher') {\n      // Teachers see only their subjects\n      return subjects.filter(subject => subject.teacherId === user.id);\n    } else {\n      // HOD and Admin see all subjects in their org\n      return subjects.filter(subject => subject.orgId === user.orgId);\n    }\n  };\n\n  return {\n    getSubjectById,\n    getSubjectsByTeacher,\n    getUserSubjects,\n  };\n};\n\n// Role-based permissions\nexport const usePermissions = () => {\n  const { user } = useAuth();\n\n  const canCreateSubject = () => {\n    return user?.role === 'teacher' || user?.role === 'admin';\n  };\n\n  const canEditSubject = (subjectId: string) => {\n    if (!user) return false;\n    if (user.role === 'admin') return true;\n    \n    const subject = useUser.getState().subjects.find(s => s.id === subjectId);\n    return user.role === 'teacher' && subject?.teacherId === user.id;\n  };\n\n  const canUploadMaterial = (subjectId: string) => {\n    return canEditSubject(subjectId);\n  };\n\n  const canCreateAnnouncement = () => {\n    return user?.role === 'hod' || user?.role === 'admin';\n  };\n\n  const canViewAnalytics = () => {\n    return user?.role === 'hod' || user?.role === 'admin';\n  };\n\n  const canManageUsers = () => {\n    return user?.role === 'admin';\n  };\n\n  return {\n    canCreateSubject,\n    canEditSubject,\n    canUploadMaterial,\n    canCreateAnnouncement,\n    canViewAnalytics,\n    canManageUsers,\n  };\n};"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;AAElD;AACA;AACA;AAEA;;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,QAAQ;QACR,MAAM;QACN,UAAU,EAAE;QACZ,WAAW,EAAE;QACb,eAAe,EAAE;QACjB,WAAW;QAEX,UAAU;QACV,SAAS,CAAC;YACR,IAAI;gBAAE;YAAK,GAAG,OAAO;QACvB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS,GAAG,OAAO;QAC3B;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;YAAc,GAAG,OAAO;QAChC;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU,GAAG,OAAO;QAC5B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,UAAU,MAAM,QAAQ;YACxB,eAAe,MAAM,aAAa;QACpC,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,iBAAiB;;IAAM,OAAA;kCAAQ,CAAC,QAAU,MAAM,IAAI;;AAAA;GAApD;;QAAuB;;;AAC7B,MAAM,cAAc;;IAAM,OAAA;+BAAQ,CAAC,QAAU,MAAM,QAAQ;;AAAA;IAArD;;QAAoB;;;AAC1B,MAAM,eAAe;;IAAM,OAAA;gCAAQ,CAAC,QAAU,MAAM,SAAS;;AAAA;IAAvD;;QAAqB;;;AAC3B,MAAM,mBAAmB;;IAAM,OAAA;oCAAQ,CAAC,QAAU,MAAM,aAAa;;AAAA;IAA/D;;QAAyB;;;AAC/B,MAAM,eAAe;;IAAM,OAAA;gCAAQ,CAAC,QAAU,MAAM,SAAS;;AAAA;IAAvD;;QAAqB;;;AAG3B,MAAM,iBAAiB;;IAAM,OAAA;kCAAQ,CAAC,QAAU,CAAC;gBACtD,SAAS,MAAM,OAAO;gBACtB,aAAa,MAAM,WAAW;gBAC9B,cAAc,MAAM,YAAY;gBAChC,kBAAkB,MAAM,gBAAgB;gBACxC,cAAc,MAAM,YAAY;YAClC,CAAC;;AAAC;IANW;;QAAuB;;;AAS7B,MAAM,UAAU;;IACrB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAE1B,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,2DAA2D;YAC3D,QAAQ,GAAG,CAAC,eAAe,OAAO,yBAAyB,SAAS,MAAM;YAE1E,yBAAyB;YACzB,QAAQ,oHAAA,CAAA,WAAQ,CAAC,IAAI;YACrB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,4DAA4D;YAC5D,QAAQ;YACR,+BAA+B;YAC/B,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgB;QAClD;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,wDAAwD;YACxD,0CAA0C;YAC1C,QAAQ,GAAG,CAAC;YAEZ,yBAAyB;YACzB,QAAQ,oHAAA,CAAA,WAAQ,CAAC,IAAI;YACrB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB,WAAW,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QAC1B,WAAW,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QAC1B,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QACtB,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAC1B;AACF;IAvDa;;QACe;;;AAyDrB,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC/B,IAAI;gBACF,wDAAwD;gBACxD,mDAAmD;gBACnD,QAAQ,GAAG,CAAC;gBAEZ,yBAAyB;gBACzB,QAAQ,QAAQ,GAAG,WAAW,CAAC,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBAChD,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA0B;YAC5D;QACF;mDAAG,EAAE,GAAG,8CAA8C;IAEtD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACvC,IAAI;gBACF,wDAAwD;gBACxD,8DAA8D;gBAC9D,QAAQ,GAAG,CAAC,kCAAkC;gBAE9C,2BAA2B;gBAC3B,QAAQ,QAAQ,GAAG,YAAY,CAAC,EAAE;gBAClC,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA2B;YAC7D;QACF;oDAAG,EAAE,GAAG,8CAA8C;IAEtD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACpC,IAAI;gBACF,wDAAwD;gBACxD,6DAA6D;gBAC7D,QAAQ,GAAG,CAAC;gBAEZ,2BAA2B;gBAC3B,QAAQ,QAAQ,GAAG,gBAAgB,CAAC,EAAE;gBACtC,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA+B;YACjE;QACF;wDAAG,EAAE,GAAG,8CAA8C;IAEtD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAChC,IAAI,CAAC,QAAS,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAU;gBAC3D,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAe;YACjD;YAEA,IAAI;gBACF,wDAAwD;gBACxD,sDAAsD;gBACtD,QAAQ,GAAG,CAAC;gBAEZ,mCAAmC;gBACnC,MAAM,gBAA2B;oBAC/B,YAAY;oBACZ,eAAe;oBACf,eAAe;oBACf,eAAe;oBACf,gBAAgB;oBAChB,YAAY;oBACZ,kBAAkB;oBAClB,qBAAqB;oBACrB,iBAAiB;wBACf;4BAAE,WAAW;4BAAK,aAAa;4BAAqB,WAAW;wBAAI;wBACnE;4BAAE,WAAW;4BAAK,aAAa;4BAAe,WAAW;wBAAI;qBAC9D;oBACD,YAAY;wBACV;4BAAE,MAAM;4BAAc,OAAO;wBAAI;wBACjC;4BAAE,MAAM;4BAAc,OAAO;wBAAI;wBACjC;4BAAE,MAAM;4BAAc,OAAO;wBAAI;qBAClC;gBACH;gBAEA,QAAQ,QAAQ,GAAG,YAAY,CAAC;gBAChC,OAAO;oBAAE,SAAS;gBAAK;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA2B;YAC7D;QACF;oDAAG;QAAC;KAAK,GAAG,yCAAyC;IAErD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC9B,MAAM,UAAU,MAAM,QAAQ,UAAU,CAAC;gBACvC;gBACA;mBACI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,UAAU;oBAAC;iBAAgB,GAAG,EAAE;aAC5E;YAED,MAAM,WAAW,QAAQ,MAAM;oEAAC,CAAA,SAAU,OAAO,MAAM,KAAK;;YAC5D,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;YAEA,OAAO;gBAAE,SAAS,SAAS,MAAM,KAAK;YAAE;QAC1C;kDAAG;QAAC;QAAc;QAAmB;QAAe,iBAAA,2BAAA,KAAM,IAAI;KAAC;IAE/D,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IA7Ga;;QACM;;;AA+GZ,MAAM,oBAAoB;;IAC/B,MAAM,WAAW;IACjB,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK;IAC1D;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,yCAAyC;YACzC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;QAChE,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;YAClC,mCAAmC;YACnC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE;QACjE,OAAO;YACL,8CAA8C;YAC9C,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;QAChE;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;IAhCa;;QACM;QACA;;;AAiCZ,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,mBAAmB;QACvB,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,aAAa,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,KAAK,IAAI,KAAK,SAAS,OAAO;QAElC,MAAM,UAAU,QAAQ,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/D,OAAO,KAAK,IAAI,KAAK,aAAa,CAAA,oBAAA,8BAAA,QAAS,SAAS,MAAK,KAAK,EAAE;IAClE;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,eAAe;IACxB;IAEA,MAAM,wBAAwB;QAC5B,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAChD;IAEA,MAAM,mBAAmB;QACvB,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IAChD;IAEA,MAAM,iBAAiB;QACrB,OAAO,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;IACxB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAvCa;;QACM", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/store/useChat.ts"], "sourcesContent": ["// Zustand store for Chat state management\n\nimport { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { ChatMessage, Subject, ChatStore } from '@/types';\nimport { chatApi } from '@/lib/api';\n\nexport const useChat = create<ChatStore>()(\n  devtools(\n    persist(\n      (set) => ({\n        // State\n        messages: [],\n        isLoading: false,\n        isStreaming: false,\n        currentSubject: undefined,\n\n        // Actions\n        addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {\n          const newMessage: ChatMessage = {\n            ...message,\n            id: crypto.randomUUID(),\n            timestamp: new Date(),\n          };\n\n          set(\n            (state) => ({\n              messages: [...state.messages, newMessage],\n            }),\n            false,\n            'addMessage'\n          );\n        },\n\n        updateLastMessage: (updates: Partial<ChatMessage>) => {\n          set(\n            (state) => {\n              const messages = [...state.messages];\n              const lastIndex = messages.length - 1;\n              \n              if (lastIndex >= 0) {\n                messages[lastIndex] = { ...messages[lastIndex], ...updates };\n              }\n              \n              return { messages };\n            },\n            false,\n            'updateLastMessage'\n          );\n        },\n\n        setLoading: (loading: boolean) => {\n          set({ isLoading: loading }, false, 'setLoading');\n        },\n\n        setStreaming: (streaming: boolean) => {\n          set({ isStreaming: streaming }, false, 'setStreaming');\n        },\n\n        setCurrentSubject: (subject: Subject | undefined) => {\n          set({ currentSubject: subject }, false, 'setCurrentSubject');\n        },\n\n        clearMessages: () => {\n          set({ messages: [] }, false, 'clearMessages');\n        },\n      }),\n      {\n        name: 'chat-store',\n        partialize: (state) => ({\n          messages: state.messages,\n          currentSubject: state.currentSubject,\n        }),\n      }\n    ),\n    {\n      name: 'chat-store',\n    }\n  )\n);\n\n// Selectors for optimized re-renders\nexport const useMessages = () => useChat((state) => state.messages);\nexport const useIsLoading = () => useChat((state) => state.isLoading);\nexport const useIsStreaming = () => useChat((state) => state.isStreaming);\nexport const useCurrentSubject = () => useChat((state) => state.currentSubject);\n\n// Actions selectors\nexport const useChatActions = () => useChat((state) => ({\n  addMessage: state.addMessage,\n  updateLastMessage: state.updateLastMessage,\n  setLoading: state.setLoading,\n  setStreaming: state.setStreaming,\n  setCurrentSubject: state.setCurrentSubject,\n  clearMessages: state.clearMessages,\n}));\n\n// Helper hooks for chat functionality\nexport const useChatHelpers = () => {\n  const {\n    addMessage,\n    updateLastMessage,\n    setLoading,\n    setStreaming,\n  } = useChatActions();\n  const currentSubject = useCurrentSubject();\n\n  const sendMessage = async (content: string) => {\n    if (!content.trim()) return;\n\n    // Add user message\n    addMessage({\n      content: content.trim(),\n      role: 'user',\n      subjectId: currentSubject?.id,\n    });\n\n    // Add placeholder assistant message\n    addMessage({\n      content: '',\n      role: 'assistant',\n      subjectId: currentSubject?.id,\n    });\n\n    setLoading(true);\n    setStreaming(true);\n\n    try {\n      // Stream the response\n      let fullResponse = '';\n      \n      for await (const chunk of chatApi.sendMessage(content, currentSubject?.id)) {\n        if (chunk.type === 'text') {\n          fullResponse += chunk.content;\n          updateLastMessage({ content: fullResponse });\n        } else if (chunk.type === 'audio' && chunk.audioUrl) {\n          updateLastMessage({ \n            hasAudio: true, \n            audioUrl: chunk.audioUrl,\n            visemes: chunk.visemes \n          });\n        } else if (chunk.type === 'complete') {\n          // Final message update\n          updateLastMessage({ content: fullResponse + chunk.content });\n          break;\n        }\n      }\n    } catch (error) {\n      console.error('Failed to send message:', error);\n      updateLastMessage({ \n        content: 'Sorry, I encountered an error. Please try again.' \n      });\n    } finally {\n      setLoading(false);\n      setStreaming(false);\n    }\n  };\n\n  const regenerateLastResponse = async () => {\n    const messages = useChat.getState().messages;\n    const lastUserMessage = [...messages].reverse().find(m => m.role === 'user');\n    \n    if (!lastUserMessage) return;\n\n    // Remove the last assistant message if it exists\n    const filteredMessages = messages.filter((_, index) => \n      index !== messages.length - 1 || messages[messages.length - 1].role !== 'assistant'\n    );\n    \n    useChat.setState({ messages: filteredMessages });\n    \n    // Resend the last user message\n    await sendMessage(lastUserMessage.content);\n  };\n\n  const loadChatHistory = async (subjectId?: string) => {\n    setLoading(true);\n    \n    try {\n      // TODO: Implement actual API call when backend is ready\n      // const response = await chatApi.getChatHistory(subjectId);\n      console.log('Loading chat history for subject:', subjectId);\n      \n      // For now, use mock data or keep existing messages\n    } catch (error) {\n      console.error('Failed to load chat history:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    sendMessage,\n    regenerateLastResponse,\n    loadChatHistory,\n  };\n};\n\n// Message filtering and search\nexport const useMessageFilters = () => {\n  const messages = useMessages();\n\n  const getMessagesBySubject = (subjectId: string) => {\n    return messages.filter(message => message.subjectId === subjectId);\n  };\n\n  const searchMessages = (query: string) => {\n    const lowercaseQuery = query.toLowerCase();\n    return messages.filter(message => \n      message.content.toLowerCase().includes(lowercaseQuery)\n    );\n  };\n\n  const getMessagesWithAudio = () => {\n    return messages.filter(message => message.hasAudio);\n  };\n\n  const getRecentMessages = (limit: number = 10) => {\n    return messages.slice(-limit);\n  };\n\n  return {\n    getMessagesBySubject,\n    searchMessages,\n    getMessagesWithAudio,\n    getRecentMessages,\n  };\n};\n\n// Chat statistics\nexport const useChatStats = () => {\n  const messages = useMessages();\n\n  const getTotalMessages = () => messages.length;\n  \n  const getUserMessageCount = () => \n    messages.filter(m => m.role === 'user').length;\n  \n  const getAssistantMessageCount = () => \n    messages.filter(m => m.role === 'assistant').length;\n  \n  const getMessagesWithAudioCount = () => \n    messages.filter(m => m.hasAudio).length;\n  \n  const getAverageMessageLength = () => {\n    if (messages.length === 0) return 0;\n    const totalLength = messages.reduce((sum, m) => sum + m.content.length, 0);\n    return Math.round(totalLength / messages.length);\n  };\n\n  const getMessagesByDate = () => {\n    const messagesByDate: Record<string, number> = {};\n    \n    messages.forEach(message => {\n      const date = message.timestamp.toISOString().split('T')[0];\n      messagesByDate[date] = (messagesByDate[date] || 0) + 1;\n    });\n    \n    return messagesByDate;\n  };\n\n  return {\n    getTotalMessages,\n    getUserMessageCount,\n    getAssistantMessageCount,\n    getMessagesWithAudioCount,\n    getAverageMessageLength,\n    getMessagesByDate,\n  };\n};\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;;;;;;;;AAE1C;AACA;AAEA;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,QAAQ;QACR,UAAU,EAAE;QACZ,WAAW;QACX,aAAa;QACb,gBAAgB;QAEhB,UAAU;QACV,YAAY,CAAC;YACX,MAAM,aAA0B;gBAC9B,GAAG,OAAO;gBACV,IAAI,OAAO,UAAU;gBACrB,WAAW,IAAI;YACjB;YAEA,IACE,CAAC,QAAU,CAAC;oBACV,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAW;gBAC3C,CAAC,GACD,OACA;QAEJ;QAEA,mBAAmB,CAAC;YAClB,IACE,CAAC;gBACC,MAAM,WAAW;uBAAI,MAAM,QAAQ;iBAAC;gBACpC,MAAM,YAAY,SAAS,MAAM,GAAG;gBAEpC,IAAI,aAAa,GAAG;oBAClB,QAAQ,CAAC,UAAU,GAAG;wBAAE,GAAG,QAAQ,CAAC,UAAU;wBAAE,GAAG,OAAO;oBAAC;gBAC7D;gBAEA,OAAO;oBAAE;gBAAS;YACpB,GACA,OACA;QAEJ;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ,GAAG,OAAO;QACrC;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE,aAAa;YAAU,GAAG,OAAO;QACzC;QAEA,mBAAmB,CAAC;YAClB,IAAI;gBAAE,gBAAgB;YAAQ,GAAG,OAAO;QAC1C;QAEA,eAAe;YACb,IAAI;gBAAE,UAAU,EAAE;YAAC,GAAG,OAAO;QAC/B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;YACxB,gBAAgB,MAAM,cAAc;QACtC,CAAC;AACH,IAEF;IACE,MAAM;AACR;AAKG,MAAM,cAAc;;IAAM,OAAA;+BAAQ,CAAC,QAAU,MAAM,QAAQ;;AAAA;GAArD;;QAAoB;;;AAC1B,MAAM,eAAe;;IAAM,OAAA;gCAAQ,CAAC,QAAU,MAAM,SAAS;;AAAA;IAAvD;;QAAqB;;;AAC3B,MAAM,iBAAiB;;IAAM,OAAA;kCAAQ,CAAC,QAAU,MAAM,WAAW;;AAAA;IAA3D;;QAAuB;;;AAC7B,MAAM,oBAAoB;;IAAM,OAAA;qCAAQ,CAAC,QAAU,MAAM,cAAc;;AAAA;IAAjE;;QAA0B;;;AAGhC,MAAM,iBAAiB;;IAAM,OAAA;kCAAQ,CAAC,QAAU,CAAC;gBACtD,YAAY,MAAM,UAAU;gBAC5B,mBAAmB,MAAM,iBAAiB;gBAC1C,YAAY,MAAM,UAAU;gBAC5B,cAAc,MAAM,YAAY;gBAChC,mBAAmB,MAAM,iBAAiB;gBAC1C,eAAe,MAAM,aAAa;YACpC,CAAC;;AAAC;IAPW;;QAAuB;;;AAU7B,MAAM,iBAAiB;;IAC5B,MAAM,EACJ,UAAU,EACV,iBAAiB,EACjB,UAAU,EACV,YAAY,EACb,GAAG;IACJ,MAAM,iBAAiB;IAEvB,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,mBAAmB;QACnB,WAAW;YACT,SAAS,QAAQ,IAAI;YACrB,MAAM;YACN,SAAS,EAAE,2BAAA,qCAAA,eAAgB,EAAE;QAC/B;QAEA,oCAAoC;QACpC,WAAW;YACT,SAAS;YACT,MAAM;YACN,SAAS,EAAE,2BAAA,qCAAA,eAAgB,EAAE;QAC/B;QAEA,WAAW;QACX,aAAa;QAEb,IAAI;YACF,sBAAsB;YACtB,IAAI,eAAe;YAEnB,WAAW,MAAM,SAAS,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,SAAS,2BAAA,qCAAA,eAAgB,EAAE,EAAG;gBAC1E,IAAI,MAAM,IAAI,KAAK,QAAQ;oBACzB,gBAAgB,MAAM,OAAO;oBAC7B,kBAAkB;wBAAE,SAAS;oBAAa;gBAC5C,OAAO,IAAI,MAAM,IAAI,KAAK,WAAW,MAAM,QAAQ,EAAE;oBACnD,kBAAkB;wBAChB,UAAU;wBACV,UAAU,MAAM,QAAQ;wBACxB,SAAS,MAAM,OAAO;oBACxB;gBACF,OAAO,IAAI,MAAM,IAAI,KAAK,YAAY;oBACpC,uBAAuB;oBACvB,kBAAkB;wBAAE,SAAS,eAAe,MAAM,OAAO;oBAAC;oBAC1D;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,kBAAkB;gBAChB,SAAS;YACX;QACF,SAAU;YACR,WAAW;YACX,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,WAAW,QAAQ,QAAQ,GAAG,QAAQ;QAC5C,MAAM,kBAAkB;eAAI;SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAErE,IAAI,CAAC,iBAAiB;QAEtB,iDAAiD;QACjD,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,GAAG,QAC3C,UAAU,SAAS,MAAM,GAAG,KAAK,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK;QAG1E,QAAQ,QAAQ,CAAC;YAAE,UAAU;QAAiB;QAE9C,+BAA+B;QAC/B,MAAM,YAAY,gBAAgB,OAAO;IAC3C;IAEA,MAAM,kBAAkB,OAAO;QAC7B,WAAW;QAEX,IAAI;YACF,wDAAwD;YACxD,4DAA4D;YAC5D,QAAQ,GAAG,CAAC,qCAAqC;QAEjD,mDAAmD;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;IAlGa;;QAMP;QACmB;;;AA8FlB,MAAM,oBAAoB;;IAC/B,MAAM,WAAW;IAEjB,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK;IAC1D;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,iBAAiB,MAAM,WAAW;QACxC,OAAO,SAAS,MAAM,CAAC,CAAA,UACrB,QAAQ,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;IAE3C;IAEA,MAAM,uBAAuB;QAC3B,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACpD;IAEA,MAAM,oBAAoB;YAAC,yEAAgB;QACzC,OAAO,SAAS,KAAK,CAAC,CAAC;IACzB;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IA5Ba;;QACM;;;AA8BZ,MAAM,eAAe;;IAC1B,MAAM,WAAW;IAEjB,MAAM,mBAAmB,IAAM,SAAS,MAAM;IAE9C,MAAM,sBAAsB,IAC1B,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;IAEhD,MAAM,2BAA2B,IAC/B,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;IAErD,MAAM,4BAA4B,IAChC,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;IAEzC,MAAM,0BAA0B;QAC9B,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAClC,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;QACxE,OAAO,KAAK,KAAK,CAAC,cAAc,SAAS,MAAM;IACjD;IAEA,MAAM,oBAAoB;QACxB,MAAM,iBAAyC,CAAC;QAEhD,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,OAAO,QAAQ,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,cAAc,CAAC,KAAK,GAAG,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,IAAI;QACvD;QAEA,OAAO;IACT;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAvCa;;QACM", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/%28dash%29/student/page.tsx"], "sourcesContent": ["'use client';\n\n// Student Dashboard - View subjects and access AI chat\n\nimport { useEffect, useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  BookOpen, \n  MessageSquare, \n  Clock, \n  User,\n  Play,\n  FileText,\n  Headphones\n} from 'lucide-react';\nimport { useSubjects, useDataLoaders } from '@/store/useUser';\nimport { useCurrentSubject, useChatActions } from '@/store/useChat';\nimport { mockData } from '@/lib/api';\nimport { Subject } from '@/types';\n\nexport default function StudentDashboard() {\n  const { data: session } = useSession();\n  const router = useRouter();\n  const subjects = useSubjects();\n  const currentSubject = useCurrentSubject();\n  const { setCurrentSubject } = useChatActions();\n  const { loadSubjects } = useDataLoaders();\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasLoaded, setHasLoaded] = useState(false);\n\n  useEffect(() => {\n    if (!hasLoaded) {\n      const loadData = async () => {\n        await loadSubjects();\n        setIsLoading(false);\n        setHasLoaded(true);\n      };\n      loadData();\n    }\n  }, [loadSubjects, hasLoaded]);\n\n  const handleStartChat = (subject: Subject) => {\n    setCurrentSubject(subject);\n    router.push('/student/chat');\n  };\n\n  const handleViewMaterials = (subjectId: string) => {\n    router.push(`/student/materials?subject=${subjectId}`);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading your subjects...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white\">\n        <h1 className=\"text-2xl font-bold mb-2\">\n          Welcome back, {session?.user?.name}!\n        </h1>\n        <p className=\"text-blue-100\">\n          Ready to continue your learning journey? Choose a subject to start chatting with your AI tutor.\n        </p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <BookOpen className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Enrolled Subjects</p>\n                <p className=\"text-2xl font-bold\">{subjects.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <MessageSquare className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Chat Sessions</p>\n                <p className=\"text-2xl font-bold\">12</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <Clock className=\"h-5 w-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Study Time</p>\n                <p className=\"text-2xl font-bold\">24h</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Current Subject */}\n      {currentSubject && (\n        <Card className=\"border-blue-200 bg-blue-50\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <MessageSquare className=\"h-5 w-5 text-blue-600\" />\n              <span>Continue Learning</span>\n            </CardTitle>\n            <CardDescription>\n              You were last studying {currentSubject.name}\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <User className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">{currentSubject.name}</p>\n                  <p className=\"text-sm text-gray-600\">with {currentSubject.teacherName}</p>\n                </div>\n              </div>\n              <Button onClick={() => handleStartChat(currentSubject)}>\n                <MessageSquare className=\"h-4 w-4 mr-2\" />\n                Continue Chat\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Available Subjects */}\n      <div>\n        <h2 className=\"text-xl font-semibold mb-4\">Your Subjects</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {subjects.length > 0 ? subjects.map((subject) => (\n            <Card key={subject.id} className=\"hover:shadow-md transition-shadow\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <span className=\"text-lg\">{subject.name}</span>\n                  <Badge variant=\"secondary\">Active</Badge>\n                </CardTitle>\n                <CardDescription>\n                  <div className=\"flex items-center space-x-2\">\n                    <User className=\"h-4 w-4\" />\n                    <span>{subject.teacherName}</span>\n                  </div>\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-sm text-gray-600 mb-4\">\n                  {subject.description || 'Enhance your knowledge with AI-powered tutoring.'}\n                </p>\n                \n                <div className=\"space-y-2\">\n                  <Button \n                    onClick={() => handleStartChat(subject)}\n                    className=\"w-full\"\n                  >\n                    <MessageSquare className=\"h-4 w-4 mr-2\" />\n                    Start AI Chat\n                  </Button>\n                  \n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <Button \n                      variant=\"outline\" \n                      size=\"sm\"\n                      onClick={() => handleViewMaterials(subject.id)}\n                    >\n                      <FileText className=\"h-4 w-4 mr-1\" />\n                      Materials\n                    </Button>\n                    <Button \n                      variant=\"outline\" \n                      size=\"sm\"\n                      onClick={() => router.push(`/student/practice?subject=${subject.id}`)}\n                    >\n                      <Play className=\"h-4 w-4 mr-1\" />\n                      Practice\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )) : (\n            // Show mock subjects if none loaded\n            mockData.subjects.map((subject) => (\n              <Card key={subject.id} className=\"hover:shadow-md transition-shadow\">\n                <CardHeader>\n                  <CardTitle className=\"flex items-center justify-between\">\n                    <span className=\"text-lg\">{subject.name}</span>\n                    <Badge variant=\"secondary\">Active</Badge>\n                  </CardTitle>\n                  <CardDescription>\n                    <div className=\"flex items-center space-x-2\">\n                      <User className=\"h-4 w-4\" />\n                      <span>{subject.teacherName}</span>\n                    </div>\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-sm text-gray-600 mb-4\">\n                    {subject.description || 'Enhance your knowledge with AI-powered tutoring.'}\n                  </p>\n                  \n                  <div className=\"space-y-2\">\n                    <Button \n                      onClick={() => handleStartChat(subject)}\n                      className=\"w-full\"\n                    >\n                      <MessageSquare className=\"h-4 w-4 mr-2\" />\n                      Start AI Chat\n                    </Button>\n                    \n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <Button \n                        variant=\"outline\" \n                        size=\"sm\"\n                        onClick={() => handleViewMaterials(subject.id)}\n                      >\n                        <FileText className=\"h-4 w-4 mr-1\" />\n                        Materials\n                      </Button>\n                      <Button \n                        variant=\"outline\" \n                        size=\"sm\"\n                        onClick={() => router.push(`/student/practice?subject=${subject.id}`)}\n                      >\n                        <Play className=\"h-4 w-4 mr-1\" />\n                        Practice\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))\n          )}\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Activity</CardTitle>\n          <CardDescription>Your latest learning sessions</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <MessageSquare className=\"h-5 w-5 text-blue-600\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">Japanese Language Chat</p>\n                <p className=\"text-sm text-gray-600\">Discussed grammar patterns - 2 hours ago</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <Headphones className=\"h-5 w-5 text-green-600\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">Pronunciation Practice</p>\n                <p className=\"text-sm text-gray-600\">Completed voice exercises - 1 day ago</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <FileText className=\"h-5 w-5 text-purple-600\" />\n              <div className=\"flex-1\">\n                <p className=\"font-medium\">Study Material Review</p>\n                <p className=\"text-sm text-gray-600\">Read Chapter 5 notes - 2 days ago</p>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA,uDAAuD;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;;;AArBA;;;;;;;;;;;AAwBe,SAAS;QA8CC;;IA7CvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,iBAAiB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD;IAC3C,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,WAAW;gBACd,MAAM;2DAAW;wBACf,MAAM;wBACN,aAAa;wBACb,aAAa;oBACf;;gBACA;YACF;QACF;qCAAG;QAAC;QAAc;KAAU;IAE5B,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,IAAI,CAAC,AAAC,8BAAuC,OAAV;IAC5C;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA0B;4BACvB,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI;4BAAC;;;;;;;kCAErC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAsB,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM1D,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ3C,gCACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,mIAAA,CAAA,kBAAe;;oCAAC;oCACS,eAAe,IAAI;;;;;;;;;;;;;kCAG/C,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAe,eAAe,IAAI;;;;;;8DAC/C,6LAAC;oDAAE,WAAU;;wDAAwB;wDAAM,eAAe,WAAW;;;;;;;;;;;;;;;;;;;8CAGzE,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,gBAAgB;;sDACrC,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;kCACZ,SAAS,MAAM,GAAG,IAAI,SAAS,GAAG,CAAC,CAAC,wBACnC,6LAAC,mIAAA,CAAA,OAAI;gCAAkB,WAAU;;kDAC/B,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC;wDAAK,WAAU;kEAAW,QAAQ,IAAI;;;;;;kEACvC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;;;;;;;0DAE7B,6LAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAM,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW,IAAI;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;;0EAEV,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAI5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,oBAAoB,QAAQ,EAAE;;kFAE7C,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,6BAAuC,OAAX,QAAQ,EAAE;;kFAElE,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;+BAzChC,QAAQ,EAAE;;;;wCAiDrB,oCAAoC;wBACpC,oHAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,6LAAC,mIAAA,CAAA,OAAI;gCAAkB,WAAU;;kDAC/B,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC;wDAAK,WAAU;kEAAW,QAAQ,IAAI;;;;;;kEACvC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;;;;;;;0DAE7B,6LAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAM,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW,IAAI;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;;0EAEV,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAI5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,oBAAoB,QAAQ,EAAE;;kFAE7C,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,6BAAuC,OAAX,QAAQ,EAAE;;kFAElE,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;+BAzChC,QAAQ,EAAE;;;;;;;;;;;;;;;;0BAsD7B,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;GAjRwB;;QACI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;QACP,0HAAA,CAAA,cAAW;QACL,0HAAA,CAAA,oBAAiB;QACV,0HAAA,CAAA,iBAAc;QACnB,0HAAA,CAAA,iBAAc;;;KANjB", "debugId": null}}]}