(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1154:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1915:(e,r,t)=>{Promise.resolve().then(t.bind(t,3792))},3792:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(5155),a=t(2115),i=t(5695),n=t(2108),l=t(7949),c=t(1154);function u(){let e=(0,i.useRouter)(),{data:r,status:t}=(0,n.useSession)();return(0,a.useEffect)(()=>{if("loading"!==t)if(r)switch(r.user.role){case"student":e.push("/student");break;case"teacher":e.push("/teacher");break;case"hod":e.push("/hod");break;case"admin":e.push("/admin");break;default:e.push("/sign-in")}else e.push("/sign-in")},[r,t,e]),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:(0,s.jsx)("div",{className:"p-4 bg-blue-600 rounded-full",children:(0,s.jsx)(l.A,{className:"h-12 w-12 text-white"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"AI Tutor Platform"}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-gray-600",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 animate-spin"}),(0,s.jsx)("span",{children:"Loading..."})]})]})})}},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},7949:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var s=t(2115);let a=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:u="",children:o,iconNode:d,...h}=e;return(0,s.createElement)("svg",{ref:r,...n,width:a,height:a,stroke:t,strokeWidth:c?24*Number(l)/Number(a):l,className:i("lucide",u),...!o&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(h)&&{"aria-hidden":"true"},...h},[...d.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(o)?o:[o]])}),c=(e,r)=>{let t=(0,s.forwardRef)((t,n)=>{let{className:c,...u}=t;return(0,s.createElement)(l,{ref:n,iconNode:r,className:i("lucide-".concat(a(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...u})});return t.displayName=a(e),t}}},e=>{e.O(0,[108,441,964,358],()=>e(e.s=1915)),_N_E=e.O()}]);