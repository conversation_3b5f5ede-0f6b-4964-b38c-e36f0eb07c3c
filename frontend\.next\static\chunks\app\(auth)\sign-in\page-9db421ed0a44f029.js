(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(5155),a=t(2115),l=t(9708),n=t(2085),d=t(9434);let i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,size:n,asChild:o=!1,...c}=e,u=o?l.DX:"button";return(0,s.jsx)(u,{className:(0,d.cn)(i({variant:a,size:n,className:t})),ref:r,...c})});o.displayName="Button"},968:(e,r,t)=>{"use strict";t.d(r,{b:()=>d});var s=t(2115),a=t(3655),l=t(5155),n=s.forwardRef((e,r)=>(0,l.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var d=n},1154:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(5155),a=t(2115),l=t(9434);let n=a.forwardRef((e,r)=>{let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...n})});n.displayName="Input"},3655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>i,sG:()=>d});var s=t(2115),a=t(7650),l=t(9708),n=t(5155),d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,l.TL)(`Primitive.${r}`),a=s.forwardRef((e,s)=>{let{asChild:a,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?t:r,{...l,ref:s})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function i(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},4067:(e,r,t)=>{Promise.resolve().then(t.bind(t,8261))},5057:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var s=t(5155),a=t(2115),l=t(968),n=t(2085),d=t(9434);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(l.b,{ref:r,className:(0,d.cn)(i(),t),...a})});o.displayName=l.b.displayName},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>d});var s=t(5155),a=t(2115),l=t(9434);let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});i.displayName="CardTitle";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},7949:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},8261:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(5155),a=t(2115),l=t(2108),n=t(5695),d=t(285),i=t(2523),o=t(5057),c=t(6695),u=t(2085),f=t(9434);let m=(0,u.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),p=a.forwardRef((e,r)=>{let{className:t,variant:a,...l}=e;return(0,s.jsx)("div",{ref:r,role:"alert",className:(0,f.cn)(m({variant:a}),t),...l})});p.displayName="Alert",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h5",{ref:r,className:(0,f.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let x=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,f.cn)("text-sm [&_p]:leading-relaxed",t),...a})});x.displayName="AlertDescription";var g=t(7949),h=t(1154);function v(){let[e,r]=(0,a.useState)(""),[t,u]=(0,a.useState)(""),[f,m]=(0,a.useState)(!1),[v,b]=(0,a.useState)(""),y=(0,n.useRouter)(),j=async r=>{r.preventDefault(),m(!0),b("");try{let r=await (0,l.signIn)("credentials",{email:e,password:t,redirect:!1});if(null==r?void 0:r.error)b("Invalid email or password");else{var s;let e=await (0,l.getSession)();if(null==e||null==(s=e.user)?void 0:s.role)switch(e.user.role){case"student":y.push("/student");break;case"teacher":y.push("/teacher");break;case"hod":y.push("/hod");break;case"admin":y.push("/admin");break;default:y.push("/")}}}catch(e){console.error("Sign-in error:",e),b("An error occurred. Please try again.")}finally{m(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)("div",{className:"p-3 bg-blue-600 rounded-full",children:(0,s.jsx)(g.A,{className:"h-8 w-8 text-white"})})}),(0,s.jsx)(c.ZB,{className:"text-2xl font-bold",children:"AI Tutor Platform"}),(0,s.jsx)(c.BT,{children:"Sign in to access your personalized learning experience"})]}),(0,s.jsxs)(c.Wu,{children:[(0,s.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(i.p,{id:"email",type:"email",placeholder:"Enter your email",value:e,onChange:e=>r(e.target.value),required:!0,disabled:f})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"password",children:"Password"}),(0,s.jsx)(i.p,{id:"password",type:"password",placeholder:"Enter your password",value:t,onChange:e=>u(e.target.value),required:!0,disabled:f})]}),v&&(0,s.jsx)(p,{variant:"destructive",children:(0,s.jsx)(x,{children:v})}),(0,s.jsx)(d.$,{type:"submit",className:"w-full",disabled:f,children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Demo Accounts:"}),(0,s.jsxs)("div",{className:"space-y-1 text-xs text-gray-600",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Student:"})," <EMAIL>"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Teacher:"})," <EMAIL>"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"HOD:"})," <EMAIL>"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Admin:"})," <EMAIL>"]}),(0,s.jsxs)("p",{className:"mt-2",children:[(0,s.jsx)("strong",{children:"Password:"})," password"]})]})]})]})]})})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var s=t(2596),a=t(9688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{e.O(0,[817,108,441,964,358],()=>e(e.s=4067)),_N_E=e.O()}]);