'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MessageSquare, 
  BookOpen, 
  ArrowLeft,
  Settings,
  Volume2,
  Bot,
  Maximize2,
  Minimize2
} from 'lucide-react';
import Link from 'next/link';
import { MessageList } from '@/components/chat/MessageList';
import { TypingBox } from '@/components/chat/TypingBox';
import { Experience } from '@/components/3d/Experience';
import { useMessages, useSetCurrentSubject, useChatHelpers } from '@/store/useChat';
import { useSubjects } from '@/store/useUser';
import { useSetBlackboardText, useShowBlackboard } from '@/store/useAITeacher';

// Mock subjects data
const mockSubjects = [
  { id: '1', name: 'Japanese Language', teacher: '<PERSON>' },
  { id: '2', name: 'Mathematics', teacher: 'Dr. Smith' },
  { id: '3', name: 'Spanish Language', teacher: 'Prof. Garcia' }
];

function ChatPageContent() {
  const searchParams = useSearchParams();
  const subjectId = searchParams.get('subject');
  
  const [selectedSubject, setSelectedSubject] = useState(subjectId || '1');
  const [showFurigana, setShowFurigana] = useState(true);
  const [showGrammarBreakdown, setShowGrammarBreakdown] = useState(false);
  const [show3DAvatar, setShow3DAvatar] = useState(true);
  const [isAvatarExpanded, setIsAvatarExpanded] = useState(false);
  
  const messages = useMessages();
  const subjects = useSubjects();
  const setCurrentSubject = useSetCurrentSubject();
  const { sendMessage, regenerateLastResponse } = useChatHelpers();
  const setBlackboardText = useSetBlackboardText();
  const showBlackboard = useShowBlackboard();

  // Set current subject when component mounts or subject changes
  useEffect(() => {
    const subject = mockSubjects.find(s => s.id === selectedSubject);
    if (subject) {
      setCurrentSubject(subject);
    }
  }, [selectedSubject, setCurrentSubject]);

  const handleSubjectChange = (newSubjectId: string) => {
    setSelectedSubject(newSubjectId);
    const subject = mockSubjects.find(s => s.id === newSubjectId);
    if (subject) {
      setCurrentSubject(subject);
    }
  };

  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage(message);
      
      // Mock AI response with blackboard text for Japanese
      if (selectedSubject === '1' && message.toLowerCase().includes('grammar')) {
        setBlackboardText(`Grammar Pattern: ${message}\n\nExample:\n私は学生です。\nWatashi wa gakusei desu.\n"I am a student."`);
        showBlackboard(true);
        
        // Hide blackboard after 10 seconds
        setTimeout(() => {
          showBlackboard(false);
        }, 10000);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleRegenerateResponse = async (messageId: string) => {
    try {
      await regenerateLastResponse();
    } catch (error) {
      console.error('Failed to regenerate response:', error);
    }
  };

  const handleRateMessage = (messageId: string, rating: 'up' | 'down') => {
    // TODO: Implement message rating
    console.log('Rating message:', messageId, rating);
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/student">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Link>
            </Button>
            
            <div className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5 text-blue-600" />
              <h1 className="text-xl font-semibold">AI Chat</h1>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Subject Selector */}
            <div className="flex items-center space-x-2">
              <BookOpen className="w-4 h-4 text-gray-500" />
              <Select value={selectedSubject} onValueChange={handleSubjectChange}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {mockSubjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Chat Settings */}
            <div className="flex items-center space-x-2">
              <Button
                variant={showFurigana ? "default" : "outline"}
                size="sm"
                onClick={() => setShowFurigana(!showFurigana)}
              >
                ふりがな
              </Button>
              
              <Button
                variant={showGrammarBreakdown ? "default" : "outline"}
                size="sm"
                onClick={() => setShowGrammarBreakdown(!showGrammarBreakdown)}
              >
                Grammar
              </Button>
              
              <Button
                variant={show3DAvatar ? "default" : "outline"}
                size="sm"
                onClick={() => setShow3DAvatar(!show3DAvatar)}
              >
                <Bot className="w-4 h-4 mr-1" />
                3D Avatar
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Chat Area */}
        <div className={`flex-1 flex flex-col ${show3DAvatar ? 'lg:w-2/3' : 'w-full'}`}>
          {/* Subject Info */}
          <div className="bg-blue-50 border-b px-6 py-3">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="font-medium text-blue-900">
                  {mockSubjects.find(s => s.id === selectedSubject)?.name}
                </h2>
                <p className="text-sm text-blue-700">
                  with {mockSubjects.find(s => s.id === selectedSubject)?.teacher}
                </p>
              </div>
              <Badge variant="secondary">
                {messages.length} messages
              </Badge>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-hidden">
            <MessageList
              className="h-full overflow-y-auto"
              showFurigana={showFurigana}
              showGrammarBreakdown={showGrammarBreakdown}
              onRegenerateResponse={handleRegenerateResponse}
              onRateMessage={handleRateMessage}
            />
          </div>

          {/* Typing Box */}
          <div className="border-t bg-white p-4">
            <TypingBox
              placeholder={`Ask a question about ${mockSubjects.find(s => s.id === selectedSubject)?.name}...`}
              onSendMessage={handleSendMessage}
            />
          </div>
        </div>

        {/* 3D Avatar Panel */}
        {show3DAvatar && (
          <div className={`bg-white border-l transition-all duration-300 ${
            isAvatarExpanded ? 'w-full lg:w-full' : 'w-full lg:w-1/3'
          }`}>
            <div className="h-full flex flex-col">
              {/* Avatar Header */}
              <div className="border-b px-4 py-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Bot className="w-5 h-5 text-blue-600" />
                    <h3 className="font-medium">AI Teacher Avatar</h3>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsAvatarExpanded(!isAvatarExpanded)}
                    >
                      {isAvatarExpanded ? (
                        <Minimize2 className="w-4 h-4" />
                      ) : (
                        <Maximize2 className="w-4 h-4" />
                      )}
                    </Button>
                    
                    <Button variant="ghost" size="sm">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* 3D Experience */}
              <div className="flex-1 relative">
                <Experience 
                  className="w-full h-full"
                  enableControls={true}
                  showBlackboard={true}
                />
                
                {/* Avatar Status */}
                <div className="absolute top-4 left-4">
                  <Badge variant="secondary" className="bg-white/90 backdrop-blur-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                    AI Teacher Active
                  </Badge>
                </div>

                {/* Quick Actions */}
                <div className="absolute bottom-4 right-4 space-y-2">
                  <Button size="sm" className="bg-white/90 backdrop-blur-sm text-gray-700 hover:bg-white">
                    <Volume2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default function StudentChatPage() {
  return (
    <Suspense fallback={
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading chat interface...</p>
        </div>
      </div>
    }>
      <ChatPageContent />
    </Suspense>
  );
}
