(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[255],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155),r=a(2115),n=a(9708),l=a(2085),c=a(9434);let i=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:a,variant:r,size:l,asChild:d=!1,...h}=e,u=d?n.DX:"button";return(0,t.jsx)(u,{className:(0,c.cn)(i({variant:r,size:l,className:a})),ref:s,...h})});d.displayName="Button"},381:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1447:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(5155),r=a(2108),n=a(5695),l=a(2115),c=a(1154);function i(e){let{children:s,allowedRoles:a=[],redirectTo:i="/sign-in"}=e,{data:d,status:h}=(0,r.useSession)(),u=(0,n.useRouter)();return((0,l.useEffect)(()=>{if("loading"!==h){if(!d)return void u.push(i);if(a.length>0&&!a.includes(d.user.role)){switch(d.user.role){case"student":u.push("/student");break;case"teacher":u.push("/teacher");break;case"hod":u.push("/hod");break;case"admin":u.push("/admin");break;default:u.push("/sign-in")}return}}},[d,h,u,a,i]),"loading"===h)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(c.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):d&&(!(a.length>0)||a.includes(d.user.role))?(0,t.jsx)(t.Fragment,{children:s}):null}var d=a(285),h=a(5040),u=a(1497),o=a(381),m=a(7580),x=a(2713),f=a(3861),p=a(7949),v=a(1007);let y=(0,a(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);function b(e){var s,a;let{children:l}=e,{data:c}=(0,r.useSession)(),b=(0,n.useRouter)(),g=async()=>{await (0,r.signOut)({callbackUrl:"/sign-in"})};return(0,t.jsx)(i,{allowedRoles:["student","teacher","hod","admin"],children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsx)("span",{className:"ml-2 text-xl font-bold text-gray-900",children:"AI Tutor Platform"})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:null==c||null==(s=c.user)?void 0:s.name}),(0,t.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full capitalize",children:null==c||null==(a=c.user)?void 0:a.role})]}),(0,t.jsxs)(d.$,{variant:"outline",size:"sm",onClick:g,className:"flex items-center space-x-2",children:[(0,t.jsx)(y,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Sign Out"})]})]})]})})}),(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("nav",{className:"w-64 bg-white shadow-sm min-h-screen",children:(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)("ul",{className:"space-y-2",children:(()=>{var e;let s=null==c||null==(e=c.user)?void 0:e.role,a=[{icon:h.A,label:"Subjects",href:"/".concat(s)}];switch(s){case"student":return[...a,{icon:u.A,label:"AI Chat",href:"/student/chat"}];case"teacher":return[...a,{icon:u.A,label:"AI Chat",href:"/teacher/chat"},{icon:o.A,label:"Avatar Setup",href:"/teacher/avatar"}];case"hod":return[...a,{icon:m.A,label:"Teachers",href:"/hod/teachers"},{icon:x.A,label:"Analytics",href:"/hod/analytics"},{icon:f.A,label:"Announcements",href:"/hod/announcements"}];case"admin":return[...a,{icon:m.A,label:"Users",href:"/admin/users"},{icon:x.A,label:"Analytics",href:"/admin/analytics"},{icon:f.A,label:"Announcements",href:"/admin/announcements"},{icon:o.A,label:"Settings",href:"/admin/settings"}];default:return a}})().map(e=>(0,t.jsx)("li",{children:(0,t.jsxs)("button",{onClick:()=>b.push(e.href),className:"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:e.label})]})},e.href))})})}),(0,t.jsx)("main",{className:"flex-1 p-6",children:l})]})]})})}},1497:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("message-square",[["path",{d:"M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z",key:"18887p"}]])},2543:(e,s,a)=>{Promise.resolve().then(a.bind(a,1447))},2713:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},5040:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},7580:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7949:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>n});var t=a(2596),r=a(9688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}}},e=>{e.O(0,[817,108,441,964,358],()=>e(e.s=2543)),_N_E=e.O()}]);