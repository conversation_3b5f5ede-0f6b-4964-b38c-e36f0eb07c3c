# Server Configuration
PORT=3000
NODE_ENV=development

# Database
MONGODB_URI=mongodb+srv://24124002:<EMAIL>/ai-tutor?retryWrites=true&w=majority

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=THoQxj0FHuEWoMEd99csNvN8uMfO0kE/XDHuIZhW
AWS_REGION=us-east-1
S3_BUCKET_NAME=ai-tutor-materials

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Azure Speech Configuration
AZURE_SPEECH_KEY=your-azure-speech-key
AZURE_SPEECH_REGION=eastus

# Redis Configuration (for BullMQ)
REDIS_URL=redis://localhost:6379

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
