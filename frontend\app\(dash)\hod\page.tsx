import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  BookOpen, 
  GraduationCap,
  TrendingUp,
  MessageSquare,
  Calendar,
  Bell,
  BarChart3,
  UserPlus,
  Settings,
  Award,
  Clock
} from 'lucide-react';
import Link from 'next/link';

// Mock data - would be fetched from API in production
const mockHODData = {
  user: {
    name: 'Dr. <PERSON>',
    department: 'Language Studies',
    organization: 'Tokyo International School'
  },
  overview: {
    totalTeachers: 12,
    totalStudents: 340,
    totalSubjects: 25,
    activeChats: 89,
    avgSatisfaction: 92
  },
  teachers: [
    {
      id: '1',
      name: 'Dr. <PERSON>',
      subjects: ['Japanese Language', 'Cultural Studies'],
      students: 45,
      satisfaction: 94,
      status: 'active',
      lastActive: '2 hours ago'
    },
    {
      id: '2',
      name: 'Prof. <PERSON>',
      subjects: ['Advanced Japanese', 'Business Japanese'],
      students: 38,
      satisfaction: 96,
      status: 'active',
      lastActive: '1 hour ago'
    },
    {
      id: '3',
      name: 'Dr. <PERSON>',
      subjects: ['Spanish Language', 'Literature'],
      students: 52,
      satisfaction: 89,
      status: 'active',
      lastActive: '30 minutes ago'
    }
  ],
  subjects: [
    {
      id: '1',
      name: 'Japanese Language',
      teacher: 'Dr. Sarah Wilson',
      students: 45,
      avgProgress: 78,
      materials: 24,
      status: 'active'
    },
    {
      id: '2',
      name: 'Advanced Japanese',
      teacher: 'Prof. Tanaka Hiroshi',
      students: 28,
      avgProgress: 82,
      materials: 18,
      status: 'active'
    },
    {
      id: '3',
      name: 'Spanish Language',
      teacher: 'Dr. Emily Rodriguez',
      students: 52,
      avgProgress: 71,
      materials: 31,
      status: 'active'
    }
  ],
  announcements: [
    {
      id: '1',
      title: 'New AI Features Available',
      content: 'Enhanced voice recognition and grammar analysis now available for all subjects.',
      date: '2024-01-15',
      priority: 'high',
      sent: true
    },
    {
      id: '2',
      title: 'Teacher Training Session',
      content: 'Monthly training on AI avatar customization scheduled for next week.',
      date: '2024-01-20',
      priority: 'medium',
      sent: false
    }
  ],
  analytics: {
    monthlyGrowth: 15,
    studentEngagement: 87,
    teacherUtilization: 94,
    systemUptime: 99.8
  }
};

export default function HODDashboard() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome, {mockHODData.user.name}! 🎓
          </h1>
          <p className="text-gray-600 mt-1">
            {mockHODData.user.department} • {mockHODData.user.organization}
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Button asChild>
            <Link href="/hod/announcements/create">
              <Bell className="w-4 h-4 mr-2" />
              Send Announcement
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/hod/teachers/invite">
              <UserPlus className="w-4 h-4 mr-2" />
              Invite Teacher
            </Link>
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <GraduationCap className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Teachers</p>
                <p className="text-xl font-bold">{mockHODData.overview.totalTeachers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Students</p>
                <p className="text-xl font-bold">{mockHODData.overview.totalStudents}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Subjects</p>
                <p className="text-xl font-bold">{mockHODData.overview.totalSubjects}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Active Chats</p>
                <p className="text-xl font-bold">{mockHODData.overview.activeChats}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Satisfaction</p>
                <p className="text-xl font-bold">{mockHODData.overview.avgSatisfaction}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Teachers Management */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <GraduationCap className="w-5 h-5" />
                  <span>Teaching Staff</span>
                </div>
                <Button size="sm" asChild>
                  <Link href="/hod/teachers">
                    View All
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Monitor teacher performance and student engagement
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockHODData.teachers.map((teacher) => (
                <div key={teacher.id} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-lg">{teacher.name}</h3>
                      <p className="text-sm text-gray-600">
                        {teacher.subjects.join(', ')}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={teacher.status === 'active' ? 'default' : 'secondary'}>
                        {teacher.status}
                      </Badge>
                      <Badge variant="outline">
                        {teacher.satisfaction}% satisfaction
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Students</p>
                      <p className="font-semibold">{teacher.students}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Rating</p>
                      <p className="font-semibold">{teacher.satisfaction}%</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Last Active</p>
                      <p className="font-semibold">{teacher.lastActive}</p>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 mt-3">
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/hod/teachers/${teacher.id}`}>
                        <BarChart3 className="w-4 h-4 mr-1" />
                        Analytics
                      </Link>
                    </Button>
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/hod/teachers/${teacher.id}/subjects`}>
                        <BookOpen className="w-4 h-4 mr-1" />
                        Subjects
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Subjects Overview */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BookOpen className="w-5 h-5" />
                <span>Subject Performance</span>
              </CardTitle>
              <CardDescription>
                Track progress across all subjects in your department
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockHODData.subjects.map((subject) => (
                <div key={subject.id} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium">{subject.name}</h4>
                      <p className="text-sm text-gray-600">by {subject.teacher}</p>
                    </div>
                    <Badge variant="outline">
                      {subject.students} students
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Average Progress</span>
                      <span>{subject.avgProgress}%</span>
                    </div>
                    <Progress value={subject.avgProgress} className="h-2" />
                  </div>
                  
                  <div className="flex items-center justify-between mt-3 text-sm text-gray-600">
                    <span>{subject.materials} materials</span>
                    <span>Status: {subject.status}</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" asChild>
                <Link href="/hod/announcements/create">
                  <Bell className="w-4 h-4 mr-2" />
                  Send Announcement
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/hod/teachers/invite">
                  <UserPlus className="w-4 h-4 mr-2" />
                  Invite Teacher
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/hod/analytics">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Department Analytics
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/hod/settings">
                  <Settings className="w-4 h-4 mr-2" />
                  Department Settings
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Recent Announcements */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <Bell className="w-5 h-5" />
                <span>Announcements</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {mockHODData.announcements.map((announcement) => (
                <div key={announcement.id} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-sm">{announcement.title}</h4>
                    <Badge 
                      variant={announcement.priority === 'high' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {announcement.priority}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{announcement.content}</p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{announcement.date}</span>
                    <Badge variant={announcement.sent ? 'default' : 'outline'} className="text-xs">
                      {announcement.sent ? 'Sent' : 'Draft'}
                    </Badge>
                  </div>
                </div>
              ))}
              
              <Button variant="outline" className="w-full" asChild>
                <Link href="/hod/announcements">Manage Announcements</Link>
              </Button>
            </CardContent>
          </Card>

          {/* Department Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Department Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Monthly Growth</span>
                <span className="font-semibold text-green-600">+{mockHODData.analytics.monthlyGrowth}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Student Engagement</span>
                <span className="font-semibold">{mockHODData.analytics.studentEngagement}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Teacher Utilization</span>
                <span className="font-semibold">{mockHODData.analytics.teacherUtilization}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">System Uptime</span>
                <span className="font-semibold text-green-600">{mockHODData.analytics.systemUptime}%</span>
              </div>
              
              <Button variant="outline" className="w-full" asChild>
                <Link href="/hod/analytics">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Detailed Analytics
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
