(()=>{var a={};a.id=40,a.ids=[40],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16335:(a,b,c)=>{Promise.resolve().then(c.bind(c,87962))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44689:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},46926:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dash)",{children:["student",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,84328)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,73035)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dash)/student/page",pathname:"/student",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dash)/student/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63191:(a,b,c)=>{Promise.resolve().then(c.bind(c,84328))},84328:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\AI\\\\ai\\\\frontend\\\\src\\\\app\\\\(dash)\\\\student\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\AI\\ai\\frontend\\src\\app\\(dash)\\student\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87142:(a,b,c)=>{"use strict";c.d(b,{D9:()=>l,Gs:()=>j,H:()=>k,LC:()=>m,oh:()=>i,ot:()=>h});var d=c(98483),e=c(59350),f=c(62185);let g=(0,d.v)()((0,e.lt)((0,e.Zr)(a=>({messages:[],isLoading:!1,isStreaming:!1,currentSubject:void 0,addMessage:b=>{let c={...b,id:crypto.randomUUID(),timestamp:new Date};a(a=>({messages:[...a.messages,c]}),!1,"addMessage")},updateLastMessage:b=>{a(a=>{let c=[...a.messages],d=c.length-1;return d>=0&&(c[d]={...c[d],...b}),{messages:c}},!1,"updateLastMessage")},setLoading:b=>{a({isLoading:b},!1,"setLoading")},setStreaming:b=>{a({isStreaming:b},!1,"setStreaming")},setCurrentSubject:b=>{a({currentSubject:b},!1,"setCurrentSubject")},clearMessages:()=>{a({messages:[]},!1,"clearMessages")}}),{name:"chat-store",partialize:a=>({messages:a.messages,currentSubject:a.currentSubject})}),{name:"chat-store"})),h=()=>g(a=>a.messages),i=()=>g(a=>a.isLoading),j=()=>g(a=>a.isStreaming),k=()=>g(a=>a.currentSubject),l=()=>g(a=>({addMessage:a.addMessage,updateLastMessage:a.updateLastMessage,setLoading:a.setLoading,setStreaming:a.setStreaming,setCurrentSubject:a.setCurrentSubject,clearMessages:a.clearMessages})),m=()=>{let{addMessage:a,updateLastMessage:b,setLoading:c,setStreaming:d}=l(),e=k(),h=async g=>{if(g.trim()){a({content:g.trim(),role:"user",subjectId:e?.id}),a({content:"",role:"assistant",subjectId:e?.id}),c(!0),d(!0);try{let a="";for await(let c of f.Pt.sendMessage(g,e?.id))if("text"===c.type)a+=c.content,b({content:a});else if("audio"===c.type&&c.audioUrl)b({hasAudio:!0,audioUrl:c.audioUrl,visemes:c.visemes});else if("complete"===c.type){b({content:a+c.content});break}}catch(a){console.error("Failed to send message:",a),b({content:"Sorry, I encountered an error. Please try again."})}finally{c(!1),d(!1)}}},i=async()=>{let a=g.getState().messages,b=[...a].reverse().find(a=>"user"===a.role);if(!b)return;let c=a.filter((b,c)=>c!==a.length-1||"assistant"!==a[a.length-1].role);g.setState({messages:c}),await h(b.content)};return{sendMessage:h,regenerateLastResponse:i,loadChatHistory:async a=>{c(!0);try{console.log("Loading chat history for subject:",a)}catch(a){console.error("Failed to load chat history:",a)}finally{c(!1)}}}}},87962:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u});var d=c(60687),e=c(43210),f=c(82136),g=c(16189),h=c(44493),i=c(29523),j=c(96834),k=c(82080),l=c(58887),m=c(48730),n=c(58869),o=c(10022),p=c(97840),q=c(44689),r=c(90631),s=c(87142),t=c(62185);function u(){let{data:a}=(0,f.useSession)(),b=(0,g.useRouter)(),c=(0,r.fR)(),u=(0,s.H)(),{setCurrentSubject:v}=(0,s.D9)(),{loadSubjects:w}=(0,r.Il)(),[x,y]=(0,e.useState)(!0),[z,A]=(0,e.useState)(!1),B=a=>{v(a),b.push("/student/chat")},C=a=>{b.push(`/student/materials?subject=${a}`)};return x?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Loading your subjects..."})]})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white",children:[(0,d.jsxs)("h1",{className:"text-2xl font-bold mb-2",children:["Welcome back, ",a?.user?.name,"!"]}),(0,d.jsx)("p",{className:"text-blue-100",children:"Ready to continue your learning journey? Choose a subject to start chatting with your AI tutor."})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(k.A,{className:"h-5 w-5 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Enrolled Subjects"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:c.length})]})]})})}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,d.jsx)(l.A,{className:"h-5 w-5 text-green-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Chat Sessions"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:"12"})]})]})})}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,d.jsx)(m.A,{className:"h-5 w-5 text-purple-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Study Time"}),(0,d.jsx)("p",{className:"text-2xl font-bold",children:"24h"})]})]})})})]}),u&&(0,d.jsxs)(h.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsxs)(h.ZB,{className:"flex items-center space-x-2",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsx)("span",{children:"Continue Learning"})]}),(0,d.jsxs)(h.BT,{children:["You were last studying ",u.name]})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,d.jsx)(n.A,{className:"h-5 w-5 text-blue-600"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:u.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["with ",u.teacherName]})]})]}),(0,d.jsxs)(i.$,{onClick:()=>B(u),children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Continue Chat"]})]})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Your Subjects"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:c.length>0?c.map(a=>(0,d.jsxs)(h.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsxs)(h.ZB,{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-lg",children:a.name}),(0,d.jsx)(j.E,{variant:"secondary",children:"Active"})]}),(0,d.jsx)(h.BT,{children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.teacherName})]})})]}),(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:a.description||"Enhance your knowledge with AI-powered tutoring."}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(i.$,{onClick:()=>B(a),className:"w-full",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Start AI Chat"]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,d.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>C(a.id),children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-1"}),"Materials"]}),(0,d.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>b.push(`/student/practice?subject=${a.id}`),children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Practice"]})]})]})]})]},a.id)):t.jy.subjects.map(a=>(0,d.jsxs)(h.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsxs)(h.ZB,{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-lg",children:a.name}),(0,d.jsx)(j.E,{variant:"secondary",children:"Active"})]}),(0,d.jsx)(h.BT,{children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(n.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.teacherName})]})})]}),(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:a.description||"Enhance your knowledge with AI-powered tutoring."}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(i.$,{onClick:()=>B(a),className:"w-full",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Start AI Chat"]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,d.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>C(a.id),children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-1"}),"Materials"]}),(0,d.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>b.push(`/student/practice?subject=${a.id}`),children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Practice"]})]})]})]})]},a.id))})]}),(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"Recent Activity"}),(0,d.jsx)(h.BT,{children:"Your latest learning sessions"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)(l.A,{className:"h-5 w-5 text-blue-600"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"font-medium",children:"Japanese Language Chat"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Discussed grammar patterns - 2 hours ago"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)(q.A,{className:"h-5 w-5 text-green-600"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"font-medium",children:"Pronunciation Practice"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Completed voice exercises - 1 day ago"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)(o.A,{className:"h-5 w-5 text-purple-600"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"font-medium",children:"Study Material Review"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Read Chapter 5 notes - 2 days ago"})]})]})]})})]})]})}},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},97840:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,625,393,484,215,436],()=>b(b.s=46926));module.exports=c})();