import { fontVariables } from '@/lib/fonts';

export default function TestFontsPage() {
  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold">Font Loading Test</h1>
      
      <div className="space-y-2">
        <p className="font-sans">
          This text should use Geist Sans font family.
        </p>
        <p className="font-mono">
          This text should use Geist Mono font family.
        </p>
      </div>
      
      <div className="text-sm text-gray-600">
        <p>Font variables loaded:</p>
        <ul>
          <li>Sans: {fontVariables.sans || 'Not loaded'}</li>
          <li>Mono: {fontVariables.mono || 'Not loaded'}</li>
        </ul>
      </div>
    </div>
  );
}
