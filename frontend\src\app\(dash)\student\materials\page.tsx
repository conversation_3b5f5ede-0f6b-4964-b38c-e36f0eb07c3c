'use client';

// Student Materials Page

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  ArrowLeft,
  FileText,
  Video,
  Headphones,
  Image as ImageIcon,
  Download,
  Search,
  BookOpen,
  Calendar
} from 'lucide-react';
import { useSubjects } from '@/store/useUser';
import { mockData } from '@/lib/api';

interface Material {
  id: string;
  title: string;
  description?: string;
  fileType: 'pdf' | 'video' | 'audio' | 'image' | 'document';
  fileSize: string;
  uploadedBy: string;
  uploadedAt: Date;
  subjectId: string;
  subjectName: string;
}

export default function StudentMaterialsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const subjectFilter = searchParams.get('subject');
  const { data: session } = useSession();
  console.log('User session:', session); // For debugging
  const subjects = useSubjects();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState(subjectFilter || '');
  const [isLoading, setIsLoading] = useState(true);

  const availableSubjects = subjects.length > 0 ? subjects : mockData.subjects;

  // Mock materials data
  const mockMaterials: Material[] = [
    {
      id: '1',
      title: 'Introduction to Hiragana',
      description: 'Basic Japanese writing system fundamentals',
      fileType: 'pdf',
      fileSize: '2.5 MB',
      uploadedBy: 'Tanaka Sensei',
      uploadedAt: new Date('2024-01-15'),
      subjectId: '1',
      subjectName: 'Japanese Language'
    },
    {
      id: '2',
      title: 'Pronunciation Guide',
      description: 'Audio guide for proper Japanese pronunciation',
      fileType: 'audio',
      fileSize: '15.2 MB',
      uploadedBy: 'Tanaka Sensei',
      uploadedAt: new Date('2024-01-20'),
      subjectId: '1',
      subjectName: 'Japanese Language'
    },
    {
      id: '3',
      title: 'Algebra Basics',
      description: 'Fundamental algebraic concepts and equations',
      fileType: 'pdf',
      fileSize: '4.1 MB',
      uploadedBy: 'Smith Sensei',
      uploadedAt: new Date('2024-01-18'),
      subjectId: '2',
      subjectName: 'Mathematics'
    },
    {
      id: '4',
      title: 'Grammar Patterns Video',
      description: 'Visual explanation of common Japanese grammar structures',
      fileType: 'video',
      fileSize: '125.8 MB',
      uploadedBy: 'Tanaka Sensei',
      uploadedAt: new Date('2024-01-22'),
      subjectId: '1',
      subjectName: 'Japanese Language'
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'video':
        return <Video className="h-5 w-5 text-red-600" />;
      case 'audio':
        return <Headphones className="h-5 w-5 text-purple-600" />;
      case 'image':
        return <ImageIcon className="h-5 w-5 text-green-600" />;
      default:
        return <FileText className="h-5 w-5 text-blue-600" />;
    }
  };

  const getFileTypeLabel = (fileType: string) => {
    switch (fileType) {
      case 'pdf':
        return 'PDF';
      case 'video':
        return 'Video';
      case 'audio':
        return 'Audio';
      case 'image':
        return 'Image';
      case 'document':
        return 'Document';
      default:
        return 'File';
    }
  };

  const filteredMaterials = mockMaterials.filter(material => {
    const matchesSearch = material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         material.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesSubject = !selectedSubject || material.subjectId === selectedSubject;
    return matchesSearch && matchesSubject;
  });

  const handleDownload = (materialId: string) => {
    // TODO: Implement actual download when backend is ready
    console.log('Downloading material:', materialId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading materials...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/student')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <BookOpen className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Study Materials</h1>
              <p className="text-gray-600">Access learning resources from your teachers</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search materials..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                <SelectTrigger>
                  <SelectValue placeholder="All subjects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All subjects</SelectItem>
                  {availableSubjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Materials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMaterials.map((material) => (
          <Card key={material.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  {getFileIcon(material.fileType)}
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">{material.title}</CardTitle>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {getFileTypeLabel(material.fileType)}
                      </Badge>
                      <span className="text-xs text-gray-500">{material.fileSize}</span>
                    </div>
                  </div>
                </div>
              </div>
              {material.description && (
                <CardDescription className="line-clamp-2">
                  {material.description}
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <BookOpen className="h-4 w-4" />
                    <span>{material.subjectName}</span>
                  </div>
                  <div className="flex items-center space-x-2 mt-1">
                    <Calendar className="h-4 w-4" />
                    <span>
                      Uploaded {material.uploadedAt.toLocaleDateString()} by {material.uploadedBy}
                    </span>
                  </div>
                </div>
                
                <Button
                  onClick={() => handleDownload(material.id)}
                  className="w-full"
                  size="sm"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredMaterials.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No materials found
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || selectedSubject 
                ? 'Try adjusting your search or filter criteria.'
                : 'Your teachers haven\'t uploaded any materials yet.'}
            </p>
            {(searchQuery || selectedSubject) && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedSubject('');
                }}
              >
                Clear filters
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
