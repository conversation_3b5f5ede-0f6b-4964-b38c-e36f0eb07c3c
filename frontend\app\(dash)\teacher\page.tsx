import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  BookOpen, 
  Upload, 
  MessageSquare,
  Settings,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  FileText,
  Bot
} from 'lucide-react';
import Link from 'next/link';

// Mock data - would be fetched from API in production
const mockTeacherData = {
  user: {
    name: 'Dr. <PERSON>',
    subjects: ['Japanese Language', 'Cultural Studies'],
    totalStudents: 45,
    activeChats: 12
  },
  subjects: [
    {
      id: '1',
      name: 'Japanese Language',
      students: 28,
      materials: 15,
      avgProgress: 72,
      recentActivity: '2 hours ago',
      pendingReviews: 3
    },
    {
      id: '2',
      name: 'Cultural Studies', 
      students: 17,
      materials: 8,
      avgProgress: 65,
      recentActivity: '5 hours ago',
      pendingReviews: 1
    }
  ],
  recentMaterials: [
    {
      id: '1',
      title: 'N3 Grammar Patterns',
      subject: 'Japanese Language',
      uploadedAt: '2024-01-15',
      downloads: 24,
      type: 'PDF'
    },
    {
      id: '2',
      title: 'Pronunciation Guide',
      subject: 'Japanese Language',
      uploadedAt: '2024-01-14', 
      downloads: 18,
      type: 'Audio'
    }
  ],
  pendingReviews: [
    {
      id: '1',
      student: 'Alex Johnson',
      question: 'How do I use keigo in business situations?',
      aiResponse: 'Keigo (敬語) in business...',
      timestamp: '10 minutes ago',
      subject: 'Japanese Language'
    },
    {
      id: '2',
      student: 'Maria Garcia',
      question: 'What is the difference between wa and ga particles?',
      aiResponse: 'The particles wa (は) and ga (が)...',
      timestamp: '25 minutes ago',
      subject: 'Japanese Language'
    }
  ],
  analytics: {
    totalQuestions: 156,
    avgResponseTime: '2.3 minutes',
    studentSatisfaction: 94,
    mostActiveHour: '14:00-15:00'
  }
};

export default function TeacherDashboard() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome, {mockTeacherData.user.name}! 👩‍🏫
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your classes and help students learn
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Badge variant="secondary" className="px-3 py-1">
            <Users className="w-4 h-4 mr-1" />
            {mockTeacherData.user.totalStudents} students
          </Badge>
          <Badge variant="outline" className="px-3 py-1">
            <MessageSquare className="w-4 h-4 mr-1" />
            {mockTeacherData.user.activeChats} active chats
          </Badge>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Students</p>
                <p className="text-xl font-bold">{mockTeacherData.user.totalStudents}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Questions Today</p>
                <p className="text-xl font-bold">{mockTeacherData.analytics.totalQuestions}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Response</p>
                <p className="text-xl font-bold">{mockTeacherData.analytics.avgResponseTime}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Satisfaction</p>
                <p className="text-xl font-bold">{mockTeacherData.analytics.studentSatisfaction}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* My Subjects */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <BookOpen className="w-5 h-5" />
                  <span>My Subjects</span>
                </div>
                <Button size="sm" asChild>
                  <Link href="/teacher/subjects/create">
                    Add Subject
                  </Link>
                </Button>
              </CardTitle>
              <CardDescription>
                Manage your teaching subjects and track student progress
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockTeacherData.subjects.map((subject) => (
                <div key={subject.id} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-lg">{subject.name}</h3>
                      <p className="text-sm text-gray-600">
                        {subject.students} students • {subject.materials} materials
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {subject.pendingReviews > 0 && (
                        <Badge variant="destructive">
                          {subject.pendingReviews} pending
                        </Badge>
                      )}
                      <Badge variant="outline">
                        {subject.avgProgress}% avg progress
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Class Average Progress</span>
                      <span>{subject.avgProgress}%</span>
                    </div>
                    <Progress value={subject.avgProgress} className="h-2" />
                  </div>
                  
                  <div className="flex items-center justify-between mt-3">
                    <p className="text-xs text-gray-500">
                      Last activity: {subject.recentActivity}
                    </p>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/teacher/subjects/${subject.id}`}>
                          <BarChart3 className="w-4 h-4 mr-1" />
                          Analytics
                        </Link>
                      </Button>
                      <Button size="sm" asChild>
                        <Link href={`/teacher/subjects/${subject.id}/materials`}>
                          <Upload className="w-4 h-4 mr-1" />
                          Materials
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* AI Response Reviews */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bot className="w-5 h-5" />
                <span>Pending AI Response Reviews</span>
                <Badge variant="destructive">
                  {mockTeacherData.pendingReviews.length}
                </Badge>
              </CardTitle>
              <CardDescription>
                Review and approve AI-generated responses to student questions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockTeacherData.pendingReviews.map((review) => (
                <div key={review.id} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium">{review.student}</h4>
                      <p className="text-sm text-gray-600">{review.subject}</p>
                      <p className="text-xs text-gray-500">{review.timestamp}</p>
                    </div>
                    <Badge variant="outline">Pending Review</Badge>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Question:</p>
                      <p className="text-sm bg-blue-50 p-2 rounded">{review.question}</p>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium text-gray-700">AI Response:</p>
                      <p className="text-sm bg-gray-50 p-2 rounded">{review.aiResponse}</p>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 mt-3">
                    <Button size="sm" variant="outline">
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Approve
                    </Button>
                    <Button size="sm" variant="outline">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      Edit & Approve
                    </Button>
                    <Button size="sm" variant="destructive">
                      Reject
                    </Button>
                  </div>
                </div>
              ))}
              
              <Button variant="outline" className="w-full" asChild>
                <Link href="/teacher/reviews">View All Reviews</Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" asChild>
                <Link href="/teacher/materials/upload">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Material
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/teacher/avatar">
                  <Bot className="w-4 h-4 mr-2" />
                  Customize AI Avatar
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/teacher/analytics">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  View Analytics
                </Link>
              </Button>
              
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href="/teacher/settings">
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Recent Materials */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Materials</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {mockTeacherData.recentMaterials.map((material) => (
                <div key={material.id} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{material.title}</h4>
                      <p className="text-xs text-gray-500">{material.subject}</p>
                      <p className="text-xs text-gray-400">{material.uploadedAt}</p>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {material.downloads} downloads
                    </Badge>
                  </div>
                </div>
              ))}
              
              <Button variant="outline" className="w-full" asChild>
                <Link href="/teacher/materials">Manage All Materials</Link>
              </Button>
            </CardContent>
          </Card>

          {/* Teaching Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Today's Analytics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Questions Answered</span>
                <span className="font-semibold">{mockTeacherData.analytics.totalQuestions}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Avg Response Time</span>
                <span className="font-semibold">{mockTeacherData.analytics.avgResponseTime}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Student Satisfaction</span>
                <span className="font-semibold">{mockTeacherData.analytics.studentSatisfaction}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Peak Hours</span>
                <span className="font-semibold">{mockTeacherData.analytics.mostActiveHour}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
