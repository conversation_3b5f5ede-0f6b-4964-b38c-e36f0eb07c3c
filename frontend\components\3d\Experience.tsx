'use client';

import { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { 
  Environment, 
  CameraControls, 
  Html, 
  Loader,
  Float,
  ContactShadows,
  PresentationControls
} from '@react-three/drei';
import { TeacherModel } from './TeacherModel';
import { Blackboard } from './Blackboard';
import { useEnvironmentState, useCameraState, useBlackboardState } from '@/store/useAITeacher';

interface ExperienceProps {
  className?: string;
  enableControls?: boolean;
  showBlackboard?: boolean;
}

export function Experience({ 
  className = "w-full h-full", 
  enableControls = true,
  showBlackboard = true 
}: ExperienceProps) {
  const { preset, lightingIntensity } = useEnvironmentState();
  const { position, target } = useCameraState();
  const { show: showBlackboardOverlay } = useBlackboardState();

  return (
    <div className={className}>
      <Canvas
        camera={{
          position: position,
          fov: 45,
          near: 0.1,
          far: 100
        }}
        shadows
        dpr={[1, 2]}
        gl={{ 
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        {/* Lighting Setup */}
        <ambientLight intensity={0.4 * lightingIntensity} />
        <directionalLight
          position={[5, 5, 5]}
          intensity={0.8 * lightingIntensity}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
          shadow-camera-far={50}
          shadow-camera-left={-10}
          shadow-camera-right={10}
          shadow-camera-top={10}
          shadow-camera-bottom={-10}
        />
        <pointLight position={[-5, 5, 5]} intensity={0.3 * lightingIntensity} />

        {/* Environment */}
        <Environment 
          preset={preset} 
          background={false}
          blur={0.8}
        />

        {/* Camera Controls */}
        {enableControls && (
          <CameraControls
            makeDefault
            minPolarAngle={Math.PI / 6}
            maxPolarAngle={Math.PI - Math.PI / 6}
            minAzimuthAngle={-Math.PI / 4}
            maxAzimuthAngle={Math.PI / 4}
            minDistance={2}
            maxDistance={8}
            target={target}
          />
        )}

        {/* 3D Scene Content */}
        <Suspense fallback={<TeacherModelFallback />}>
          {/* Ground/Floor */}
          <mesh 
            rotation={[-Math.PI / 2, 0, 0]} 
            position={[0, -0.1, 0]}
            receiveShadow
          >
            <planeGeometry args={[20, 20]} />
            <meshStandardMaterial color="#f0f0f0" />
          </mesh>

          {/* Contact Shadows */}
          <ContactShadows
            position={[0, 0, 0]}
            opacity={0.4}
            scale={10}
            blur={2}
            far={4}
          />

          {/* Teacher Avatar */}
          <PresentationControls
            enabled={enableControls}
            global={false}
            cursor={true}
            snap={false}
            speed={1}
            zoom={1}
            rotation={[0, 0, 0]}
            polar={[-Math.PI / 3, Math.PI / 3]}
            azimuth={[-Math.PI / 1.4, Math.PI / 1.4]}
          >
            <Float
              speed={1}
              rotationIntensity={0.1}
              floatIntensity={0.1}
              floatingRange={[0, 0.1]}
            >
              <TeacherModel />
            </Float>
          </PresentationControls>

          {/* Blackboard (3D Object) */}
          {showBlackboard && (
            <Blackboard position={[0, 2, -2]} />
          )}

          {/* HTML Overlay for Blackboard Text */}
          {showBlackboardOverlay && (
            <Html
              position={[0, 2.5, -1.9]}
              transform
              occlude
              distanceFactor={10}
              className="pointer-events-none"
            >
              <BlackboardOverlay />
            </Html>
          )}
        </Suspense>

        {/* Performance Monitor (Development) */}
        {process.env.NODE_ENV === 'development' && (
          <Html position={[0, 0, 0]}>
            <div className="fixed top-4 right-4 bg-black/50 text-white p-2 rounded text-xs">
              <div>Camera: [{position.map(p => p.toFixed(1)).join(', ')}]</div>
              <div>Environment: {preset}</div>
              <div>Lighting: {(lightingIntensity * 100).toFixed(0)}%</div>
            </div>
          </Html>
        )}
      </Canvas>

      {/* Loading Fallback */}
      <Loader 
        containerStyles={{
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white'
        }}
        innerStyles={{
          background: 'white'
        }}
        barStyles={{
          background: '#3b82f6'
        }}
        dataStyles={{
          color: 'white',
          fontSize: '14px'
        }}
      />
    </div>
  );
}

// Fallback component for teacher model loading
function TeacherModelFallback() {
  return (
    <mesh position={[0, 1, 0]}>
      <boxGeometry args={[0.5, 1.8, 0.3]} />
      <meshStandardMaterial color="#cccccc" wireframe />
      <Html center>
        <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 text-center">
          <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Loading AI Teacher...</p>
        </div>
      </Html>
    </mesh>
  );
}

// Blackboard text overlay component
function BlackboardOverlay() {
  const { text } = useBlackboardState();

  if (!text) return null;

  return (
    <div className="bg-green-900/90 backdrop-blur-sm border-2 border-green-700 rounded-lg p-6 max-w-md">
      <div className="text-green-100 font-mono text-sm leading-relaxed">
        {text.split('\n').map((line, index) => (
          <div key={index} className="mb-1">
            {line}
          </div>
        ))}
      </div>
      
      {/* Chalk effect border */}
      <div className="absolute inset-0 border-2 border-green-400/30 rounded-lg pointer-events-none"></div>
      
      {/* Corner decorations */}
      <div className="absolute top-1 left-1 w-2 h-2 border-l-2 border-t-2 border-green-400/50"></div>
      <div className="absolute top-1 right-1 w-2 h-2 border-r-2 border-t-2 border-green-400/50"></div>
      <div className="absolute bottom-1 left-1 w-2 h-2 border-l-2 border-b-2 border-green-400/50"></div>
      <div className="absolute bottom-1 right-1 w-2 h-2 border-r-2 border-b-2 border-green-400/50"></div>
    </div>
  );
}

// Environment presets for easy switching
export const environmentPresets = [
  { value: 'sunset', label: 'Sunset', description: 'Warm, golden lighting' },
  { value: 'dawn', label: 'Dawn', description: 'Soft morning light' },
  { value: 'night', label: 'Night', description: 'Cool, moonlit atmosphere' },
  { value: 'warehouse', label: 'Warehouse', description: 'Industrial setting' },
  { value: 'forest', label: 'Forest', description: 'Natural outdoor environment' },
  { value: 'apartment', label: 'Apartment', description: 'Indoor living space' },
] as const;

// Camera preset positions
export const cameraPresets = {
  default: { position: [0, 1.6, 3] as [number, number, number], target: [0, 1.6, 0] as [number, number, number] },
  closeUp: { position: [0, 1.8, 1.5] as [number, number, number], target: [0, 1.7, 0] as [number, number, number] },
  wideShot: { position: [2, 2, 4] as [number, number, number], target: [0, 1.6, 0] as [number, number, number] },
  profile: { position: [3, 1.6, 0] as [number, number, number], target: [0, 1.6, 0] as [number, number, number] },
  blackboard: { position: [0, 2.5, 1] as [number, number, number], target: [0, 2, -2] as [number, number, number] },
} as const;
