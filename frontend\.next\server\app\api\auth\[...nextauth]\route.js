const CHUNK_PUBLIC_PATH = "server/app/api/auth/[...nextauth]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/27da8_next_663660bb._.js");
runtime.loadChunk("server/chunks/27da8_next-auth_342d42df._.js");
runtime.loadChunk("server/chunks/27da8_openid-client_3ad46bdd._.js");
runtime.loadChunk("server/chunks/27da8_jose_dist_node_cjs_19c7104d._.js");
runtime.loadChunk("server/chunks/27da8_55d39523._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__c153b36e._.js");
runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Desktop/AI/ai/frontend/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/Desktop/AI/ai/frontend/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Desktop/AI/ai/frontend/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
