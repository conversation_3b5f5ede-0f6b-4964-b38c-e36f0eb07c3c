// Zustand store for AI Teacher 3D Avatar state management
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// Types for AI Teacher Avatar
export interface TeacherAvatar {
  id: string;
  name: string;
  modelUrl: string;
  voiceId: string;
  personality: 'friendly' | 'professional' | 'enthusiastic' | 'calm';
  language: 'en' | 'ja' | 'es' | 'fr';
  appearance: {
    hairColor: string;
    skinTone: string;
    outfit: string;
    accessories: string[];
  };
}

export interface Viseme {
  time: number;
  value: string;
  weight: number;
}

export interface TeacherState {
  // Current avatar configuration
  currentAvatar: TeacherAvatar | null;
  
  // 3D Scene state
  isModelLoaded: boolean;
  isModelLoading: boolean;
  modelError: string | null;
  
  // Animation state
  isAnimating: boolean;
  currentAnimation: string | null;
  
  // Speech and lip-sync
  isSpeaking: boolean;
  currentAudio: HTMLAudioElement | null;
  visemes: Viseme[];
  currentVisemeIndex: number;
  
  // Blackboard overlay
  blackboardText: string;
  showBlackboard: boolean;
  
  // Camera controls
  cameraPosition: [number, number, number];
  cameraTarget: [number, number, number];
  
  // Environment settings
  environmentPreset: 'sunset' | 'dawn' | 'night' | 'warehouse' | 'forest' | 'apartment';
  lightingIntensity: number;
}

export interface TeacherActions {
  // Avatar management
  setCurrentAvatar: (avatar: TeacherAvatar) => void;
  updateAvatarAppearance: (appearance: Partial<TeacherAvatar['appearance']>) => void;
  
  // Model loading
  setModelLoading: (loading: boolean) => void;
  setModelLoaded: (loaded: boolean) => void;
  setModelError: (error: string | null) => void;
  
  // Animation controls
  playAnimation: (animationName: string) => void;
  stopAnimation: () => void;
  setAnimating: (animating: boolean) => void;
  
  // Speech and lip-sync
  startSpeaking: (audioUrl: string, visemes: Viseme[]) => void;
  stopSpeaking: () => void;
  updateVisemeIndex: (index: number) => void;
  
  // Blackboard controls
  setBlackboardText: (text: string) => void;
  showBlackboard: (show: boolean) => void;
  
  // Camera controls
  setCameraPosition: (position: [number, number, number]) => void;
  setCameraTarget: (target: [number, number, number]) => void;
  resetCamera: () => void;
  
  // Environment controls
  setEnvironmentPreset: (preset: TeacherState['environmentPreset']) => void;
  setLightingIntensity: (intensity: number) => void;
  
  // Utility actions
  resetTeacherState: () => void;
}

export type TeacherStore = TeacherState & TeacherActions;

// Default avatar configuration
const defaultAvatar: TeacherAvatar = {
  id: 'default-teacher',
  name: 'AI Sensei',
  modelUrl: '/models/teacher-avatar.glb',
  voiceId: 'voice-1',
  personality: 'friendly',
  language: 'en',
  appearance: {
    hairColor: '#8B4513',
    skinTone: '#FDBCB4',
    outfit: 'casual',
    accessories: ['glasses'],
  },
};

// Initial state
const initialState: TeacherState = {
  currentAvatar: defaultAvatar,
  isModelLoaded: false,
  isModelLoading: false,
  modelError: null,
  isAnimating: false,
  currentAnimation: null,
  isSpeaking: false,
  currentAudio: null,
  visemes: [],
  currentVisemeIndex: 0,
  blackboardText: '',
  showBlackboard: false,
  cameraPosition: [0, 1.6, 3],
  cameraTarget: [0, 1.6, 0],
  environmentPreset: 'sunset',
  lightingIntensity: 1,
};

// Create the store
export const useAITeacher = create<TeacherStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Avatar management
      setCurrentAvatar: (avatar) => {
        set({ currentAvatar: avatar }, false, 'setCurrentAvatar');
      },

      updateAvatarAppearance: (appearance) => {
        const currentAvatar = get().currentAvatar;
        if (currentAvatar) {
          set({
            currentAvatar: {
              ...currentAvatar,
              appearance: { ...currentAvatar.appearance, ...appearance }
            }
          }, false, 'updateAvatarAppearance');
        }
      },

      // Model loading
      setModelLoading: (loading) => {
        set({ isModelLoading: loading }, false, 'setModelLoading');
      },

      setModelLoaded: (loaded) => {
        set({ 
          isModelLoaded: loaded,
          isModelLoading: false,
          modelError: loaded ? null : get().modelError
        }, false, 'setModelLoaded');
      },

      setModelError: (error) => {
        set({ 
          modelError: error,
          isModelLoading: false,
          isModelLoaded: false
        }, false, 'setModelError');
      },

      // Animation controls
      playAnimation: (animationName) => {
        set({
          currentAnimation: animationName,
          isAnimating: true
        }, false, 'playAnimation');
      },

      stopAnimation: () => {
        set({
          currentAnimation: null,
          isAnimating: false
        }, false, 'stopAnimation');
      },

      setAnimating: (animating) => {
        set({ isAnimating: animating }, false, 'setAnimating');
      },

      // Speech and lip-sync
      startSpeaking: (audioUrl, visemes) => {
        // Stop any existing audio
        const currentAudio = get().currentAudio;
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.currentTime = 0;
        }

        // Create new audio element
        const audio = new Audio(audioUrl);
        
        audio.onended = () => {
          get().stopSpeaking();
        };

        audio.onerror = () => {
          console.error('Failed to load audio:', audioUrl);
          get().stopSpeaking();
        };

        set({
          isSpeaking: true,
          currentAudio: audio,
          visemes,
          currentVisemeIndex: 0
        }, false, 'startSpeaking');

        // Start playing audio
        audio.play().catch(console.error);
      },

      stopSpeaking: () => {
        const currentAudio = get().currentAudio;
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.currentTime = 0;
        }

        set({
          isSpeaking: false,
          currentAudio: null,
          visemes: [],
          currentVisemeIndex: 0
        }, false, 'stopSpeaking');
      },

      updateVisemeIndex: (index) => {
        set({ currentVisemeIndex: index }, false, 'updateVisemeIndex');
      },

      // Blackboard controls
      setBlackboardText: (text) => {
        set({ blackboardText: text }, false, 'setBlackboardText');
      },

      showBlackboard: (show) => {
        set({ showBlackboard: show }, false, 'showBlackboard');
      },

      // Camera controls
      setCameraPosition: (position) => {
        set({ cameraPosition: position }, false, 'setCameraPosition');
      },

      setCameraTarget: (target) => {
        set({ cameraTarget: target }, false, 'setCameraTarget');
      },

      resetCamera: () => {
        set({
          cameraPosition: initialState.cameraPosition,
          cameraTarget: initialState.cameraTarget
        }, false, 'resetCamera');
      },

      // Environment controls
      setEnvironmentPreset: (preset) => {
        set({ environmentPreset: preset }, false, 'setEnvironmentPreset');
      },

      setLightingIntensity: (intensity) => {
        set({ lightingIntensity: intensity }, false, 'setLightingIntensity');
      },

      // Utility actions
      resetTeacherState: () => {
        const currentAudio = get().currentAudio;
        if (currentAudio) {
          currentAudio.pause();
        }
        set(initialState, false, 'resetTeacherState');
      },
    }),
    {
      name: 'ai-teacher-store',
    }
  )
);

// Selectors for optimized re-renders
export const useCurrentAvatar = () => useAITeacher((state) => state.currentAvatar);
export const useModelState = () => useAITeacher((state) => ({
  isLoaded: state.isModelLoaded,
  isLoading: state.isModelLoading,
  error: state.modelError
}));
export const useAnimationState = () => useAITeacher((state) => ({
  isAnimating: state.isAnimating,
  currentAnimation: state.currentAnimation
}));
export const useSpeechState = () => useAITeacher((state) => ({
  isSpeaking: state.isSpeaking,
  visemes: state.visemes,
  currentVisemeIndex: state.currentVisemeIndex
}));
export const useBlackboardState = () => useAITeacher((state) => ({
  text: state.blackboardText,
  show: state.showBlackboard
}));
export const useCameraState = () => useAITeacher((state) => ({
  position: state.cameraPosition,
  target: state.cameraTarget
}));
export const useEnvironmentState = () => useAITeacher((state) => ({
  preset: state.environmentPreset,
  lightingIntensity: state.lightingIntensity
}));

// Action selectors
export const useSetCurrentAvatar = () => useAITeacher((state) => state.setCurrentAvatar);
export const useUpdateAvatarAppearance = () => useAITeacher((state) => state.updateAvatarAppearance);
export const usePlayAnimation = () => useAITeacher((state) => state.playAnimation);
export const useStopAnimation = () => useAITeacher((state) => state.stopAnimation);
export const useStartSpeaking = () => useAITeacher((state) => state.startSpeaking);
export const useStopSpeaking = () => useAITeacher((state) => state.stopSpeaking);
export const useSetBlackboardText = () => useAITeacher((state) => state.setBlackboardText);
export const useShowBlackboard = () => useAITeacher((state) => state.showBlackboard);
export const useSetCameraPosition = () => useAITeacher((state) => state.setCameraPosition);
export const useSetEnvironmentPreset = () => useAITeacher((state) => state.setEnvironmentPreset);
