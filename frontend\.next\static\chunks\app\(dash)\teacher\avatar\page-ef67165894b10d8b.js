(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[791],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>c});var r=a(5155),t=a(2115),i=a(9708),n=a(2085),l=a(9434);let o=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:a,variant:t,size:n,asChild:c=!1,...d}=e,h=c?i.DX:"button";return(0,r.jsx)(h,{className:(0,l.cn)(o({variant:t,size:n,className:a})),ref:s,...d})});c.displayName="Button"},4718:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>ea});var r=a(5155),t=a(2115),i=a(5695),n=a(6695),l=a(285),o=a(5057),c=a(4073),d=a(9434);let h=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsxs)(c.bL,{ref:s,className:(0,d.cn)("relative flex w-full touch-none select-none items-center",a),...t,children:[(0,r.jsx)(c.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,r.jsx)(c.Q6,{className:"absolute h-full bg-primary"})}),(0,r.jsx)(c.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});h.displayName=c.bL.displayName;var u=a(9409),m=a(2473);let x=m.bL,p=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(m.B8,{ref:s,className:(0,d.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...t})});p.displayName=m.B8.displayName;let f=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(m.l9,{ref:s,className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...t})});f.displayName=m.l9.displayName;let j=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(m.UC,{ref:s,className:(0,d.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...t})});j.displayName=m.UC.displayName;var v=a(6126),g=a(5169),b=a(5690),y=a(5657),w=a(4229),N=a(3127),S=a(5273),k=a(1007),C=a(7558),A=a(5026),M=a(9957),T=a(2515),R=a(8435),B=a(3758),P=a(4688),D=a(461),F=a(3498),V=a(5571),I=a(3264),_=a(5521),z=a(6786);let G=(0,_.v)()((0,z.lt)(e=>({avatar:void 0,isModelLoaded:!1,isSpeaking:!1,currentVisemes:[],blackboardText:"",setAvatar:s=>{e({avatar:s},!1,"setAvatar")},setModelLoaded:s=>{e({isModelLoaded:s},!1,"setModelLoaded")},setSpeaking:s=>{e({isSpeaking:s},!1,"setSpeaking"),s||e({currentVisemes:[]},!1,"clearVisemes")},setCurrentVisemes:s=>{e({currentVisemes:s},!1,"setCurrentVisemes")},setBlackboardText:s=>{e({blackboardText:s},!1,"setBlackboardText")}}),{name:"ai-teacher-store"})),E=()=>G(e=>e.avatar),L=()=>G(e=>e.isSpeaking),J=()=>G(e=>({setAvatar:e.setAvatar,setModelLoaded:e.setModelLoaded,setSpeaking:e.setSpeaking,setCurrentVisemes:e.setCurrentVisemes,setBlackboardText:e.setBlackboardText})),H={modelUrl:"/models/teacher-default.glb",voiceId:"default-voice",voiceSettings:{pitch:1,speed:1,volume:.8},appearance:{skinTone:"medium",hairColor:"brown",eyeColor:"brown",clothing:"professional"},personality:{tone:"friendly",enthusiasm:7,patience:8}},U={sil:"viseme_sil",PP:"viseme_PP",FF:"viseme_FF",TH:"viseme_TH",DD:"viseme_DD",kk:"viseme_kk",CH:"viseme_CH",SS:"viseme_SS",nn:"viseme_nn",RR:"viseme_RR",aa:"viseme_aa",E:"viseme_E",I:"viseme_I",O:"viseme_O",U:"viseme_U"};function W(){return(0,r.jsxs)("group",{children:[(0,r.jsxs)("mesh",{position:[0,1.6,0],castShadow:!0,children:[(0,r.jsx)("sphereGeometry",{args:[.15,32,32]}),(0,r.jsx)("meshStandardMaterial",{color:"#FDBCB4"})]}),(0,r.jsxs)("mesh",{position:[0,1,0],castShadow:!0,children:[(0,r.jsx)("cylinderGeometry",{args:[.2,.25,.8]}),(0,r.jsx)("meshStandardMaterial",{color:"#4A90E2"})]}),(0,r.jsxs)("mesh",{position:[-.3,1.2,0],rotation:[0,0,.3],castShadow:!0,children:[(0,r.jsx)("cylinderGeometry",{args:[.05,.05,.6]}),(0,r.jsx)("meshStandardMaterial",{color:"#FDBCB4"})]}),(0,r.jsxs)("mesh",{position:[.3,1.2,0],rotation:[0,0,-.3],castShadow:!0,children:[(0,r.jsx)("cylinderGeometry",{args:[.05,.05,.6]}),(0,r.jsx)("meshStandardMaterial",{color:"#FDBCB4"})]}),(0,r.jsxs)("mesh",{position:[-.1,.2,0],castShadow:!0,children:[(0,r.jsx)("cylinderGeometry",{args:[.08,.08,.8]}),(0,r.jsx)("meshStandardMaterial",{color:"#2C3E50"})]}),(0,r.jsxs)("mesh",{position:[.1,.2,0],castShadow:!0,children:[(0,r.jsx)("cylinderGeometry",{args:[.08,.08,.8]}),(0,r.jsx)("meshStandardMaterial",{color:"#2C3E50"})]}),(0,r.jsxs)("mesh",{position:[-.05,1.65,.12],castShadow:!0,children:[(0,r.jsx)("sphereGeometry",{args:[.02,16,16]}),(0,r.jsx)("meshStandardMaterial",{color:"#000000"})]}),(0,r.jsxs)("mesh",{position:[.05,1.65,.12],castShadow:!0,children:[(0,r.jsx)("sphereGeometry",{args:[.02,16,16]}),(0,r.jsx)("meshStandardMaterial",{color:"#000000"})]}),(0,r.jsxs)("mesh",{position:[0,1.55,.12],castShadow:!0,children:[(0,r.jsx)("sphereGeometry",{args:[.015,16,16]}),(0,r.jsx)("meshStandardMaterial",{color:"#8B0000"})]})]})}function Z(e){let{url:s,...a}=e,i=(0,t.useRef)(null),{scene:n,animations:l}=(0,F.p)(s),{actions:o,mixer:c}=(0,V.f)(l,i),d=L(),h=G(e=>e.currentVisemes),u=E(),m=(0,t.useMemo)(()=>n.clone(),[n]);return(0,t.useEffect)(()=>{u&&m&&m.traverse(e=>{e instanceof I.eaF&&(e.name.includes("hair")?e.material=new I._4j({color:u.appearance.hairColor}):e.name.includes("skin")?e.material=new I._4j({color:u.appearance.skinTone}):e.name.includes("clothing")&&(e.material=new I._4j({color:u.appearance.clothing})))})},[u,m]),(0,D.D)((e,s)=>{if(c&&c.update(s),d&&h.length>0&&m){let s=1e3*e.clock.getElapsedTime(),a=h.find((e,a)=>{let r=h[a+1];return s>=e.time&&(!r||s<r.time)});a&&m.traverse(e=>{if(e instanceof I.I46&&e.morphTargetInfluences){Object.values(U).forEach(s=>{var a;let r=null==(a=e.morphTargetDictionary)?void 0:a[s];void 0!==r&&e.morphTargetInfluences&&(e.morphTargetInfluences[r]=0)});let r=U[a.viseme];if(r){var s;let t=null==(s=e.morphTargetDictionary)?void 0:s[r];void 0!==t&&e.morphTargetInfluences&&(e.morphTargetInfluences[t]=a.value)}}})}}),(0,t.useEffect)(()=>(o.idle&&o.idle.play(),()=>{o.idle&&o.idle.stop()}),[o]),(0,r.jsx)("group",{ref:i,...a,children:(0,r.jsx)("primitive",{object:m})})}function q(e){let s=(0,t.useRef)(null),a=E(),i=L(),{setModelLoaded:n}=J();(0,D.D)(e=>{if(s.current&&!i){let a=1+.02*Math.sin(2*e.clock.elapsedTime);s.current.scale.y=a}}),(0,D.D)(e=>{s.current&&(s.current.rotation.z=.05*Math.sin(.5*e.clock.elapsedTime))}),(0,t.useEffect)(()=>{let e=setTimeout(()=>{n(!0)},1e3);return()=>clearTimeout(e)},[n]);let l=null==a?void 0:a.modelUrl;return(0,r.jsx)("group",{ref:s,...e,children:l?(0,r.jsx)(Z,{url:l}):(0,r.jsx)(W,{})})}var O=a(8842);function $(e){let{text:s,position:a,width:i=3,height:n=2}=e,l=(0,t.useRef)(null);return s?(0,r.jsx)(M.E,{position:a,transform:!0,occlude:!0,style:{width:"".concat(100*i,"px"),height:"".concat(100*n,"px"),pointerEvents:"none"},children:(0,r.jsx)("div",{ref:l,className:"w-full h-full p-4 text-white font-mono text-sm leading-relaxed overflow-hidden",style:{background:"rgba(0, 0, 0, 0.1)",backdropFilter:"blur(1px)"},children:(0,r.jsx)("div",{className:"space-y-2",children:s.split("\n").map((e,s)=>(0,r.jsx)("div",{className:"flex flex-wrap",children:e.includes("[")&&e.includes("]")?e.split(/(\[[^\]]+\])/).map((e,s)=>{if(e.startsWith("[")&&e.endsWith("]")){let[a,t]=e.slice(1,-1).split("|");return(0,r.jsxs)("span",{className:"inline-block mx-1",children:[(0,r.jsx)("span",{className:"block text-xs text-center text-blue-300",children:t}),(0,r.jsx)("span",{className:"block text-lg font-bold",children:a})]},s)}return(0,r.jsx)("span",{children:e},s)}):e},s))})})}):null}function Q(e){let{position:s}=e,a=(0,t.useRef)(null),i=(0,t.useMemo)(()=>{let e=new Float32Array(150);for(let s=0;s<50;s++)e[3*s]=(Math.random()-.5)*2,e[3*s+1]=(Math.random()-.5)*2,e[3*s+2]=(Math.random()-.5)*.1;return e},[]);return(0,D.D)(e=>{if(a.current){a.current.rotation.y=.1*e.clock.elapsedTime;let s=a.current.geometry.attributes.position.array;for(let a=0;a<50;a++)s[3*a+1]+=.001*Math.sin(e.clock.elapsedTime+a);a.current.geometry.attributes.position.needsUpdate=!0}}),(0,r.jsxs)("points",{ref:a,position:s,children:[(0,r.jsx)("bufferGeometry",{children:(0,r.jsx)("bufferAttribute",{attach:"attributes-position",count:50,array:i,itemSize:3,args:[i,3]})}),(0,r.jsx)("pointsMaterial",{size:.02,color:"#ffffff",transparent:!0,opacity:.6,sizeAttenuation:!0})]})}function X(e){let{position:s=[0,0,0],rotation:a=[0,0,0],scale:i=1,text:n="",width:l=3,height:o=2}=e,c=(0,t.useRef)(null),d=(0,t.useRef)(null);return(0,D.D)(e=>{d.current&&(d.current.position.y=s[1]+.01*Math.sin(.5*e.clock.elapsedTime))}),(0,r.jsxs)("group",{ref:d,position:s,rotation:a,scale:i,children:[(0,r.jsxs)("mesh",{position:[0,0,-.05],castShadow:!0,children:[(0,r.jsx)("boxGeometry",{args:[l+.2,o+.2,.1]}),(0,r.jsx)("meshStandardMaterial",{color:"#8B4513"})]}),(0,r.jsxs)("mesh",{ref:c,castShadow:!0,receiveShadow:!0,children:[(0,r.jsx)("planeGeometry",{args:[l,o]}),(0,r.jsx)("meshStandardMaterial",{color:"#1a1a1a",roughness:.8,metalness:.1})]}),(0,r.jsxs)("mesh",{position:[0,-o/2-.05,.05],castShadow:!0,children:[(0,r.jsx)("boxGeometry",{args:[.8*l,.05,.1]}),(0,r.jsx)("meshStandardMaterial",{color:"#8B4513"})]}),[0,.3,-.3].map((e,s)=>(0,r.jsxs)("mesh",{position:[e,-o/2-.02,.1],rotation:[0,0,Math.random()*Math.PI],castShadow:!0,children:[(0,r.jsx)("cylinderGeometry",{args:[.01,.01,.1]}),(0,r.jsx)("meshStandardMaterial",{color:"#ffffff"})]},s)),n&&(0,r.jsx)(O.E,{position:[0,0,.01],fontSize:.15,color:"#ffffff",anchorX:"center",anchorY:"middle",maxWidth:.9*l,textAlign:"center",font:"/fonts/chalk-font.woff",outlineWidth:.002,outlineColor:"#cccccc",children:n}),n&&(0,r.jsx)($,{text:n,position:[0,0,.02],width:l,height:o}),(0,r.jsx)(Q,{position:[0,0,.1]}),(0,r.jsxs)("mesh",{position:[l/2-.2,o/2-.1,.05],castShadow:!0,children:[(0,r.jsx)("boxGeometry",{args:[.15,.05,.03]}),(0,r.jsx)("meshStandardMaterial",{color:"#654321"})]}),(0,r.jsxs)("mesh",{position:[l/2-.2,o/2-.1,.065],castShadow:!0,children:[(0,r.jsx)("boxGeometry",{args:[.14,.04,.01]}),(0,r.jsx)("meshStandardMaterial",{color:"#333333"})]})]})}function Y(){let{progress:e}=(0,A.p)();return(0,r.jsx)(M.E,{center:!0,children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg font-medium text-gray-900",children:"Loading AI Teacher"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[Math.round(e),"% complete"]})]})]})})}function K(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("ambientLight",{intensity:.4}),(0,r.jsx)("directionalLight",{position:[5,5,5],intensity:1,castShadow:!0,"shadow-mapSize-width":2048,"shadow-mapSize-height":2048,"shadow-camera-far":50,"shadow-camera-left":-10,"shadow-camera-right":10,"shadow-camera-top":10,"shadow-camera-bottom":-10}),(0,r.jsx)("pointLight",{position:[-5,5,5],intensity:.5}),(0,r.jsx)(T.OH,{preset:"studio"}),(0,r.jsxs)("mesh",{rotation:[-Math.PI/2,0,0],position:[0,-2,0],receiveShadow:!0,children:[(0,r.jsx)("planeGeometry",{args:[20,20]}),(0,r.jsx)("meshStandardMaterial",{color:"#f0f0f0"})]}),(0,r.jsx)(R._,{position:[0,-1.99,0],opacity:.4,scale:10,blur:2,far:4})]})}function ee(){return(0,r.jsx)(B.u,{makeDefault:!0,position:[0,0,5],fov:50,near:.1,far:1e3})}function es(e){let{enableControls:s=!0,showBlackboard:a=!0,className:i="w-full h-full"}=e,n=G(e=>e.blackboardText);return(0,r.jsx)("div",{className:i,children:(0,r.jsx)(C.Hl,{shadows:!0,camera:{position:[0,0,5],fov:50},gl:{antialias:!0,alpha:!0,powerPreference:"high-performance"},children:(0,r.jsxs)(t.Suspense,{fallback:(0,r.jsx)(Y,{}),children:[(0,r.jsx)(ee,{}),s&&(0,r.jsx)(P.N,{enablePan:!0,enableZoom:!0,enableRotate:!0,minDistance:2,maxDistance:10,minPolarAngle:Math.PI/6,maxPolarAngle:Math.PI/2,target:[0,0,0]}),(0,r.jsx)(K,{}),(0,r.jsx)(q,{position:[0,-2,0]}),a&&(0,r.jsx)(X,{position:[0,1,-3],text:n}),(0,r.jsxs)("group",{position:[3,-1,-2],children:[(0,r.jsxs)("mesh",{castShadow:!0,children:[(0,r.jsx)("boxGeometry",{args:[2,.1,1]}),(0,r.jsx)("meshStandardMaterial",{color:"#8B4513"})]}),[[-.8,-.5,-.4],[.8,-.5,-.4],[-.8,-.5,.4],[.8,-.5,.4]].map((e,s)=>(0,r.jsxs)("mesh",{position:e,castShadow:!0,children:[(0,r.jsx)("cylinderGeometry",{args:[.05,.05,1]}),(0,r.jsx)("meshStandardMaterial",{color:"#654321"})]},s))]}),(0,r.jsxs)("group",{position:[-3,0,-2],children:[(0,r.jsxs)("mesh",{castShadow:!0,children:[(0,r.jsx)("boxGeometry",{args:[1,3,.3]}),(0,r.jsx)("meshStandardMaterial",{color:"#8B4513"})]}),Array.from({length:8},(e,s)=>(0,r.jsxs)("mesh",{position:[-.3+s%4*.2,-1+.5*Math.floor(s/4),.2],castShadow:!0,children:[(0,r.jsx)("boxGeometry",{args:[.15,.4,.05]}),(0,r.jsx)("meshStandardMaterial",{color:"hsl(".concat(45*s,", 70%, 50%)")})]},s))]})]})})})}function ea(){let e=(0,i.useRouter)(),s=E(),{updateAvatarAppearance:a,updateVoiceSettings:c,updatePersonality:d}=(()=>{let{avatar:e,setAvatar:s}=G();return{updateAvatarAppearance:a=>{e&&s({...e,appearance:{...e.appearance,...a},updatedAt:new Date})},updateVoiceSettings:a=>{e&&s({...e,voiceSettings:{...e.voiceSettings,...a},updatedAt:new Date})},updatePersonality:a=>{e&&s({...e,personality:{...e.personality,...a},updatedAt:new Date})}}})(),{setAvatar:m,setBlackboardText:C}=J(),[A,M]=(0,t.useState)(!1),[T,R]=(0,t.useState)("Hello! I am your AI teacher. How can I help you today?"),B=s||{...H,id:"temp",teacherId:"current-teacher",createdAt:new Date,updatedAt:new Date},P=async()=>{M(!0);try{console.log("Saving avatar configuration:",B),await new Promise(e=>setTimeout(e,1e3)),alert("Avatar configuration saved successfully!")}catch(e){console.error("Failed to save avatar:",e),alert("Failed to save avatar configuration. Please try again.")}finally{M(!1)}},D=()=>{console.log("Playing voice preview with settings:",B.voiceSettings),C(T)};return(0,r.jsxs)("div",{className:"h-[calc(100vh-4rem)] flex",children:[(0,r.jsxs)("div",{className:"w-1/2 bg-gray-100 relative",children:[(0,r.jsx)("div",{className:"absolute top-4 left-4 z-10",children:(0,r.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>e.push("/teacher"),className:"bg-white/90 backdrop-blur-sm",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})}),(0,r.jsx)("div",{className:"absolute top-4 right-4 z-10",children:(0,r.jsx)(v.E,{variant:"secondary",className:"bg-white/90 backdrop-blur-sm",children:"3D Preview"})}),(0,r.jsx)(es,{enableControls:!0,showBlackboard:!0,className:"w-full h-full"}),(0,r.jsx)("div",{className:"absolute bottom-4 left-4 right-4 z-10",children:(0,r.jsx)(n.Zp,{className:"bg-white/90 backdrop-blur-sm",children:(0,r.jsx)(n.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(l.$,{onClick:D,size:"sm",className:"flex-shrink-0",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Preview Voice"]}),(0,r.jsx)("input",{type:"text",value:T,onChange:e=>R(e.target.value),placeholder:"Enter text to preview...",className:"flex-1 px-3 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"})]})})})})]}),(0,r.jsx)("div",{className:"w-1/2 overflow-y-auto bg-white",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold flex items-center space-x-2",children:[(0,r.jsx)(y.A,{className:"h-6 w-6 text-blue-600"}),(0,r.jsx)("span",{children:"AI Avatar Setup"})]}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Customize your AI teaching assistant's appearance and personality"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(l.$,{variant:"outline",onClick:()=>{m({...H,id:B.id,teacherId:B.teacherId,createdAt:B.createdAt,updatedAt:new Date})},size:"sm",children:"Reset to Default"}),(0,r.jsx)(l.$,{onClick:P,disabled:A,size:"sm",children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Save Avatar"]})})]})]}),(0,r.jsxs)(x,{defaultValue:"appearance",className:"space-y-6",children:[(0,r.jsxs)(p,{className:"grid w-full grid-cols-3",children:[(0,r.jsxs)(f,{value:"appearance",className:"flex items-center space-x-2",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Appearance"})]}),(0,r.jsxs)(f,{value:"voice",className:"flex items-center space-x-2",children:[(0,r.jsx)(S.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Voice"})]}),(0,r.jsxs)(f,{value:"personality",className:"flex items-center space-x-2",children:[(0,r.jsx)(k.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Personality"})]})]}),(0,r.jsx)(j,{value:"appearance",className:"space-y-6",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Physical Appearance"}),(0,r.jsx)(n.BT,{children:"Customize the visual appearance of your AI avatar"})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"skinTone",children:"Skin Tone"}),(0,r.jsxs)(u.l6,{value:B.appearance.skinTone,onValueChange:e=>a({skinTone:e}),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:"light",children:"Light"}),(0,r.jsx)(u.eb,{value:"medium",children:"Medium"}),(0,r.jsx)(u.eb,{value:"dark",children:"Dark"}),(0,r.jsx)(u.eb,{value:"olive",children:"Olive"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"hairColor",children:"Hair Color"}),(0,r.jsxs)(u.l6,{value:B.appearance.hairColor,onValueChange:e=>a({hairColor:e}),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:"black",children:"Black"}),(0,r.jsx)(u.eb,{value:"brown",children:"Brown"}),(0,r.jsx)(u.eb,{value:"blonde",children:"Blonde"}),(0,r.jsx)(u.eb,{value:"red",children:"Red"}),(0,r.jsx)(u.eb,{value:"gray",children:"Gray"}),(0,r.jsx)(u.eb,{value:"white",children:"White"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"eyeColor",children:"Eye Color"}),(0,r.jsxs)(u.l6,{value:B.appearance.eyeColor,onValueChange:e=>a({eyeColor:e}),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:"brown",children:"Brown"}),(0,r.jsx)(u.eb,{value:"blue",children:"Blue"}),(0,r.jsx)(u.eb,{value:"green",children:"Green"}),(0,r.jsx)(u.eb,{value:"hazel",children:"Hazel"}),(0,r.jsx)(u.eb,{value:"gray",children:"Gray"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"clothing",children:"Clothing Style"}),(0,r.jsxs)(u.l6,{value:B.appearance.clothing,onValueChange:e=>a({clothing:e}),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:"professional",children:"Professional"}),(0,r.jsx)(u.eb,{value:"casual",children:"Casual"}),(0,r.jsx)(u.eb,{value:"formal",children:"Formal"}),(0,r.jsx)(u.eb,{value:"academic",children:"Academic"})]})]})]})]})]})}),(0,r.jsx)(j,{value:"voice",className:"space-y-6",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Voice Settings"}),(0,r.jsx)(n.BT,{children:"Adjust the voice characteristics of your AI avatar"})]}),(0,r.jsxs)(n.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(o.J,{children:["Pitch: ",B.voiceSettings.pitch.toFixed(1)]}),(0,r.jsx)(h,{value:[B.voiceSettings.pitch],onValueChange:e=>{let[s]=e;return c({pitch:s})},min:.5,max:2,step:.1,className:"mt-2"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(o.J,{children:["Speed: ",B.voiceSettings.speed.toFixed(1)]}),(0,r.jsx)(h,{value:[B.voiceSettings.speed],onValueChange:e=>{let[s]=e;return c({speed:s})},min:.5,max:2,step:.1,className:"mt-2"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(o.J,{children:["Volume: ",Math.round(100*B.voiceSettings.volume),"%"]}),(0,r.jsx)(h,{value:[B.voiceSettings.volume],onValueChange:e=>{let[s]=e;return c({volume:s})},min:.1,max:1,step:.1,className:"mt-2"})]}),(0,r.jsxs)(l.$,{onClick:D,className:"w-full",variant:"outline",children:[(0,r.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Test Voice Settings"]})]})]})}),(0,r.jsx)(j,{value:"personality",className:"space-y-6",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Personality Traits"}),(0,r.jsx)(n.BT,{children:"Define how your AI avatar interacts with students"})]}),(0,r.jsxs)(n.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"tone",children:"Communication Tone"}),(0,r.jsxs)(u.l6,{value:B.personality.tone,onValueChange:e=>d({tone:e}),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:"formal",children:"Formal"}),(0,r.jsx)(u.eb,{value:"casual",children:"Casual"}),(0,r.jsx)(u.eb,{value:"friendly",children:"Friendly"}),(0,r.jsx)(u.eb,{value:"professional",children:"Professional"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(o.J,{children:["Enthusiasm Level: ",B.personality.enthusiasm,"/10"]}),(0,r.jsx)(h,{value:[B.personality.enthusiasm],onValueChange:e=>{let[s]=e;return d({enthusiasm:s})},min:1,max:10,step:1,className:"mt-2"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(o.J,{children:["Patience Level: ",B.personality.patience,"/10"]}),(0,r.jsx)(h,{value:[B.personality.patience],onValueChange:e=>{let[s]=e;return d({patience:s})},min:1,max:10,step:1,className:"mt-2"})]})]})]})})]})]})})]})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>c});var r=a(5155),t=a(2115),i=a(968),n=a(2085),l=a(9434);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.b,{ref:s,className:(0,l.cn)(o(),a),...t})});c.displayName=i.b.displayName},6126:(e,s,a)=>{"use strict";a.d(s,{E:()=>l});var r=a(5155);a(2115);var t=a(2085),i=a(9434);let n=(0,t.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:a,...t}=e;return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:a}),s),...t})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>l});var r=a(5155),t=a(2115),i=a(9434);let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});n.displayName="Card";let l=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...t})});l.displayName="CardHeader";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});o.displayName="CardTitle";let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",a),...t})});c.displayName="CardDescription";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",a),...t})});d.displayName="CardContent",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",a),...t})}).displayName="CardFooter"},7811:(e,s,a)=>{Promise.resolve().then(a.bind(a,4718))},9409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>d,yv:()=>h});var r=a(5155),t=a(2115),i=a(4678),n=a(6474),l=a(7863),o=a(5196),c=a(9434);let d=i.bL;i.YJ;let h=i.WT,u=t.forwardRef((e,s)=>{let{className:a,children:t,...l}=e;return(0,r.jsxs)(i.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...l,children:[t,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=i.l9.displayName;let m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})});m.displayName=i.PP.displayName;let x=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=i.wn.displayName;let p=t.forwardRef((e,s)=>{let{className:a,children:t,position:n="popper",...l}=e;return(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...l,children:[(0,r.jsx)(m,{}),(0,r.jsx)(i.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(x,{})]})})});p.displayName=i.UC.displayName,t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...t})}).displayName=i.JU.displayName;let f=t.forwardRef((e,s)=>{let{className:a,children:t,...n}=e;return(0,r.jsxs)(i.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:t})]})});f.displayName=i.q7.displayName,t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,r.jsx)(i.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...t})}).displayName=i.wv.displayName},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>i});var r=a(2596),t=a(9688);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.QP)((0,r.$)(s))}}},e=>{e.O(0,[367,831,683,413,817,294,510,537,441,964,358],()=>e(e.s=7811)),_N_E=e.O()}]);