{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["// NextAuth configuration for the AI Tutor Platform\n\nimport NextAuth from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { User, UserRole } from '@/types';\n\n// TODO: Replace with actual backend authentication\nconst authenticateUser = async (email: string, password: string): Promise<User | null> => {\n  // Mock authentication - replace with actual API call\n  const mockUsers: User[] = [\n    {\n      id: '1',\n      name: 'John <PERSON>',\n      email: '<EMAIL>',\n      role: 'student',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n    {\n      id: '2',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'teacher',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n    {\n      id: '3',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'hod',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n    {\n      id: '4',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      role: 'admin',\n      orgId: 'org1',\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    },\n  ];\n\n  // Simple mock authentication\n  const user = mockUsers.find(u => u.email === email);\n  if (user && password === 'password') {\n    return user;\n  }\n\n  return null;\n};\n\nconst authOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          const user = await authenticateUser(credentials.email, credentials.password);\n          \n          if (user) {\n            return {\n              id: user.id,\n              name: user.name,\n              email: user.email,\n              role: user.role,\n              orgId: user.orgId,\n            };\n          }\n          \n          return null;\n        } catch (error) {\n          console.error('Authentication error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  callbacks: {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async jwt({ token, user }: any) {\n      if (user) {\n        // Type assertion since we know this is our User type from the credentials provider\n        const customUser = user as User;\n        token.role = customUser.role;\n        token.orgId = customUser.orgId;\n      }\n      return token;\n    },\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async session({ session, token }: any) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as UserRole;\n        session.user.orgId = token.orgId as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/sign-in',\n  },\n  session: {\n    strategy: 'jwt' as const,\n  },\n  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key',\n};\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;AAEnD;AACA;;;AAGA,mDAAmD;AACnD,MAAM,mBAAmB,OAAO,OAAe;IAC7C,qDAAqD;IACrD,MAAM,YAAoB;QACxB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YAC<PERSON>,MAAM;YACN,OAAO;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;KACD;IAED,6BAA6B;IAC7B,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAC7C,IAAI,QAAQ,aAAa,YAAY;QACnC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,WAAW;QACT,CAAA,GAAA,6LAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,iBAAiB,YAAY,KAAK,EAAE,YAAY,QAAQ;oBAE3E,IAAI,MAAM;wBACR,OAAO;4BACL,IAAI,KAAK,EAAE;4BACX,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;4BACjB,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;wBACnB;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,OAAO;gBACT;YACF;QACF;KACD;IACD,WAAW;QACT,8DAA8D;QAC9D,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAO;YAC5B,IAAI,MAAM;gBACR,mFAAmF;gBACnF,MAAM,aAAa;gBACnB,MAAM,IAAI,GAAG,WAAW,IAAI;gBAC5B,MAAM,KAAK,GAAG,WAAW,KAAK;YAChC;YACA,OAAO;QACT;QACA,8DAA8D;QAC9D,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAO;YACnC,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;YAClC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe,IAAI;AACzC;AAEA,MAAM,UAAU,CAAA,GAAA,0KAAA,CAAA,UAAQ,AAAD,EAAE", "debugId": null}}]}