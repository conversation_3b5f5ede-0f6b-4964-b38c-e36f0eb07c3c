{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/auth/AuthProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/Desktop/AI/ai/frontend/src/components/auth/AuthProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/AI/ai/frontend/src/components/auth/AuthProvider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,gRAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+T,GAC5V,6FACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/components/auth/AuthProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/Desktop/AI/ai/frontend/src/components/auth/AuthProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/AI/ai/frontend/src/components/auth/AuthProvider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,gRAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_7ddbc3fa.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_7ddbc3fa-module__nRnV_W__className\",\n  \"variable\": \"geist_7ddbc3fa-module__nRnV_W__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_7ddbc3fa.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22fonts.ts%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22fallback%22:[%22system-ui%22,%22-apple-system%22,%22BlinkMacSystemFont%22,%22Segoe%20UI%22,%22Roboto%22,%22sans-serif%22],%22adjustFontFallback%22:true,%22preload%22:true}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_8e3c2ba4.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_8e3c2ba4-module__LZ84Oq__className\",\n  \"variable\": \"geist_mono_8e3c2ba4-module__LZ84Oq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_8e3c2ba4.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22fonts.ts%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22fallback%22:[%22ui-monospace%22,%22SFMono-Regular%22,%22SF%20Mono%22,%22Consolas%22,%22Liberation%20Mono%22,%22Menlo%22,%22monospace%22],%22adjustFontFallback%22:true,%22preload%22:true}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', ui-monospace, SFMono-Regular, SF Mono, <PERSON>solas, Liberation Mono, Menlo, monospace\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/lib/fonts.ts"], "sourcesContent": ["// Font configuration with Turbopack compatibility\nimport { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from \"next/font/google\";\n\n// Configure Geist Sans with optimizations\nexport const geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n  fallback: [\n    \"system-ui\",\n    \"-apple-system\", \n    \"BlinkMacSystemFont\",\n    \"Segoe UI\",\n    \"Roboto\",\n    \"sans-serif\"\n  ],\n  adjustFontFallback: true,\n  preload: true,\n});\n\n// Configure Geist Mono with optimizations  \nexport const geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n  fallback: [\n    \"ui-monospace\",\n    \"SFMono-Regular\",\n    \"SF Mono\", \n    \"Consolas\",\n    \"Liberation Mono\",\n    \"Menlo\",\n    \"monospace\"\n  ],\n  adjustFontFallback: true,\n  preload: true,\n});\n\n// Font class names for easy usage\nexport const fontClassNames = `${geistSans.variable} ${geistMono.variable}`;\n\n// CSS variables for direct usage\nexport const fontVariables = {\n  sans: geistSans.style.fontFamily,\n  mono: geistMono.style.fontFamily,\n} as const;\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;AAuC3C,MAAM,iBAAiB,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,EAAE;AAGpE,MAAM,gBAAgB;IAC3B,MAAM,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC,UAAU;IAChC,MAAM,8IAAA,CAAA,UAAS,CAAC,KAAK,CAAC,UAAU;AAClC", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport \"./globals.css\";\nimport AuthProvider from \"@/components/auth/AuthProvider\";\nimport { fontClassNames } from \"@/lib/fonts\";\n\nexport const metadata: Metadata = {\n  title: \"AI Tutor Platform\",\n  description: \"Personalized AI-powered learning experience\",\n  icons: {\n    icon: \"/icon.svg\",\n    shortcut: \"/icon.svg\",\n    apple: \"/icon.svg\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" data-scroll-behavior=\"smooth\">\n      <body\n        className={`${fontClassNames} antialiased`}\n      >\n        <AuthProvider>\n          {children}\n        </AuthProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,iRAAC;QAAK,MAAK;QAAK,wBAAqB;kBACnC,cAAA,iRAAC;YACC,WAAW,GAAG,sKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;sBAE1C,cAAA,iRAAC,6KAAA,CAAA,UAAY;0BACV;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/AI/ai/frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,mJACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}