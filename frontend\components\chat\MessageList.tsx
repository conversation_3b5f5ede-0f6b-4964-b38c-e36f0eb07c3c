'use client';

import { useEffect, useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Volume2, 
  VolumeX, 
  Copy, 
  ThumbsUp, 
  ThumbsDown,
  RotateCcw,
  User,
  Bot,
  BookOpen
} from 'lucide-react';
import { ChatMessage } from '@/types';
import { useMessages, useIsLoading, useIsStreaming } from '@/store/useChat';
import { useStartSpeaking, useStopSpeaking, useSpeechState } from '@/store/useAITeacher';

interface MessageListProps {
  className?: string;
  showFurigana?: boolean;
  showGrammarBreakdown?: boolean;
  onRegenerateResponse?: (messageId: string) => void;
  onRateMessage?: (messageId: string, rating: 'up' | 'down') => void;
}

export function MessageList({
  className = "",
  showFurigana = true,
  showGrammarBreakdown = false,
  onRegenerateResponse,
  onRateMessage
}: MessageListProps) {
  const messages = useMessages();
  const isLoading = useIsLoading();
  const isStreaming = useIsStreaming();
  const { isSpeaking } = useSpeechState();
  
  const startSpeaking = useStartSpeaking();
  const stopSpeaking = useStopSpeaking();
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [playingMessageId, setPlayingMessageId] = useState<string | null>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);

  const handlePlayAudio = async (message: ChatMessage) => {
    if (isSpeaking && playingMessageId === message.id) {
      stopSpeaking();
      setPlayingMessageId(null);
      return;
    }

    try {
      // TODO: Replace with actual TTS API call
      // const { audioUrl, visemes } = await api.getTTS(message.id);
      
      // Mock audio for now
      const audioUrl = '/mock-audio.mp3';
      const visemes: any[] = [];
      
      setPlayingMessageId(message.id);
      startSpeaking(audioUrl, visemes);
      
      // Reset playing state when audio ends
      setTimeout(() => {
        setPlayingMessageId(null);
      }, 3000); // Mock duration
    } catch (error) {
      console.error('Failed to play audio:', error);
    }
  };

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    // TODO: Add toast notification
  };

  const formatMessageContent = (content: string, showFurigana: boolean) => {
    if (!showFurigana) return content;

    // Simple furigana formatting (in production, this would be more sophisticated)
    // Format: 漢字[かんじ] -> <ruby>漢字<rt>かんじ</rt></ruby>
    return content.replace(/([一-龯]+)\[([ひらがな\u3040-\u309F]+)\]/g, '<ruby>$1<rt>$2</rt></ruby>');
  };

  const parseGrammarBreakdown = (content: string) => {
    // Mock grammar breakdown parsing
    // In production, this would parse actual grammar data from the AI response
    const sentences = content.split(/[。！？]/);
    return sentences.filter(s => s.trim()).map((sentence, index) => ({
      id: index,
      text: sentence.trim(),
      breakdown: [
        { word: sentence.split(' ')[0] || '', type: 'noun', meaning: 'Example meaning' },
        { word: sentence.split(' ')[1] || '', type: 'particle', meaning: 'Grammar particle' },
      ]
    }));
  };

  return (
    <div className={`flex flex-col space-y-4 p-4 ${className}`}>
      {messages.map((message) => (
        <MessageBubble
          key={message.id}
          message={message}
          showFurigana={showFurigana}
          showGrammarBreakdown={showGrammarBreakdown}
          isPlaying={playingMessageId === message.id}
          onPlayAudio={() => handlePlayAudio(message)}
          onCopy={() => handleCopyMessage(message.content)}
          onRegenerate={() => onRegenerateResponse?.(message.id)}
          onRate={(rating) => onRateMessage?.(message.id, rating)}
          formatContent={formatMessageContent}
          parseGrammar={parseGrammarBreakdown}
        />
      ))}

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex justify-start">
          <Card className="max-w-xs p-4 bg-blue-50 border-blue-200">
            <div className="flex items-center space-x-2">
              <Bot className="w-5 h-5 text-blue-600" />
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Streaming indicator */}
      {isStreaming && (
        <div className="flex justify-start">
          <Card className="max-w-xs p-4 bg-green-50 border-green-200">
            <div className="flex items-center space-x-2">
              <Bot className="w-5 h-5 text-green-600" />
              <span className="text-sm text-green-700">AI is responding...</span>
              <div className="w-2 h-2 bg-green-600 rounded-full animate-pulse"></div>
            </div>
          </Card>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
}

// Individual message bubble component
interface MessageBubbleProps {
  message: ChatMessage;
  showFurigana: boolean;
  showGrammarBreakdown: boolean;
  isPlaying: boolean;
  onPlayAudio: () => void;
  onCopy: () => void;
  onRegenerate: () => void;
  onRate: (rating: 'up' | 'down') => void;
  formatContent: (content: string, showFurigana: boolean) => string;
  parseGrammar: (content: string) => any[];
}

function MessageBubble({
  message,
  showFurigana,
  showGrammarBreakdown,
  isPlaying,
  onPlayAudio,
  onCopy,
  onRegenerate,
  onRate,
  formatContent,
  parseGrammar
}: MessageBubbleProps) {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <Card className={`max-w-2xl p-4 ${
        isUser 
          ? 'bg-blue-600 text-white' 
          : 'bg-white border-gray-200'
      }`}>
        {/* Message Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            {isUser ? (
              <User className="w-4 h-4" />
            ) : (
              <Bot className="w-4 h-4 text-blue-600" />
            )}
            <span className="text-sm font-medium">
              {isUser ? 'You' : 'AI Tutor'}
            </span>
            {message.subjectId && (
              <Badge variant="secondary" className="text-xs">
                <BookOpen className="w-3 h-3 mr-1" />
                Subject
              </Badge>
            )}
          </div>
          
          <span className="text-xs opacity-70">
            {message.timestamp.toLocaleTimeString()}
          </span>
        </div>

        {/* Message Content */}
        <div className="space-y-3">
          <div 
            className="text-sm leading-relaxed"
            dangerouslySetInnerHTML={{
              __html: formatContent(message.content, showFurigana)
            }}
          />

          {/* Grammar Breakdown */}
          {showGrammarBreakdown && isAssistant && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <h4 className="text-xs font-semibold text-gray-700 mb-2">Grammar Breakdown:</h4>
              {parseGrammar(message.content).map((sentence) => (
                <div key={sentence.id} className="mb-2">
                  <div className="text-xs text-gray-600 mb-1">{sentence.text}</div>
                  <div className="flex flex-wrap gap-1">
                    {sentence.breakdown.map((word: any, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {word.word} ({word.type})
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Message Actions */}
        {isAssistant && (
          <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={onPlayAudio}
                className="h-8 px-2"
              >
                {isPlaying ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onCopy}
                className="h-8 px-2"
              >
                <Copy className="w-4 h-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onRegenerate}
                className="h-8 px-2"
              >
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>

            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRate('up')}
                className="h-8 px-2"
              >
                <ThumbsUp className="w-4 h-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRate('down')}
                className="h-8 px-2"
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
